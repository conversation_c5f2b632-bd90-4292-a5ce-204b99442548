import os
import sys
import json
import re
import logging
import pandas as pd

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AwardsExtractor')

# Print immediate feedback
print("<PERSON><PERSON><PERSON> started. Attempting to extract data from AWARDS & GRANTS PDF and save to Excel...")

class AwardsExtractor:
    """
    Class for extracting data from AWARDS & GRANTS PDF.
    """

    def __init__(self):
        """Initialize the AwardsExtractor class."""
        self.awards_data = {}
        self.sections_found = set()
        self.section_employee_counts = {}

    def extract_awards_data(self, awards_file):
        """
        Extract data from AWARDS & GRANTS PDF.

        Args:
            awards_file (str): Path to the AWARDS & GRANTS PDF file
        """
        logger.info(f"Extracting data from awards file: {awards_file}")

        # Initialize awards data dictionary
        self.awards_data = {}
        self.sections_found = set()
        self.section_employee_counts = {}

        # Check if the file exists
        if not awards_file or not os.path.exists(awards_file):
            logger.warning(f"Awards file not found or not provided: {awards_file}")
            logger.info("Using empty awards data - no mock data will be generated")
            # Return empty data structure - no mock data
            return

        try:
            # Import PyPDF2 for PDF extraction
            import PyPDF2

            # Open the PDF file
            with open(awards_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)

                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                logger.info(f"Awards PDF has {num_pages} pages")
                print(f"Awards PDF has {num_pages} pages")

                # First pass: identify all sections in the document
                print("First pass: Identifying all sections in the document...")
                self._identify_sections(pdf_reader)
                
                print(f"Found {len(self.sections_found)} sections: {', '.join(self.sections_found)}")

                # Second pass: extract employee data for each section
                print("Second pass: Extracting employee data for each section...")
                
                # Initialize variables for tracking the current section
                current_section = None

                # Process each page
                for page_num in range(num_pages):
                    # Print progress
                    if page_num % 5 == 0 or page_num == num_pages - 1:
                        print(f"Processing page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Print the first page text for debugging
                    if page_num == 0:
                        print("\nFirst page text sample:")
                        print(text[:500] + "..." if len(text) > 500 else text)
                        print("\nFirst few lines:")
                        lines = text.split('\n')
                        for i, line in enumerate(lines[:10]):
                            print(f"Line {i+1}: {line}")

                    # Split the text into lines
                    lines = text.split('\n')

                    # Process each line to identify sections
                    for line in lines:
                        # Skip header lines that contain the year
                        if "February 2025" in line or "Page" in line or "SA Period Code" in line:
                            continue
                            
                        # Look for section headers (focus areas and subtitles)
                        section_match = re.search(r'(?:SA Period Code: MAR \d{4} )?([\w\s\-\&\(\)]+(?:AWARD|GRANT|SERVICE|RECOGNITION|ACHIEVEMENT|BONUS))', line, re.IGNORECASE)
                        if section_match:
                            # Extract the section name and clean it up
                            section_name = section_match.group(1).strip()
                            # Remove any numbers from the section name
                            section_name = re.sub(r'^\d+\s+', '', section_name)
                            section_name = re.sub(r'\s+\d+$', '', section_name)
                            
                            # Check if this is a valid section
                            if section_name in self.sections_found:
                                current_section = section_name
                                logger.info(f"Found section: {current_section}")
                                print(f"Found section: {current_section}")
                                continue
                        
                        # Look for "Long Service Award" specifically
                        if "Long Service Award" in line:
                            current_section = "Long Service Award"
                            logger.info(f"Found Long Service Award section")
                            print(f"Found Long Service Award section")
                            continue

                        # Also check for lines that might be section headers without specific keywords
                        if line.isupper() and len(line.strip()) > 5 and len(line.strip()) < 50 and not re.search(r'\d', line):
                            # Check if this uppercase line matches any of our known sections
                            for section in self.sections_found:
                                if section in line:
                                    current_section = section
                                    logger.info(f"Found section from uppercase line: {current_section}")
                                    print(f"Found section from uppercase line: {current_section}")
                                    break
                            continue

                        # Look for lines with "LSTG" or similar that might indicate awards
                        lstg_match = re.search(r'\b(LSTG|AWARD|GRANT|SERVICE|RECOGNITION)\b', line, re.IGNORECASE)
                        if lstg_match and current_section is None:
                            current_section = "LONG SERVICE AWARD"
                            logger.info(f"Found LSTG indicator, using section: {current_section}")
                            print(f"Found LSTG indicator, using section: {current_section}")
                            continue

                    # After processing all lines for sections, extract employee data
                    # This is more efficient than trying to extract data line by line
                    employee_data = self._extract_employee_data_from_text(text, current_section, "award")

                    # Process the extracted employee data
                    for employee_no, amount, section in employee_data:
                        # Store the data
                        if employee_no not in self.awards_data:
                            self.awards_data[employee_no] = {
                                'EMPLOYEE_NO': employee_no,
                                'AWARDS & GRANTS': 0.0,
                                'BREAKDOWN': []
                            }

                        # Add the amount to the total
                        self.awards_data[employee_no]['AWARDS & GRANTS'] += amount

                        # Add the award type to the breakdown
                        if section and amount > 0:
                            # Check if this award type is already in the breakdown
                            if section not in self.awards_data[employee_no]['BREAKDOWN']:
                                self.awards_data[employee_no]['BREAKDOWN'].append(section)

                        logger.info(f"Found award for employee {employee_no}: {section} = {amount}")
                        print(f"Found award for employee {employee_no}: {section} = {amount}")
                        
                        # Update section employee counts
                        if section not in self.section_employee_counts:
                            self.section_employee_counts[section] = 0
                        self.section_employee_counts[section] += 1

            # Convert breakdown lists to strings
            for employee_no, data in self.awards_data.items():
                if isinstance(data['BREAKDOWN'], list):
                    data['BREAKDOWN'] = ', '.join(data['BREAKDOWN'])

            # Remove any invalid employee numbers (like years)
            invalid_keys = []
            for employee_no in self.awards_data.keys():
                # Check if the employee number is a valid format (COP followed by numbers, or SEC followed by numbers)
                if not re.match(r'^(COP\d+|SEC\d+|\d{4,})$', employee_no) or employee_no == '2025':
                    invalid_keys.append(employee_no)
            
            # Remove invalid keys
            for key in invalid_keys:
                print(f"Removing invalid employee number: {key}")
                del self.awards_data[key]

        except Exception as e:
            logger.error(f"Error extracting data from awards file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            print(f"Error extracting data: {str(e)}")
            print(traceback.format_exc())

        logger.info(f"Extracted awards data for {len(self.awards_data)} employees")
        print(f"\nExtracted awards data for {len(self.awards_data)} employees")
        
        # Print section statistics
        print("\nEmployee counts per section:")
        for section, count in self.section_employee_counts.items():
            print(f"  {section}: {count} employees")
        
        # Print the extracted data for debugging
        if self.awards_data:
            print("\nSample of extracted data:")
            for i, (employee_no, data) in enumerate(list(self.awards_data.items())[:5]):
                print(f"  Employee {employee_no}: {data}")
            
            if len(self.awards_data) > 5:
                print(f"  ... and {len(self.awards_data) - 5} more entries")
        else:
            print("No data was extracted!")

    def _identify_sections(self, pdf_reader):
        """
        Identify all sections in the document.
        
        Args:
            pdf_reader: PyPDF2 PdfReader object
        """
        # Process each page
        for page_num in range(len(pdf_reader.pages)):
            # Extract text from the page
            page = pdf_reader.pages[page_num]
            text = page.extract_text()
            
            # Split the text into lines
            lines = text.split('\n')
            
            # Process each line to identify sections
            for line in lines:
                # Look for section headers (focus areas and subtitles)
                section_match = re.search(r'(?:SA Period Code: MAR \d{4} )?([\w\s\-\&\(\)]+(?:AWARD|GRANT|SERVICE|RECOGNITION|ACHIEVEMENT|BONUS))', line, re.IGNORECASE)
                if section_match:
                    # Extract the section name and clean it up
                    section_name = section_match.group(1).strip()
                    # Remove any numbers from the section name
                    section_name = re.sub(r'^\d+\s+', '', section_name)
                    section_name = re.sub(r'\s+\d+$', '', section_name)
                    
                    # Add to sections found
                    self.sections_found.add(section_name)
                    logger.info(f"Identified section: {section_name}")
                
                # Look for "Long Service Award" specifically
                if "Long Service Award" in line:
                    self.sections_found.add("Long Service Award")
                    logger.info(f"Identified Long Service Award section")

    def _extract_employee_data_from_text(self, text, current_section, data_type="award"):
        """
        Extract employee data from text using improved patterns.

        Args:
            text (str): The text to extract data from
            current_section (str): The current section being processed
            data_type (str): The type of data being extracted ("allowance" or "award")

        Returns:
            list: List of tuples containing (employee_no, amount, section)
        """
        results = []

        # If no section is identified, return empty results
        if not current_section:
            return results

        # Split text into lines
        lines = text.split('\n')

        # Process each line
        for i, line in enumerate(lines):
            # Skip header lines that contain the year or are likely headers
            if "February 2025" in line or "Page" in line or "SA Period Code" in line or "Employee Code" in line or "Employee Name" in line:
                continue
                
            # Skip lines that are likely section totals
            if re.search(r'^\d{1,3}(?:,\d{3})*(?:\.\d+)?\s+' + re.escape(current_section), line):
                continue
                
            # Look for employee information - more comprehensive pattern
            # This pattern looks for employee IDs in various formats:
            # - COP followed by numbers (e.g., COP1234)
            # - SEC followed by numbers (e.g., SEC0019)
            employee_match = re.search(r'(COP\d{4}|SEC\d{4})\s+([\w\s\.\-\']+)', line)
            if employee_match and current_section:
                # Extract employee number
                employee_no = employee_match.group(1).strip()
                logger.info(f"Found employee in {data_type}: {employee_no} in line: {line}")

                # Split the line by spaces
                parts = line.split()
                
                # Check if we have enough parts and the employee code is at the beginning
                if len(parts) >= 4 and employee_no in parts[0]:
                    try:
                        # For the Long Service Award, the amount is the second-to-last element
                        # The format is: "COP2340 ABUBAKARI SADDIQUE AMINA E 7,028.82 3,514.41"
                        # Where 7,028.82 is the amount we want (second-to-last)
                        amount_str = parts[-2].replace(',', '')
                        amount = float(amount_str)
                        logger.info(f"Found amount for employee {employee_no}: {amount}")
                        results.append((employee_no, amount, current_section))
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Could not extract amount for {employee_no}: {str(e)}")
                        
                        # Fallback to regex pattern
                        amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                        if amount_match:
                            try:
                                # Remove commas and other non-numeric characters before converting to float
                                amount_str = amount_match.group(1).replace(',', '')
                                amount_str = re.sub(r'[^\d.]', '', amount_str)
                                amount = float(amount_str)
                                logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                                results.append((employee_no, amount, current_section))
                            except ValueError:
                                logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
                else:
                    logger.warning(f"Line format not as expected for {employee_no}: {line}")
                    
                    # Fallback to original regex pattern
                    amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                    if amount_match:
                        try:
                            # Remove commas and other non-numeric characters before converting to float
                            amount_str = amount_match.group(1).replace(',', '')
                            amount_str = re.sub(r'[^\d.]', '', amount_str)
                            amount = float(amount_str)
                            logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                            results.append((employee_no, amount, current_section))
                        except ValueError:
                            logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")

        return results
        
    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.
        
        Args:
            output_file (str): Path to the output Excel file
        """
        try:
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.awards_data.items():
                data_list.append(data)
                
            df = pd.DataFrame(data_list)
            
            # Reorder columns
            columns = ['EMPLOYEE_NO', 'AWARDS & GRANTS', 'BREAKDOWN']
            df = df[columns]
            
            # Save to Excel
            df.to_excel(output_file, index=False)
            
            print(f"Successfully saved data to Excel file: {output_file}")
            print(f"Total records saved: {len(df)}")
            
        except Exception as e:
            logger.error(f"Error saving data to Excel file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            print(f"Error saving to Excel: {str(e)}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python comprehensive_awards_extraction.py <awards_pdf_file> [output_excel_file]")
        sys.exit(1)
    
    awards_file = sys.argv[1]
    
    # Default output file is comprehensive_awards_extract.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "comprehensive_awards_extract.xlsx")
    
    # If output file is provided, use it
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print(f"File path: {awards_file}")
    print(f"File exists: {os.path.exists(awards_file)}")
    print(f"Output file: {output_file}")
    
    extractor = AwardsExtractor()
    extractor.extract_awards_data(awards_file)
    
    # Save to Excel if data was extracted
    if extractor.awards_data:
        extractor.save_to_excel(output_file)
    else:
        print("No data to save to Excel.")

if __name__ == "__main__":
    main()
