@echo off
echo Installing TEM<PERSON><PERSON> PAYROLL AUDITOR...

REM Check for admin privileges
NET SESSION >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click on the script and select "Run as administrator".
    pause
    exit /B 1
)

REM Set installation path
set "INSTALL_DIR=C:\TEMPLAR PAYROLL AUDITOR"

REM Create installation directory if it doesn't exist
if not exist "%INSTALL_DIR%" (
    echo Creating installation directory...
    mkdir "%INSTALL_DIR%"
)

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "dist\TEMPLAR PAYROLL AUDITOR-win32-x64\*" "%INSTALL_DIR%"

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('Desktop') + '\TEMPLAR PAYROLL AUDITOR.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\TEMPLAR PAYROLL AUDITOR.exe'; $Shortcut.Save()"

REM Create Start Menu shortcut
echo Creating Start Menu shortcut...
powershell -Command "$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('StartMenu') + '\Programs\TEMPLAR PAYROLL AUDITOR.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\TEMPLAR PAYROLL AUDITOR.exe'; $Shortcut.Save()"

REM Install Tesseract OCR if needed
echo Checking for Tesseract OCR...
where tesseract >nul 2>&1
if %errorlevel% neq 0 (
    echo Tesseract OCR is not installed. Installing now...
    
    REM Create Tesseract directory
    set "TESSERACT_DIR=C:\Program Files\Tesseract-OCR"
    if not exist "%TESSERACT_DIR%" (
        echo Creating Tesseract directory...
        mkdir "%TESSERACT_DIR%" 2>nul
    )
    
    REM Download Tesseract OCR installer
    echo Downloading Tesseract OCR installer...
    curl -L "https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.3.20231005.exe" -o "tesseract-installer.exe"
    
    REM Install Tesseract silently
    echo Installing Tesseract OCR...
    start /wait tesseract-installer.exe /S /D=C:\Program Files\Tesseract-OCR
    
    REM Add Tesseract to PATH
    echo Adding Tesseract to PATH...
    setx PATH "%PATH%;C:\Program Files\Tesseract-OCR" /M
    
    REM Clean up
    del tesseract-installer.exe
)

echo Installation complete!
echo TEMPLAR PAYROLL AUDITOR has been installed to %INSTALL_DIR%
echo.
echo You may need to restart your computer for all changes to take effect.
pause
