"""
This script contains the fix for the Bank Adviser's ALL ALLOWANCES extraction.
The issue is that the amount extraction in the _extract_employee_data_from_text method
is not correctly extracting the amount from the PDF text.
"""

def fix_extract_employee_data_from_text(self, text, current_section, data_type="allowance"):
    """
    Extract employee data from text using improved patterns.

    Args:
        text (str): The text to extract data from
        current_section (str): The current section being processed
        data_type (str): The type of data being extracted ("allowance" or "award")

    Returns:
        list: List of tuples containing (employee_no, amount, section)
    """
    results = []

    # Split text into lines
    lines = text.split('\n')

    # Process each line
    for i, line in enumerate(lines):
        # Look for employee information - more comprehensive pattern
        # This pattern looks for employee IDs in various formats:
        # - Alpha followed by numbers (e.g., COP1234, EMP001)
        # - Just numbers with optional prefix/suffix (e.g., 12345, #12345)
        employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
        if employee_match and current_section:
            # Extract employee number
            employee_no = employee_match.group(1).strip()
            logger.info(f"Found employee in {data_type}: {employee_no} in line: {line}")

            # For the EDUCATIONAL SUBSIDY section, we know the amount is in a specific position
            # The format is: "COP1337 AYISI DAVID 4,111.45 6,691.86"
            # Where 4,111.45 is the amount we want
            
            # Split the line by spaces
            parts = line.split()
            
            # Check if we have enough parts and the employee code is at the beginning
            if len(parts) >= 4 and employee_no in parts[0]:
                try:
                    # The amount should be the third element from the end
                    amount_str = parts[-2].replace(',', '')
                    amount = float(amount_str)
                    logger.info(f"Found amount for employee {employee_no}: {amount}")
                    results.append((employee_no, amount, current_section))
                except (ValueError, IndexError) as e:
                    logger.warning(f"Could not extract amount for {employee_no}: {str(e)}")
                    
                    # Fallback to regex pattern
                    amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                    if amount_match:
                        try:
                            # Remove commas and other non-numeric characters before converting to float
                            amount_str = amount_match.group(1).replace(',', '')
                            amount_str = re.sub(r'[^\d.]', '', amount_str)
                            amount = float(amount_str)
                            logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                            results.append((employee_no, amount, current_section))
                        except ValueError:
                            logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
            else:
                logger.warning(f"Line format not as expected for {employee_no}: {line}")
                
                # Fallback to original regex pattern
                amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                if amount_match:
                    try:
                        # Remove commas and other non-numeric characters before converting to float
                        amount_str = amount_match.group(1).replace(',', '')
                        amount_str = re.sub(r'[^\d.]', '', amount_str)
                        amount = float(amount_str)
                        logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                        results.append((employee_no, amount, current_section))
                    except ValueError:
                        logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
                else:
                    # If no amount found in the same line, look in the next line
                    if i + 1 < len(lines):
                        next_line = lines[i + 1]
                        amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', next_line)
                        if amount_match:
                            try:
                                amount_str = amount_match.group(1).replace(',', '')
                                amount_str = re.sub(r'[^\d.]', '', amount_str)
                                amount = float(amount_str)
                                logger.info(f"Found amount for employee {employee_no} in next line: {amount}")
                                results.append((employee_no, amount, current_section))
                            except ValueError:
                                logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")

    return results
