import os
import json
import pandas as pd
import re
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def extract_period_from_payslips(comparison_data):
    """Extract period (month and year) from payslip data"""
    # Look for period information in the data
    prev_period = "Previous Period"
    curr_period = "Current Period"

    # Try to find period information in the data
    for emp in comparison_data:
        # Check if period is directly available
        if "previous_period" in emp and emp["previous_period"]:
            prev_period = emp["previous_period"]
        if "current_period" in emp and emp["current_period"]:
            curr_period = emp["current_period"]

        # If we found both periods, break
        if prev_period != "Previous Period" and curr_period != "Current Period":
            break

        # Try to extract from changes text
        for change in emp.get("changes", []):
            # Look for patterns like "March 2025" or "April 2025"
            month_year_match = re.search(r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})', change)
            if month_year_match:
                # If we find a month/year in the changes, assume it's the current period
                curr_period = f"{month_year_match.group(1)} {month_year_match.group(2)}"
                # Make an educated guess for previous period (one month before)
                months = ["January", "February", "March", "April", "May", "June",
                          "July", "August", "September", "October", "November", "December"]
                curr_month_idx = months.index(month_year_match.group(1))
                prev_month_idx = (curr_month_idx - 1) % 12
                prev_year = int(month_year_match.group(2))
                if prev_month_idx == 11 and curr_month_idx == 0:  # December to January transition
                    prev_year -= 1
                prev_period = f"{months[prev_month_idx]} {prev_year}"
                break

    return prev_period, curr_period

def generate_improved_reports(comparison_data, output_dir):
    """
    Generate improved reports in various formats (Excel, CSV, JSON, Word, PDF).

    This function creates detailed reports with specific worksheets and formats
    as requested by the user.

    Args:
        comparison_data: List of employee comparison data dictionaries
        output_dir: Directory to save the reports

    Returns:
        Dictionary with paths to the generated reports
    """
    print("Generating improved reports...")

    # Create timestamp for unique filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Extract period information from the data
    prev_period, curr_period = extract_period_from_payslips(comparison_data)

    # Create Excel report
    excel_path = os.path.join(output_dir, f"payroll_audit_{timestamp}.xlsx")
    wb = Workbook()

    # Create the required worksheets
    ws_summary = wb.active
    ws_summary.title = "SUMMARY"
    ws_changes = wb.create_sheet("EMPLOYEES WITH CHANGES")
    ws_no_changes = wb.create_sheet("EMPLOYEES NO CHANGES")
    ws_new = wb.create_sheet("NEW EMPLOYEES")
    ws_removed = wb.create_sheet("REMOVED EMPLOYEES")

    # Define common styles
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    header_font = Font(bold=True)
    header_border = Border(bottom=Side(style='thin'))

    # Define common headers with period information
    headers = [
        "EMPLOYEE NO.",
        "DEPARTMENT",
        f"GROSS {prev_period}",
        f"GROSS {curr_period}",
        f"NET {prev_period}",
        f"NET {curr_period}",
        "CHANGE (increase or decrease)",
        "ITEMIZED CHANGE"
    ]

    # Apply headers to all worksheets
    all_worksheets = [ws_summary, ws_changes, ws_no_changes, ws_new, ws_removed]
    for ws in all_worksheets:
        # Add title
        ws.cell(row=1, column=1, value=f"Payroll Audit Report - {ws.title}")
        ws.cell(row=1, column=1).font = Font(bold=True, size=14)
        ws.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Add headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = header_border

    # Add statistics to summary sheet
    ws_summary.cell(row=6, column=1, value="Statistics:").font = Font(bold=True)
    ws_summary.cell(row=7, column=1, value="Total employees analyzed:")
    ws_summary.cell(row=7, column=2, value=len(comparison_data))

    # Count employees with changes
    employees_with_changes = sum(1 for emp in comparison_data if any(change != "No significant changes detected" for change in emp["changes"]))
    ws_summary.cell(row=8, column=1, value="Employees with changes:")
    ws_summary.cell(row=8, column=2, value=employees_with_changes)

    # Count new employees
    new_employees = sum(1 for emp in comparison_data if "New employee" in str(emp["changes"][0]))
    ws_summary.cell(row=9, column=1, value="New employees:")
    ws_summary.cell(row=9, column=2, value=new_employees)

    # Count removed employees
    removed_employees = sum(1 for emp in comparison_data if "Employee removed" in str(emp["changes"][0]))
    ws_summary.cell(row=10, column=1, value="Removed employees:")
    ws_summary.cell(row=10, column=2, value=removed_employees)

    # Categorize employees
    employees_with_significant_changes = []
    employees_without_changes = []
    new_employees_list = []
    removed_employees_list = []

    for emp in comparison_data:
        # Check if employee is new or removed
        is_new = any("New employee" in str(change) for change in emp["changes"])
        is_removed = any("Employee removed" in str(change) for change in emp["changes"])

        if is_new:
            new_employees_list.append(emp)
        elif is_removed:
            removed_employees_list.append(emp)
        elif any(change != "No significant changes detected" for change in emp["changes"]):
            employees_with_significant_changes.append(emp)
        else:
            employees_without_changes.append(emp)

    # Function to add employee data to a worksheet
    def add_employee_data(worksheet, employees, start_row=5):
        for idx, emp in enumerate(employees, start_row):
            # Basic employee info
            worksheet.cell(row=idx, column=1, value=emp["id"])
            worksheet.cell(row=idx, column=2, value=emp.get("department", "Unknown"))

            # Gross salary data
            prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
            curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))
            worksheet.cell(row=idx, column=3, value=prev_gross)
            worksheet.cell(row=idx, column=4, value=curr_gross)

            # Net pay data
            prev_net = emp.get("previous_net_pay", "N/A")
            curr_net = emp.get("current_net_pay", "N/A")
            worksheet.cell(row=idx, column=5, value=prev_net)
            worksheet.cell(row=idx, column=6, value=curr_net)

            # Calculate change
            change_text = "N/A"
            if prev_net != "N/A" and curr_net != "N/A" and not is_new and not is_removed:
                try:
                    prev_val = float(''.join(c for c in str(prev_net) if c.isdigit() or c == '.'))
                    curr_val = float(''.join(c for c in str(curr_net) if c.isdigit() or c == '.'))
                    if prev_val > 0:
                        diff = curr_val - prev_val
                        if diff > 0:
                            change_text = f"Increase: {diff:.2f}"
                        else:
                            change_text = f"Decrease: {abs(diff):.2f}"
                except (ValueError, TypeError):
                    pass
            elif is_new:
                change_text = "New Employee"
            elif is_removed:
                change_text = "Employee Removed"

            worksheet.cell(row=idx, column=7, value=change_text)

            # Itemized changes - simplified to not repeat gross/net salary values
            significant_changes = [change for change in emp["changes"] if change != "No significant changes detected"]

            # Filter out changes that are already shown in the gross/net columns
            filtered_changes = []
            for change in significant_changes:
                # Skip changes that mention gross salary or net pay as they're already in columns
                if "Gross Salary" not in change and "Net Pay" not in change:
                    filtered_changes.append(change)

            if filtered_changes:
                changes_text = "\n".join(filtered_changes)
            else:
                changes_text = "No changes"
            worksheet.cell(row=idx, column=8, value=changes_text)
            worksheet.cell(row=idx, column=8).alignment = Alignment(wrapText=True)

    # Add data to each worksheet
    add_employee_data(ws_summary, comparison_data, start_row=12)  # Start after statistics
    add_employee_data(ws_changes, employees_with_significant_changes)
    add_employee_data(ws_no_changes, employees_without_changes)
    add_employee_data(ws_new, new_employees_list)
    add_employee_data(ws_removed, removed_employees_list)

    # Format all worksheets
    for ws in all_worksheets:
        # Adjust column widths
        for col in range(1, len(headers) + 1):
            max_length = 0
            for row in range(4, ws.max_row + 1):  # Start from header row
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value).split('\n')[0]))  # Take first line for width calculation
            adjusted_width = min(max_length + 2, 50)  # Cap width at 50 characters
            ws.column_dimensions[chr(64 + col)].width = adjusted_width

    # Save Excel file
    try:
        wb.save(excel_path)
        print(f"Excel report saved to: {excel_path}")
    except Exception as e:
        print(f"Error saving Excel file: {e}")
        excel_path = None

    # Create CSV report
    csv_path = os.path.join(output_dir, f"payroll_audit_{timestamp}.csv")

    # Prepare data for CSV with the requested column headings
    csv_data = []
    for emp in comparison_data:
        # Get gross salary data
        prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
        curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))

        # Get net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")

        # Calculate change
        change_text = "N/A"
        is_new = any("New employee" in str(change) for change in emp.get("changes", []))
        is_removed = any("Employee removed" in str(change) for change in emp.get("changes", []))

        if prev_net != "N/A" and curr_net != "N/A" and not is_new and not is_removed:
            try:
                prev_val = float(''.join(c for c in str(prev_net) if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in str(curr_net) if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
            except (ValueError, TypeError):
                pass
        elif is_new:
            change_text = "New Employee"
        elif is_removed:
            change_text = "Employee Removed"

        # Get itemized changes - simplified to not repeat gross/net salary values
        significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

        # Filter out changes that are already shown in the gross/net columns
        filtered_changes = []
        for change in significant_changes:
            # Skip changes that mention gross salary or net pay as they're already in columns
            if "Gross Salary" not in change and "Net Pay" not in change:
                filtered_changes.append(change)

        itemized_changes = "; ".join(filtered_changes) if filtered_changes else "No changes"

        emp_copy = {
            "EMPLOYEE NO.": emp["id"],
            "DEPARTMENT": emp.get("department", "Unknown"),
            f"GROSS {prev_period}": prev_gross,
            f"GROSS {curr_period}": curr_gross,
            f"NET {prev_period}": prev_net,
            f"NET {curr_period}": curr_net,
            "CHANGE (increase or decrease)": change_text,
            "ITEMIZED CHANGE": itemized_changes
        }
        csv_data.append(emp_copy)

    try:
        df = pd.DataFrame(csv_data)
        df.to_csv(csv_path, index=False)
        print(f"CSV report saved to: {csv_path}")
    except Exception as e:
        print(f"Error saving CSV file: {e}")
        csv_path = None

    # Create JSON report
    json_path = os.path.join(output_dir, f"payroll_audit_{timestamp}.json")

    # Prepare data for JSON with the requested format
    json_data = []
    for emp in comparison_data:
        # Get gross salary data
        prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
        curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))

        # Get net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")

        # Calculate change
        change_text = "N/A"
        is_new = any("New employee" in str(change) for change in emp.get("changes", []))
        is_removed = any("Employee removed" in str(change) for change in emp.get("changes", []))

        if prev_net != "N/A" and curr_net != "N/A" and not is_new and not is_removed:
            try:
                prev_val = float(''.join(c for c in str(prev_net) if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in str(curr_net) if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
            except (ValueError, TypeError):
                pass
        elif is_new:
            change_text = "New Employee"
        elif is_removed:
            change_text = "Employee Removed"

        # Get itemized changes - simplified to not repeat gross/net salary values
        significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

        # Filter out changes that are already shown in the gross/net columns
        filtered_changes = []
        for change in significant_changes:
            # Skip changes that mention gross salary or net pay as they're already in columns
            if "Gross Salary" not in change and "Net Pay" not in change:
                filtered_changes.append(change)

        json_emp = {
            "EMPLOYEE NO.": emp["id"],
            "DEPARTMENT": emp.get("department", "Unknown"),
            f"GROSS {prev_period}": prev_gross,
            f"GROSS {curr_period}": curr_gross,
            f"NET {prev_period}": prev_net,
            f"NET {curr_period}": curr_net,
            "CHANGE (increase or decrease)": change_text,
            "ITEMIZED CHANGE": filtered_changes
        }
        json_data.append(json_emp)

    try:
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        print(f"JSON report saved to: {json_path}")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        json_path = None

    # Create Word report
    word_path = os.path.join(output_dir, f"payroll_audit_{timestamp}.docx")

    try:
        # Create a new Word document
        doc = Document()

        # Set document properties
        doc.add_heading('Payroll Audit Report', 0)
        doc.add_paragraph(f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        doc.add_paragraph(f'Period: {curr_period} VS {prev_period}')

        # Add statistics section
        doc.add_heading('Statistics', level=1)
        stats_table = doc.add_table(rows=4, cols=2)
        stats_table.style = 'Table Grid'

        # Add statistics data
        stats_rows = [
            ("Total employees analyzed:", str(len(comparison_data))),
            ("Employees with changes:", str(employees_with_changes)),
            ("New employees:", str(new_employees)),
            ("Removed employees:", str(removed_employees))
        ]

        for i, (label, value) in enumerate(stats_rows):
            stats_table.cell(i, 0).text = label
            stats_table.cell(i, 1).text = value

        # Group employees by department for the Word report
        dept_employees = {}
        for emp in comparison_data:
            dept = emp.get("department", "Unknown")
            if dept not in dept_employees:
                dept_employees[dept] = []
            dept_employees[dept].append(emp)

        # Add itemized changes section
        doc.add_heading('Itemized Changes', level=1)

        # Process each department
        for dept, emps in dept_employees.items():
            # Skip departments with no changes
            if all(all(change == "No significant changes detected" for change in emp.get("changes", [])) for emp in emps):
                continue

            doc.add_heading(f'Department: {dept}', level=2)

            # Process each employee with changes
            for emp in emps:
                # Get significant changes
                significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

                # Filter out changes that are already shown in the gross/net columns
                filtered_changes = []
                for change in significant_changes:
                    # Skip changes that mention gross salary or net pay as they're already in columns
                    if "Gross Salary" not in change and "Net Pay" not in change:
                        filtered_changes.append(change)

                significant_changes = filtered_changes

                if significant_changes:
                    # Get employee ID and department
                    emp_id = emp['id']
                    dept = emp.get("department", "")
                    name = emp.get("name", "")

                    # Format the employee header with emp_id and dept
                    # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                    # Department and Section are distinct entities and must not be substituted
                    print(f"DEBUG - Formatting employee header with Department: '{dept}'")

                    # CRITICAL: Department must always be included, never use Section as fallback
                    if dept:
                        emp_header = f"{emp_id}: {dept}"
                        print(f"DEBUG - Successfully included Department in header: {dept}")
                    else:
                        # If department is missing, log a warning but don't substitute with section
                        emp_header = f"{emp_id}"
                        print(f"WARNING - Department missing for employee {emp_id} - this should not happen")

                    # Only add the name if it's valid and not the same as section
                    if name and name != "Unknown" and name.strip():
                        # Clean up the name - remove any trailing punctuation or extra spaces
                        name = name.strip().rstrip('.:,;')

                        # Minimal validation - just check for exact matches with organizational terms
                        org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                        # Only exclude the name if it's exactly one of these terms
                        if not any(name == term for term in org_terms):
                            # Add the name after the department
                            emp_header = f"{emp_header} - {name}"
                            print(f"DEBUG - Added name to header: {name}")

                    # Print the final header for debugging
                    print(f"DEBUG - Final employee header: {emp_header}")

                    doc.add_paragraph(emp_header, style='List Bullet')

                    # Add each change as a numbered list item
                    for i, change in enumerate(significant_changes, 1):
                        p = doc.add_paragraph(f"{i}. {change}")
                        p.paragraph_format.left_indent = Inches(0.5)

        # Add new employees section if any
        if new_employees_list:
            doc.add_heading('New Employees', level=1)
            for emp in new_employees_list:
                # Get employee ID and department
                emp_id = emp['id']
                dept = emp.get("department", "")
                name = emp.get("name", "")

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting new employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in new employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for new employee {emp_id} - this should not happen")

                # Only add the name if it's valid
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final new employee header: {emp_header}")

                doc.add_paragraph(emp_header, style='List Bullet')

        # Add removed employees section if any
        if removed_employees_list:
            doc.add_heading('Removed Employees', level=1)
            for emp in removed_employees_list:
                # Get employee ID and department
                emp_id = emp['id']
                dept = emp.get("department", "")
                name = emp.get("name", "")

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting removed employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in removed employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for removed employee {emp_id} - this should not happen")

                # Only add the name if it's valid
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final removed employee header: {emp_header}")

                doc.add_paragraph(emp_header, style='List Bullet')

        # Save the Word document
        doc.save(word_path)
        print(f"Word: {word_path}")
    except Exception as e:
        print(f"Error creating Word report: {e}")
        word_path = None

    # Create PDF report (similar structure to Word report)
    pdf_path = os.path.join(output_dir, f"payroll_audit_{timestamp}.pdf")

    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors

        # Create PDF document
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        styles = getSampleStyleSheet()

        # Create custom styles
        title_style = styles['Title']
        heading1_style = styles['Heading1']
        heading2_style = styles['Heading2']
        normal_style = styles['Normal']

        # List item style
        list_style = ParagraphStyle(
            'ListItem',
            parent=styles['Normal'],
            leftIndent=20,
            firstLineIndent=0,
            spaceBefore=2,
            spaceAfter=2
        )

        # Create content elements
        elements = []

        # Add title and date
        elements.append(Paragraph('Payroll Audit Report', title_style))
        elements.append(Paragraph(f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', normal_style))
        elements.append(Paragraph(f'Period: {curr_period} VS {prev_period}', normal_style))
        elements.append(Spacer(1, 12))

        # Add statistics section
        elements.append(Paragraph('Statistics', heading1_style))
        stats_data = [
            ["Total employees analyzed:", str(len(comparison_data))],
            ["Employees with changes:", str(employees_with_changes)],
            ["New employees:", str(new_employees)],
            ["Removed employees:", str(removed_employees)]
        ]

        stats_table = Table(stats_data, colWidths=[200, 100])
        stats_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        elements.append(stats_table)
        elements.append(Spacer(1, 12))

        # Add itemized changes section
        elements.append(Paragraph('Itemized Changes', heading1_style))

        # Process each department
        for dept, emps in dept_employees.items():
            # Skip departments with no changes
            if all(all(change == "No significant changes detected" for change in emp.get("changes", [])) for emp in emps):
                continue

            elements.append(Paragraph(f'Department: {dept}', heading2_style))

            # Process each employee with changes
            for emp in emps:
                # Get significant changes
                significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

                # Filter out changes that are already shown in the gross/net columns
                filtered_changes = []
                for change in significant_changes:
                    # Skip changes that mention gross salary or net pay as they're already in columns
                    if "Gross Salary" not in change and "Net Pay" not in change:
                        filtered_changes.append(change)

                significant_changes = filtered_changes

                if significant_changes:
                    # Get employee ID and department
                    emp_id = emp['id']
                    dept = emp.get("department", "")
                    name = emp.get("name", "")

                    # Format the employee header with emp_id and dept
                    # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                    # Department and Section are distinct entities and must not be substituted
                    print(f"DEBUG - Formatting PDF employee header with Department: '{dept}'")

                    # CRITICAL: Department must always be included, never use Section as fallback
                    if dept:
                        emp_header = f"{emp_id}: {dept}"
                        print(f"DEBUG - Successfully included Department in PDF employee header: {dept}")
                    else:
                        # If department is missing, log a warning but don't substitute with section
                        emp_header = f"{emp_id}"
                        print(f"WARNING - Department missing for PDF employee {emp_id} - this should not happen")

                    # Only add the name if it's valid
                    if name and name != "Unknown" and name.strip():
                        # Clean up the name - remove any trailing punctuation or extra spaces
                        name = name.strip().rstrip('.:,;')

                        # Minimal validation - just check for exact matches with organizational terms
                        org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                        # Only exclude the name if it's exactly one of these terms
                        if not any(name == term for term in org_terms):
                            # Add the name after the department
                            emp_header = f"{emp_header} - {name}"
                            print(f"DEBUG - Added name to PDF header: {name}")

                    # Print the final header for debugging
                    print(f"DEBUG - Final PDF employee header: {emp_header}")

                    elements.append(Paragraph(f"• {emp_header}", list_style))

                    # Add each change as a numbered list item
                    for i, change in enumerate(significant_changes, 1):
                        elements.append(Paragraph(f"    {i}. {change}", list_style))

            elements.append(Spacer(1, 6))

        # Add new employees section if any
        if new_employees_list:
            elements.append(Paragraph('New Employees', heading1_style))
            for emp in new_employees_list:
                # Get employee ID and department
                emp_id = emp['id']
                dept = emp.get("department", "")
                name = emp.get("name", "")

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting PDF new employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in PDF new employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for PDF new employee {emp_id} - this should not happen")

                # Only add the name if it's valid
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to PDF new employee header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final PDF new employee header: {emp_header}")

                elements.append(Paragraph(f"• {emp_header}", list_style))
            elements.append(Spacer(1, 12))

        # Add removed employees section if any
        if removed_employees_list:
            elements.append(Paragraph('Removed Employees', heading1_style))
            for emp in removed_employees_list:
                # Get employee ID and department
                emp_id = emp['id']
                dept = emp.get("department", "")
                name = emp.get("name", "")

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting PDF removed employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in PDF removed employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for PDF removed employee {emp_id} - this should not happen")

                # Only add the name if it's valid
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to PDF removed employee header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final PDF removed employee header: {emp_header}")

                elements.append(Paragraph(f"• {emp_header}", list_style))

        # Build the PDF
        doc.build(elements)
        print(f"PDF: {pdf_path}")
    except Exception as e:
        print(f"Error creating PDF report: {e}")
        pdf_path = None

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path,
        "word": word_path,
        "pdf": pdf_path
    }

    print("Report generation complete.")
    return reports
