{"name": "templar-payroll-auditor", "version": "1.0.0", "description": "Templar Payroll Auditor Desktop Application", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "package": "electron-packager . \"TEMPLAR PAYROLL AUDITOR\" --platform=win32 --arch=x64 --out=dist --icon=build/icon.ico --overwrite --asar --extra-resource=backend-dist --extra-resource=python-embedded", "create-installer": "node installer.js", "test": "echo \"Error: no test specified\" && exit 1", "dev": "electron ."}, "keywords": ["payroll", "auditor", "pdf", "extraction"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4", "electron-installer-windows": "^3.0.0", "electron-packager": "^17.1.2", "electron-winstaller": "^5.4.0", "electron-wix-msi": "^5.1.3"}, "dependencies": {"adm-zip": "^0.5.16", "react": "^18.2.0", "react-dom": "^18.2.0", "xlsx": "^0.18.5"}, "build": {"appId": "com.templar.payrollauditor", "productName": "TEMPLAR PAYROLL AUDITOR", "directories": {"output": "dist"}, "win": {"target": "nsis", "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "TEMPLAR PAYROLL AUDITOR", "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico", "installerSidebar": "build/installerSidebar.bmp", "uninstallerSidebar": "build/uninstallerSidebar.bmp"}, "artifactName": "TEMPLAR PAYROLL AUDITOR Setup ${version}.${ext}", "extraResources": [{"from": "backend-dist", "to": "backend"}, {"from": "vendor", "to": "vendor"}], "extraFiles": [{"from": "python-embedded", "to": "python-embedded"}], "asar": true, "asarUnpack": ["node_modules/adm-zip/**/*"]}}