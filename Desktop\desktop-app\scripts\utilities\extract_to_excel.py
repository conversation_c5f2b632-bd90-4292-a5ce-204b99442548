import os
import sys
import json
import re
import logging
import pandas as pd

# Configure logging to output to console
logging.basicConfig(
    level=logging.WARNING,  # Change to WARNING to reduce verbosity
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AllowanceExtractor')

# Print immediate feedback
print("Script started. Attempting to extract data from ALL ALLOWANCES PDF and save to Excel...")

class AllowanceExtractor:
    """
    Class for testing extraction of data from ALL ALLOWANCES PDF.
    """

    def __init__(self):
        """Initialize the AllowanceExtractor class."""
        self.allowances_data = {}

    def extract_allowances_data(self, allowances_file):
        """
        Extract data from ALL ALLOWANCES PDF.

        Args:
            allowances_file (str): Path to the ALL ALLOWANCES PDF file
        """
        logger.info(f"Extracting data from allowances file: {allowances_file}")

        # Initialize allowances data dictionary
        self.allowances_data = {}

        # Check if the file exists
        if not allowances_file or not os.path.exists(allowances_file):
            logger.warning(f"Allowances file not found or not provided: {allowances_file}")
            logger.info("Using empty allowances data - no mock data will be generated")
            # Return empty data structure - no mock data
            return

        try:
            # Import PyPDF2 for PDF extraction
            import PyPDF2

            # Open the PDF file
            with open(allowances_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)

                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                logger.info(f"Allowances PDF has {num_pages} pages")

                # Initialize variables for tracking the current section
                current_section = None

                # Process each page
                for page_num in range(num_pages):
                    # Print progress
                    if page_num % 5 == 0 or page_num == num_pages - 1:
                        print(f"Processing page {page_num + 1} of {num_pages}...")

                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()

                    # Split the text into lines
                    lines = text.split('\n')

                    # Process each line
                    for line in lines:
                        # Look for section headers (focus areas and subtitles)
                        # More comprehensive pattern to match various allowance types
                        section_match = re.search(r'^([\w\s\-\&\(\)]+(?:ALLOWANCE|SUBSIDY|BENEFIT|BONUS|INCENTIVE|PAYMENT|REIMBURSEMENT|STIPEND|GRANT|COMPENSATION))', line, re.IGNORECASE)
                        if section_match:
                            current_section = section_match.group(1).strip()
                            logger.info(f"Found allowance section: {current_section}")
                            continue

                        # Also check for lines that might be section headers without specific keywords
                        if line.isupper() and len(line.strip()) > 5 and len(line.strip()) < 50 and not re.search(r'\d', line):
                            current_section = line.strip()
                            logger.info(f"Found potential allowance section (uppercase line): {current_section}")
                            continue

                        # Look for lines with "LV" or similar that might indicate allowances
                        lv_match = re.search(r'\b(LV|LSTG|LIVING|ALLOWANCE)\b', line, re.IGNORECASE)
                        if lv_match and current_section is None:
                            current_section = "LV ALLOWANCE"
                            logger.info(f"Found LV indicator, using section: {current_section}")
                            continue

                        # Look for "EDUCATIONAL SUBSIDY" specifically
                        if "EDUCATIONAL SUBSIDY" in line.upper():
                            current_section = "EDUCATIONAL SUBSIDY"
                            logger.info(f"Found EDUCATIONAL SUBSIDY section")
                            continue

                    # After processing all lines for sections, extract employee data
                    # This is more efficient than trying to extract data line by line
                    employee_data = self._extract_employee_data_from_text(text, current_section, "allowance")

                    # Process the extracted employee data
                    for employee_no, amount, section in employee_data:
                        # Store the data
                        if employee_no not in self.allowances_data:
                            self.allowances_data[employee_no] = {
                                'EMPLOYEE_NO': employee_no,
                                'ALL_ALLOWANCES': 0.0,
                                'BREAKDOWN': []
                            }

                        # Add the amount to the total
                        self.allowances_data[employee_no]['ALL_ALLOWANCES'] += amount

                        # Add the allowance type to the breakdown
                        if section and amount > 0:
                            # Check if this allowance type is already in the breakdown
                            if section not in self.allowances_data[employee_no]['BREAKDOWN']:
                                self.allowances_data[employee_no]['BREAKDOWN'].append(section)

                        logger.info(f"Found allowance for employee {employee_no}: {section} = {amount}")

            # Convert breakdown lists to strings
            for employee_no, data in self.allowances_data.items():
                if isinstance(data['BREAKDOWN'], list):
                    data['BREAKDOWN'] = ', '.join(data['BREAKDOWN'])

        except Exception as e:
            logger.error(f"Error extracting data from allowances file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

        logger.info(f"Extracted allowances data for {len(self.allowances_data)} employees")

        # Print progress update
        print(f"Successfully extracted data for {len(self.allowances_data)} employees")

        # Print the extracted data for debugging
        logger.info("Extracted allowances data:")
        for employee_no, data in list(self.allowances_data.items())[:5]:  # Show first 5 entries
            logger.info(f"  Employee {employee_no}: {data}")

        if len(self.allowances_data) > 5:
            logger.info(f"  ... and {len(self.allowances_data) - 5} more entries")

    def _extract_employee_data_from_text(self, text, current_section, data_type="allowance"):
        """
        Extract employee data from text using improved patterns.

        Args:
            text (str): The text to extract data from
            current_section (str): The current section being processed
            data_type (str): The type of data being extracted ("allowance" or "award")

        Returns:
            list: List of tuples containing (employee_no, amount, section)
        """
        results = []

        # Split text into lines
        lines = text.split('\n')

        # Process each line
        for i, line in enumerate(lines):
            # Look for employee information - more comprehensive pattern
            # This pattern looks for employee IDs in various formats:
            # - Alpha followed by numbers (e.g., COP1234, EMP001)
            # - Just numbers with optional prefix/suffix (e.g., 12345, #12345)
            employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
            if employee_match and current_section:
                # Extract employee number
                employee_no = employee_match.group(1).strip()
                logger.info(f"Found employee in {data_type}: {employee_no} in line: {line}")

                # For the EDUCATIONAL SUBSIDY section, we know the amount is in a specific position
                # The format is: "COP1337 AYISI DAVID 4,111.45 6,691.86"
                # Where 4,111.45 is the amount we want

                # Split the line by spaces
                parts = line.split()

                # Check if we have enough parts and the employee code is at the beginning
                if len(parts) >= 4 and employee_no in parts[0]:
                    try:
                        # The amount should be the third element from the end
                        amount_str = parts[-2].replace(',', '')
                        amount = float(amount_str)
                        logger.info(f"Found amount for employee {employee_no}: {amount}")
                        results.append((employee_no, amount, current_section))
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Could not extract amount for {employee_no}: {str(e)}")

                        # Fallback to regex pattern
                        amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                        if amount_match:
                            try:
                                # Remove commas and other non-numeric characters before converting to float
                                amount_str = amount_match.group(1).replace(',', '')
                                amount_str = re.sub(r'[^\d.]', '', amount_str)
                                amount = float(amount_str)
                                logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                                results.append((employee_no, amount, current_section))
                            except ValueError:
                                logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
                else:
                    logger.warning(f"Line format not as expected for {employee_no}: {line}")

                    # Fallback to original regex pattern
                    amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                    if amount_match:
                        try:
                            # Remove commas and other non-numeric characters before converting to float
                            amount_str = amount_match.group(1).replace(',', '')
                            amount_str = re.sub(r'[^\d.]', '', amount_str)
                            amount = float(amount_str)
                            logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                            results.append((employee_no, amount, current_section))
                        except ValueError:
                            logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")

        return results

    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.

        Args:
            output_file (str): Path to the output Excel file
        """
        logger.info(f"Saving data to Excel file: {output_file}")

        try:
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.allowances_data.items():
                data_list.append(data)

            df = pd.DataFrame(data_list)

            # Reorder columns
            columns = ['EMPLOYEE_NO', 'ALL_ALLOWANCES', 'BREAKDOWN']
            df = df[columns]

            # Save to Excel
            df.to_excel(output_file, index=False)

            logger.info(f"Successfully saved data to Excel file: {output_file}")
            logger.info(f"Total records saved: {len(df)}")

        except Exception as e:
            logger.error(f"Error saving data to Excel file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

def main():
    if len(sys.argv) < 2:
        print("Usage: python extract_to_excel.py <allowances_pdf_file> [output_excel_file]")
        sys.exit(1)

    allowances_file = sys.argv[1]

    # Default output file is extract2.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "extract2.xlsx")

    # If output file is provided, use it
    if len(sys.argv) > 2:
        output_file = sys.argv[2]

    print(f"File path: {allowances_file}")
    print(f"File exists: {os.path.exists(allowances_file)}")
    print(f"Output file: {output_file}")

    extractor = AllowanceExtractor()
    extractor.extract_allowances_data(allowances_file)
    extractor.save_to_excel(output_file)

    print(f"\nExtracted allowances data for {len(extractor.allowances_data)} employees")
    print(f"Data saved to: {output_file}")

if __name__ == "__main__":
    main()
