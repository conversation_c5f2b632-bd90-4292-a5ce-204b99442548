#!/usr/bin/env python3
"""
Test script to verify hardcoded exclusion functionality.
This script tests the exclusion logic across all modules.
"""

import sys
import os

# Add the backend-dist directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_parser_exclusions():
    """Test exclusions in the payroll parser."""
    print("=" * 60)
    print("TESTING PAYROLL PARSER EXCLUSIONS")
    print("=" * 60)
    
    try:
        from improved_payroll_parser import is_excluded_item, HARDCODED_EXCLUSIONS
        
        print(f"Hardcoded exclusions list: {HARDCODED_EXCLUSIONS}")
        
        # Test exact matches
        test_items = [
            "NET PAY",
            "GROSS SALARY", 
            "INCOME TAX",
            "BASIC SALARY",
            "net pay",  # lowercase
            "Gross Salary",  # mixed case
            "INCOME_TAX",  # with underscore
            "Basic Salary Amount",  # partial match
            "ALLOWANCE",  # should not be excluded
            "PENSION",  # should not be excluded
        ]
        
        for item in test_items:
            result = is_excluded_item(item)
            status = "EXCLUDED" if result else "INCLUDED"
            print(f"  {item:<25} -> {status}")
            
        print("\nParser exclusion test completed successfully!")
        
    except Exception as e:
        print(f"Error testing parser exclusions: {e}")
        return False
    
    return True

def test_report_exclusions():
    """Test exclusions in the report generator."""
    print("\n" + "=" * 60)
    print("TESTING REPORT GENERATOR EXCLUSIONS")
    print("=" * 60)
    
    try:
        from improved_report_generator import is_excluded_item, filter_excluded_items_from_dict
        
        # Test the exclusion function
        test_items = [
            "NET PAY",
            "GROSS SALARY", 
            "INCOME TAX",
            "BASIC SALARY",
            "ALLOWANCE",
            "PENSION",
        ]
        
        for item in test_items:
            result = is_excluded_item(item)
            status = "EXCLUDED" if result else "INCLUDED"
            print(f"  {item:<25} -> {status}")
        
        # Test dictionary filtering
        print("\nTesting dictionary filtering:")
        test_dict = {
            "NET PAY": "50000",
            "GROSS SALARY": "75000",
            "INCOME TAX": "15000",
            "BASIC SALARY": "60000",
            "ALLOWANCE": "5000",
            "PENSION": "3000",
            "OVERTIME": "2000"
        }
        
        print(f"Original dict: {test_dict}")
        filtered_dict = filter_excluded_items_from_dict(test_dict, "test")
        print(f"Filtered dict: {filtered_dict}")
        
        print("\nReport generator exclusion test completed successfully!")
        
    except Exception as e:
        print(f"Error testing report generator exclusions: {e}")
        return False
    
    return True

def test_dictionary_exclusions():
    """Test exclusions in the dictionary integration."""
    print("\n" + "=" * 60)
    print("TESTING DICTIONARY INTEGRATION EXCLUSIONS")
    print("=" * 60)
    
    try:
        from dictionary_integration import is_excluded_item, should_include_in_report, standardize_earnings, standardize_deductions
        
        # Test the exclusion function
        test_items = [
            "NET PAY",
            "GROSS SALARY", 
            "INCOME TAX",
            "BASIC SALARY",
            "ALLOWANCE",
            "PENSION",
        ]
        
        for item in test_items:
            result = is_excluded_item(item)
            status = "EXCLUDED" if result else "INCLUDED"
            print(f"  {item:<25} -> {status}")
        
        # Test should_include_in_report
        print("\nTesting should_include_in_report:")
        for item in test_items:
            result = should_include_in_report("EARNINGS", item)
            status = "INCLUDED" if result else "EXCLUDED"
            print(f"  {item:<25} -> {status}")
        
        # Test standardization with filtering
        print("\nTesting earnings standardization with filtering:")
        test_earnings = {
            "NET PAY": "50000",
            "GROSS SALARY": "75000",
            "BASIC SALARY": "60000",
            "ALLOWANCE": "5000",
            "OVERTIME": "2000"
        }
        
        print(f"Original earnings: {test_earnings}")
        filtered_earnings = standardize_earnings(test_earnings)
        print(f"Filtered earnings: {filtered_earnings}")
        
        print("\nTesting deductions standardization with filtering:")
        test_deductions = {
            "INCOME TAX": "15000",
            "PENSION": "3000",
            "NHIF": "1500"
        }
        
        print(f"Original deductions: {test_deductions}")
        filtered_deductions = standardize_deductions(test_deductions)
        print(f"Filtered deductions: {filtered_deductions}")
        
        print("\nDictionary integration exclusion test completed successfully!")
        
    except Exception as e:
        print(f"Error testing dictionary integration exclusions: {e}")
        return False
    
    return True

def main():
    """Run all exclusion tests."""
    print("HARDCODED EXCLUSION TESTING")
    print("Testing exclusion of: NET PAY, GROSS SALARY, INCOME TAX, BASIC SALARY")
    print("These items should NEVER appear in extraction or reports")
    
    success = True
    
    # Test each module
    success &= test_parser_exclusions()
    success &= test_report_exclusions()
    success &= test_dictionary_exclusions()
    
    print("\n" + "=" * 60)
    if success:
        print("ALL EXCLUSION TESTS PASSED!")
        print("Hardcoded exclusions are working correctly across all modules.")
    else:
        print("SOME TESTS FAILED!")
        print("Please check the error messages above.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
