import PyPDF2
import pdfplumber
import re
import pandas as pd
import os
import json
import fitz  # PyMuPDF
import cv2
import numpy as np
from PIL import Image
import io
import pytesseract
from collections import defaultdict
from datetime import datetime
from payroll_report_generator import generate_reports
from improved_report_generator import generate_improved_reports

def extract_payslips_from_pdf(pdf_path):
    """Extract individual payslips from a multi-page PDF, treating each page as a potential payslip."""
    print(f"Extracting payslips from: {pdf_path}")

    payslips = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"Total pages in PDF: {total_pages}")

            # Process pages in batches to avoid memory issues
            batch_size = 25  # Reduced batch size to prevent freezing
            for batch_start in range(0, total_pages, batch_size):
                batch_end = min(batch_start + batch_size, total_pages)
                print(f"Processing pages {batch_start+1} to {batch_end}...")

                for i in range(batch_start, batch_end):
                    try:
                        page = pdf.pages[i]
                        page_text = page.extract_text() or ""

                        # Only add pages that have substantial text and look like payslips
                        if len(page_text.strip()) > 100 and is_likely_payslip(page_text):
                            payslips.append({
                                "text": page_text,
                                "page_num": i + 1
                            })

                        # Print progress for every page for UI updates
                        if (i + 1) % 5 == 0 or i == batch_end - 1:  # Reduce console output frequency
                            print(f"Processing page {i+1}/{total_pages}")
                    except Exception as page_error:
                        print(f"Error processing page {i+1}: {page_error}")
                        continue  # Skip this page and continue with the next one

    except Exception as e:
        print(f"Error extracting payslips with pdfplumber: {e}")
        # Fallback to PyPDF2
        try:
            print("Falling back to PyPDF2...")
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                total_pages = len(reader.pages)
                print(f"Total pages in PDF: {total_pages}")

                # Process pages in batches to avoid memory issues
                batch_size = 25  # Reduced batch size to prevent freezing
                for batch_start in range(0, total_pages, batch_size):
                    batch_end = min(batch_start + batch_size, total_pages)
                    print(f"Processing pages {batch_start+1} to {batch_end}...")

                    for i in range(batch_start, batch_end):
                        try:
                            page = reader.pages[i]
                            page_text = page.extract_text() or ""

                            # Only add pages that have substantial text and look like payslips
                            if len(page_text.strip()) > 100 and is_likely_payslip(page_text):
                                payslips.append({
                                    "text": page_text,
                                    "page_num": i + 1
                                })

                            # Print progress for every page for UI updates
                            if (i + 1) % 5 == 0 or i == batch_end - 1:  # Reduce console output frequency
                                print(f"Processing page {i+1}/{total_pages}")
                        except Exception as page_error:
                            print(f"Error processing page {i+1}: {page_error}")
                            continue  # Skip this page and continue with the next one
        except Exception as e2:
            print(f"Error extracting payslips with PyPDF2: {e2}")

    print(f"Found {len(payslips)} individual payslips")
    return payslips

def is_likely_payslip(text):
    """Determine if the text is likely to be a payslip based on key patterns."""
    # Key patterns that indicate a payslip
    payslip_indicators = [
        r"PAYSLIP",
        r"Employee Name",
        r"Employee ID",
        r"SSF No",
        r"Ghana Card ID",
        r"BASIC SALARY",
        r"GROSS SALARY",
        r"NET PAY",
        r"DEDUCTIONS",
        r"EARNINGS"
    ]

    # Count how many indicators are present
    indicator_count = 0
    for pattern in payslip_indicators:
        if re.search(pattern, text, re.IGNORECASE):
            indicator_count += 1

    # If at least 3 indicators are present, it's likely a payslip
    return indicator_count >= 3

def identify_payslip_sections(text):
    """Identify individual payslip sections in the text."""
    print("Identifying individual payslip sections...")

    # Common patterns that indicate the start of a new payslip
    payslip_start_patterns = [
        r"THE CHURCH OF PENTECOST.*?Period.*?20\d{2}",
        r"Employee Name.*?SSF No",
        r"EMPLOYEE BANK DETAILS",
        r"AMT \(GHS\) DEDUCTIONS AMT \(GHS\) EARNINGS"
    ]

    # Split the text into lines
    lines = text.split('\n')

    # Initialize variables
    payslips = []
    current_payslip = []
    payslip_count = 0

    for line in lines:
        # Check if this line indicates the start of a new payslip
        is_new_payslip = False
        for pattern in payslip_start_patterns:
            if re.search(pattern, line, re.IGNORECASE) and "Printed" in line:
                is_new_payslip = True

        # If we found a new payslip and we have content in the current one
        if is_new_payslip and current_payslip:
            payslip_text = '\n'.join(current_payslip)
            payslips.append(payslip_text)
            current_payslip = []
            payslip_count += 1
            if payslip_count % 10 == 0:
                print(f"  Identified {payslip_count} payslips so far")

        # Add the line to the current payslip
        current_payslip.append(line)

    # Add the last payslip if there's content
    if current_payslip:
        payslip_text = '\n'.join(current_payslip)
        payslips.append(payslip_text)
        payslip_count += 1

    print(f"Total payslips identified: {payslip_count}")

    # Alternative approach: split by "Akatua by theSOFTtribe" which appears to be at the end of each payslip
    if payslip_count < 10:
        print("Few payslips found. Trying alternative approach...")
        payslips = re.split(r"Akatua by theSOFTtribe.*?Printed", text)
        print(f"Alternative approach found {len(payslips)} potential payslips")

    return payslips

def extract_employee_id_with_layout(pdf_path, page_num):
    """
    Extract Employee No. using advanced layout detection techniques.
    This function uses PyMuPDF to analyze the PDF layout and extract the Employee No.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to extract from (1-based)

    Returns:
        The extracted Employee No. or None if not found
    """
    print(f"Attempting to extract Employee No. using advanced layout detection for page {page_num}...")

    try:
        # Open the PDF with PyMuPDF
        doc = fitz.open(pdf_path)
        page = doc[page_num - 1]  # Convert to 0-based index

        # Get the page text with detailed layout information
        blocks = page.get_text("dict")["blocks"]

        # Look for blocks containing "Employee No." or similar
        employee_id = None

        # First, look specifically for the Employee No. formats: COP####, PW####, SEC####, E####
        # These are often found at the top left of the payslip
        for block in blocks:
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]

                    # Look for specific Employee No. formats
                    id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', line_text)
                    if id_candidates:
                        employee_id = id_candidates[0]
                        print(f"Found Employee No. in line: {employee_id}")



        # If not found, look for blocks containing "Employee No." or similar
        if not employee_id:
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]

                        # Check if this line contains "Employee No."
                        if re.search(r"Employee\s*No\.?", line_text, re.IGNORECASE):
                            print(f"Found 'Employee No.' in line: {line_text}")

                            # Extract the ID from this line
                            id_match = re.search(r"([A-Z0-9-]{3,15})\s+Employee\s*No\.?", line_text)
                            if id_match:
                                employee_id = id_match.group(1).strip()
                                print(f"Extracted Employee No.: {employee_id}")

                            # If not found in the same line, look at nearby lines
                            # Get the position of this line
                            line_y = line["bbox"][1]  # Top y-coordinate

                            # Look for text that could be an ID in nearby lines
                            for other_block in blocks:
                                if "lines" in other_block:
                                    for other_line in other_block["lines"]:
                                        other_y = other_line["bbox"][1]
                                        # Check if the line is close to the "Employee No." line
                                        if abs(other_y - line_y) < 20:
                                            other_text = ""
                                            for span in other_line["spans"]:
                                                other_text += span["text"]

                                            # Look for specific Employee No. formats
                                            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', other_text)
                                            if id_candidates:
                                                employee_id = id_candidates[0]
                                                print(f"Found Employee No. in nearby line: {employee_id}")



        # If we still don't have an ID, try OCR on the page
        if not employee_id:
            print("Trying OCR to extract Employee No...")
            # Render the page to an image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))

            # Convert to numpy array for OpenCV
            img_np = np.array(img)

            # Convert to grayscale
            gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)

            # Apply thresholding
            _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Focus on the top-left portion of the page where Employee No. is typically found
            # Sort contours by position (top to bottom, left to right)
            contours = sorted(contours, key=lambda c: (cv2.boundingRect(c)[1], cv2.boundingRect(c)[0]))

            # First, look for specific Employee No. formats in the entire image
            full_text = pytesseract.image_to_string(gray)
            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', full_text)
            if id_candidates:
                employee_id = id_candidates[0]
                print(f"Found Employee No. using OCR on full page: {employee_id}")
            else:
                # Look for text regions that might contain "Employee No."
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    # Focus on the top portion of the page
                    if y < img_np.shape[0] / 3 and w > 50 and h > 10:  # Top third of page, filter out small regions
                        # Extract the region
                        roi = gray[y:y+h, x:x+w]
                        # Apply OCR
                        text = pytesseract.image_to_string(roi)

                        # Look for specific Employee No. formats
                        id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', text)
                        if id_candidates:
                            employee_id = id_candidates[0]
                            print(f"Found Employee No. using OCR in region: {employee_id}")

                        # Check if this region contains Employee No. related text
                        if re.search(r'employee|no\.?', text.lower()):
                            # Look for Employee No. pattern in this region and nearby regions
                            expanded_roi = gray[max(0, y-20):min(gray.shape[0], y+h+20),
                                               max(0, x-20):min(gray.shape[1], x+w+100)]
                            expanded_text = pytesseract.image_to_string(expanded_roi)

                            # Look for specific Employee No. formats
                            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', expanded_text)
                            if id_candidates:
                                employee_id = id_candidates[0]
                                print(f"Found Employee No. using OCR in expanded region: {employee_id}")

        doc.close()
        return employee_id

    except Exception as e:
        print(f"Error in advanced layout detection: {str(e)}")
        return None

def extract_employee_data(payslip_text):
    """Extract employee data from a single payslip."""
    employee = {
        "employee_id": None,      # Primary identifier (default)
        "ssf_no": None,           # Alternative identifier
        "ghana_card_id": None,    # Alternative identifier
        "name": None,             # Alternative identifier
        "job_title": None,
        "department": None,       # Added for sorting/comparison
        "section": None,          # Added for sorting/comparison
        "basic_salary": None,
        "gross_salary": None,
        "net_pay": None,
        "deductions": {},         # Dictionary for easier comparison
        "earnings": {},           # Dictionary for easier comparison
        "month": None,
        "year": None,
        "loan": None,             # Added for loan information
        "bank_details": None,     # Added for employee bank details
        "employer_contributions": {}  # Added for employer's contributions
    }

    # Extract employee name
    name_match = re.search(r"([A-Z]+\s+[A-Z]+\s+[A-Z]+)\s+Employee Name", payslip_text)
    if name_match:
        employee["name"] = name_match.group(1).strip()

    # Extract employee number - this is critical for accurate matching
    # Try multiple patterns to ensure we capture the Employee No. correctly
    # Employee No. formats: COP####, PW####, SEC####, E####
    emp_id_patterns = [
        r"([A-Z0-9-]+)\s+Employee No\.",
        r"Employee No\.?\s*([A-Za-z0-9-]+)",
        r"Employee Number:?\s*([A-Za-z0-9-]+)",
        r"(COP\d{4})",  # Format: COP followed by 4 digits
        r"(PW\d{4})",   # Format: PW followed by 4 digits
        r"(SEC\d{4})",  # Format: SEC followed by 4 digits
        r"(E\d{4})",    # Format: E followed by 4 digits
        r"Employee No:?\s*([A-Za-z0-9-]+)",
        r"Emp\. No\.?\s*([A-Za-z0-9-]+)",
        r"EMP NO\.?\s*([A-Za-z0-9-]+)"
    ]

    for pattern in emp_id_patterns:
        emp_id_match = re.search(pattern, payslip_text)
        if emp_id_match:
            potential_id = emp_id_match.group(1).strip()
            # Validate the Employee No. format - COP####, PW####, SEC####, E####
            if re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', potential_id):
                employee["employee_id"] = potential_id
                print(f"Successfully extracted Employee No.: {potential_id}")
            # Also accept other alphanumeric formats as a fallback
            elif re.match(r'^[A-Za-z0-9-]{3,15}$', potential_id):
                employee["employee_id"] = potential_id
                print(f"Successfully extracted Employee No. (alternative format): {potential_id}")

    # If we still don't have an ID, try to find it in a different section
    if not employee.get("employee_id"):
        # Look specifically for the Employee No. formats: COP####, PW####, SEC####, E####
        id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', payslip_text)
        if id_candidates:
            employee["employee_id"] = id_candidates[0]
            print(f"Found Employee No. from pattern matching: {id_candidates[0]}")
        # If still not found, try a more general pattern
        elif not employee.get("employee_id"):
            id_candidates = re.findall(r'\b([A-Z]{2,4}[0-9]{3,6})\b', payslip_text)
            if id_candidates:
                employee["employee_id"] = id_candidates[0]
                print(f"Found potential Employee No. from general pattern matching: {id_candidates[0]}")

    # Extract SSF number
    ssf_match = re.search(r"([A-Z0-9]+)\s+SSF No", payslip_text)
    if ssf_match:
        employee["ssf_no"] = ssf_match.group(1).strip()

    # Extract Ghana Card ID
    ghana_card_match = re.search(r"(GHA-\d+-\d+)\s+Ghana Card ID", payslip_text)
    if ghana_card_match:
        employee["ghana_card_id"] = ghana_card_match.group(1).strip()

    # Extract job title
    job_title_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Job Title", payslip_text)
    if job_title_match:
        employee["job_title"] = job_title_match.group(1).strip()

    # Extract department - critical for grouping reports
    dept_patterns = [
        r"([A-Z][A-Za-z\s\-\.]+)\s+Department",
        r"Department:?\s*([A-Za-z\s\-\.]+)",
        r"Dept:?\s*([A-Za-z\s\-\.]+)",
        r"DEPARTMENT:?\s*([A-Za-z\s\-\.]+)"
    ]

    for pattern in dept_patterns:
        dept_match = re.search(pattern, payslip_text)
        if dept_match:
            department = dept_match.group(1).strip()
            # Clean up department name - remove extra spaces, standardize case
            department = ' '.join(department.split())
            if department.isupper():
                department = department.title()
            employee["department"] = department
            print(f"Successfully extracted Department: {department}")
            break

    # If we still don't have a department, try to find it in a different section
    if not employee.get("department"):
        # Look for common department names
        dept_keywords = ["AREA", "DISTRICT", "REGION", "HEADQUARTERS", "OFFICE", "MINISTRY", "DEPARTMENT"]
        for keyword in dept_keywords:
            dept_candidates = re.findall(r'\b([A-Z][A-Za-z\s\-\.]+' + keyword + r')\b', payslip_text)
            if dept_candidates:
                employee["department"] = dept_candidates[0].strip()
                print(f"Found potential Department from keyword matching: {dept_candidates[0]}")

    # Extract section - important for employee identification
    section_patterns = [
        r"([A-Z][A-Za-z\s\-\.]+)\s+Section",
        r"Section:?\s*([A-Za-z\s\-\.]+)",
        r"SECTION:?\s*([A-Za-z\s\-\.]+)"
    ]

    for pattern in section_patterns:
        section_match = re.search(pattern, payslip_text)
        if section_match:
            section = section_match.group(1).strip()
            # Clean up section name
            section = ' '.join(section.split())
            if section.isupper():
                section = section.title()
            employee["section"] = section
            print(f"Successfully extracted Section: {section}")
            break

    # Extract basic salary
    basic_salary_match = re.search(r"BASIC SALARY\s+([\d,.]+)", payslip_text)
    if basic_salary_match:
        employee["basic_salary"] = basic_salary_match.group(1).strip()

    # Extract gross salary
    gross_salary_match = re.search(r"GROSS SALARY\s+([\d,.]+)", payslip_text)
    if gross_salary_match:
        employee["gross_salary"] = gross_salary_match.group(1).strip()

    # Extract net pay
    net_pay_match = re.search(r"NET PAY\s+([\d,.]+)", payslip_text)
    if net_pay_match:
        employee["net_pay"] = net_pay_match.group(1).strip()

    # Extract month and year
    period_match = re.search(r"([A-Za-z]+)\s+(\d{4})\s+Period", payslip_text)
    if period_match:
        employee["month"] = period_match.group(1).strip()
        employee["year"] = period_match.group(2).strip()

    # Extract loan information
    loan_match = re.search(r"Loan Balance:?\s*([\d,.]+)", payslip_text, re.IGNORECASE)
    if loan_match:
        employee["loan"] = loan_match.group(1).strip()

    # Extract bank details
    bank_details_section = re.search(r"EMPLOYEE BANK DETAILS(.*?)(?:EARNINGS|DEDUCTIONS)",
                                    payslip_text, re.DOTALL | re.IGNORECASE)
    if bank_details_section:
        bank_text = bank_details_section.group(1).strip()

        # Try to extract bank name
        bank_name_match = re.search(r"Bank Name:?\s*([A-Za-z\s]+)", bank_text, re.IGNORECASE)
        bank_branch_match = re.search(r"Branch:?\s*([A-Za-z\s]+)", bank_text, re.IGNORECASE)
        account_number_match = re.search(r"Account Number:?\s*(\d+)", bank_text, re.IGNORECASE)

        bank_details = {}
        if bank_name_match:
            bank_details["bank_name"] = bank_name_match.group(1).strip()
        if bank_branch_match:
            bank_details["branch"] = bank_branch_match.group(1).strip()
        if account_number_match:
            bank_details["account_number"] = account_number_match.group(1).strip()

        if bank_details:
            employee["bank_details"] = bank_details

    # Extract employer contributions
    # SSF Employer Contribution
    ssf_employer_match = re.search(r"SSF EMPLOYER\s+([\d,.]+)", payslip_text)
    if ssf_employer_match:
        employee["employer_contributions"]["SSF Employer"] = ssf_employer_match.group(1).strip()

    # Tier 2 Employer Contribution
    tier2_match = re.search(r"TIER 2 EMPLOYER\s+([\d,.]+)", payslip_text)
    if tier2_match:
        employee["employer_contributions"]["Tier 2"] = tier2_match.group(1).strip()

    # Provident Fund Employer Contribution
    provident_match = re.search(r"PROVIDENT FUND EMPLOYER\s+([\d,.]+)", payslip_text)
    if provident_match:
        employee["employer_contributions"]["Provident Fund"] = provident_match.group(1).strip()

    # Extract specific deductions with more precise patterns
    # SSF Employee Contribution
    ssf_emp_match = re.search(r"SSF EEMPLOYEE\s+([\d,.]+)", payslip_text)
    if ssf_emp_match:
        employee["deductions"]["SSF Employee"] = ssf_emp_match.group(1).strip()

    # Income Tax / PAYE
    tax_match = re.search(r"INCOME TAX\s+([\d,.]+)", payslip_text)
    if tax_match:
        employee["deductions"]["Income Tax"] = tax_match.group(1).strip()

    # Tithes
    tithes_match = re.search(r"TITHES\s+([\d,.]+)", payslip_text)
    if tithes_match:
        employee["deductions"]["Tithes"] = tithes_match.group(1).strip()

    # Ministers Pension
    pension_match = re.search(r"MINISTERS-PENSION\s+([\d,.]+)", payslip_text)
    if pension_match:
        employee["deductions"]["Ministers Pension"] = pension_match.group(1).strip()

    # Welfare Fund
    welfare_match = re.search(r"PENT\. MIN WELFARE FUND\s+([\d,.]+)", payslip_text)
    if welfare_match:
        employee["deductions"]["Welfare Fund"] = welfare_match.group(1).strip()

    # Staff Mutual Health Fund
    health_match = re.search(r"STAFF MUTUAL HEALTH FUND\s+([\d,.]+)", payslip_text)
    if health_match:
        employee["deductions"]["Health Fund"] = health_match.group(1).strip()

    # Loan Deductions
    loan_match = re.search(r"Loan Deductions\s+([\d,.]+)", payslip_text)
    if loan_match:
        employee["deductions"]["Loan"] = loan_match.group(1).strip()

    # Extract specific earnings/allowances
    # Transport Elements
    transport1_match = re.search(r"1ST TRANSPORT ELEMEN\s+([\d,.]+)", payslip_text)
    if transport1_match:
        employee["earnings"]["1st Transport Element"] = transport1_match.group(1).strip()

    transport2_match = re.search(r"2ND TRANSPORT ELEME\s+([\d,.]+)", payslip_text)
    if transport2_match:
        employee["earnings"]["2nd Transport Element"] = transport2_match.group(1).strip()

    # Rent Element
    rent_match = re.search(r"RENT ELEMENT\s+([\d,.]+)", payslip_text)
    if rent_match:
        employee["earnings"]["Rent Element"] = rent_match.group(1).strip()

    # Leave Allowance
    leave_match = re.search(r"LEAVE ALLOWANCE\s+([\d,.]+)", payslip_text)
    if leave_match:
        employee["earnings"]["Leave Allowance"] = leave_match.group(1).strip()

    # Subsistence Allowance
    subsistence_match = re.search(r"SUBSISTENCEMINISTERS\s+([\d,.]+)", payslip_text)
    if subsistence_match:
        employee["earnings"]["Subsistence Allowance"] = subsistence_match.group(1).strip()

    # Set the employee_id (primary identifier) based on hierarchy of available identifiers
    # Default to SSF number if available
    if employee["ssf_no"]:
        employee["employee_id"] = employee["ssf_no"]
    # Otherwise use Ghana Card ID
    elif employee["ghana_card_id"]:
        employee["employee_id"] = employee["ghana_card_id"]
    # Otherwise use name
    elif employee["name"]:
        employee["employee_id"] = employee["name"]
    # Generate a random ID as last resort
    else:
        employee["employee_id"] = f"Unknown_Employee_{hash(payslip_text) % 10000}"

    return employee

def parse_payroll_data(payslips, pdf_path=None):
    """Parse the extracted payslips to identify payroll data."""
    print("Parsing payroll data...")

    # Extract employee data from each payslip
    employees = []
    for i, payslip in enumerate(payslips):
        # Print progress for every payslip for UI updates
        print(f"Processing payslip {i+1}/{len(payslips)}")

        payslip_text = payslip["text"]
        employee = extract_employee_data(payslip_text)

        # Add page number information
        page_num = payslip.get("page_num", 0)
        employee["page_num"] = page_num

        # If we have a PDF path and the employee No. is missing or doesn't match expected formats,
        # try to extract it using advanced layout detection
        if pdf_path and (not employee["employee_id"] or not re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', str(employee["employee_id"]))):
            print(f"Employee No. missing or doesn't match expected format: {employee['employee_id']}")
            print("Trying advanced layout detection...")

            # First check if we already have an ID from layout detection
            if "employee_id_from_layout" in payslip and payslip["employee_id_from_layout"]:
                employee["employee_id"] = payslip["employee_id_from_layout"]
                print(f"Using pre-extracted Employee No. from layout: {employee['employee_id']}")
            # Otherwise try to extract it now
            elif page_num > 0:
                layout_id = extract_employee_id_with_layout(pdf_path, page_num)
                if layout_id:
                    employee["employee_id"] = layout_id
                    print(f"Successfully extracted Employee No. using layout detection: {layout_id}")

        # Only add employees with at least some data and a valid Employee No.
        if employee["employee_id"] and (employee["name"] or employee["basic_salary"] or employee["net_pay"]):
            # Validate the Employee No. format - COP####, PW####, SEC####, E####
            if re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', str(employee["employee_id"])):
                print(f"Valid Employee No. format: {employee['employee_id']}")
                employees.append(employee)
            else:
                print(f"WARNING: Employee No. doesn't match expected format: {employee['employee_id']}")
                # Still add it but with a warning
                employees.append(employee)

    print(f"Successfully extracted data for {len(employees)} employees")

    # If we didn't find many employees, try a more aggressive approach
    if len(employees) < 10:
        print("Few employees found. Trying more aggressive pattern matching...")

        # Combine all payslip texts for pattern matching
        all_text = "\n".join([p["text"] for p in payslips])

        # Look for patterns like "Employee Name" followed by a name
        name_matches = re.finditer(r"([A-Z]+\s+[A-Z]+\s+[A-Z]+)\s+Employee Name", all_text)
        for i, match in enumerate(name_matches):
            name = match.group(1).strip()

            # Create a basic employee record
            employee = {
                "employee_id": f"Employee_{i+1}",
                "ssf_no": None,
                "ghana_card_id": None,
                "name": name,
                "job_title": None,
                "department": None,  # Added department field
                "section": None,     # Added section field
                "basic_salary": None,
                "gross_salary": None,
                "net_pay": None,
                "deductions": {},
                "earnings": {},
                "month": None,
                "year": None,
                "loan": None,
                "bank_details": None,
                "employer_contributions": {},
                "page_num": 0
            }

            # Look for salary information near the name
            context = all_text[max(0, match.start() - 500):min(len(all_text), match.end() + 500)]

            # Try to find gross salary
            gross_match = re.search(r"GROSS SALARY\s+([\d,.]+)", context)
            if gross_match:
                employee["gross_salary"] = gross_match.group(1).strip()

            # Try to find net pay
            net_match = re.search(r"NET PAY\s+([\d,.]+)", context)
            if net_match:
                employee["net_pay"] = net_match.group(1).strip()

            # Try to find SSF number
            ssf_match = re.search(r"([A-Z0-9]+)\s+SSF No", context)
            if ssf_match:
                employee["ssf_no"] = ssf_match.group(1).strip()

            # Try to find Ghana Card ID
            ghana_card_match = re.search(r"(GHA-\d+-\d+)\s+Ghana Card ID", context)
            if ghana_card_match:
                employee["ghana_card_id"] = ghana_card_match.group(1).strip()

            # Try to find job title
            job_title_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Job Title", context)
            if job_title_match:
                employee["job_title"] = job_title_match.group(1).strip()

            # Try to find department
            dept_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Department", context)
            if dept_match:
                employee["department"] = dept_match.group(1).strip()

            # Try to find section
            section_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Section", context)
            if section_match:
                employee["section"] = section_match.group(1).strip()

            # Try to find month and year
            period_match = re.search(r"([A-Za-z]+)\s+(\d{4})\s+Period", context)
            if period_match:
                employee["month"] = period_match.group(1).strip()
                employee["year"] = period_match.group(2).strip()

            employees.append(employee)

        print(f"Aggressive approach found {len(employees)} employees")

    return employees

def compare_payrolls(prev_data, curr_data, id_field="employee_id"):
    """
    Compare previous and current payroll data.

    Args:
        prev_data: List of employee data dictionaries from previous month
        curr_data: List of employee data dictionaries from current month
        id_field: The field to use as the unique identifier (default: "employee_id")
    """
    print("Comparing payroll data...")
    print(f"Using '{id_field}' as the unique identifier for comparison")

    comparison = []

    # Create a dictionary of previous employees by the specified ID field for easy lookup
    prev_dict = {emp[id_field]: emp for emp in prev_data if emp.get(id_field)}

    print(f"Previous payroll has {len(prev_data)} employees")
    print(f"Current payroll has {len(curr_data)} employees")

    # Function to safely convert amount strings to float for comparison
    def parse_amount(amount_str):
        if not amount_str:
            return 0.0
        try:
            # Remove any non-numeric characters except decimal point
            clean_str = ''.join(c for c in amount_str if c.isdigit() or c == '.')
            return float(clean_str)
        except (ValueError, TypeError):
            print(f"Warning: Could not parse amount '{amount_str}'")
            return 0.0

    # Function to format amount for display
    def format_amount(amount_str):
        if not amount_str:
            return "N/A"
        return amount_str

    # Function to calculate and format change
    def calculate_change(prev_val, curr_val, label, emp_id, department, section, prev_month=None, curr_month=None):
        if prev_val > 0 and curr_val > 0 and prev_val != curr_val:
            diff = curr_val - prev_val

            # Format the employee identifier
            emp_identifier = f"{emp_id}: {section}-{department}"

            # Create change message with employee identifier
            change_msg = f"{emp_identifier}; {label} changed from {prev_val:.2f} to {curr_val:.2f}"

            # Add month information if available
            month_info = ""
            if prev_month and curr_month:
                month_info = f" in {curr_month}"

            if diff > 0:
                change_msg += f" (increase of {diff:.2f}{month_info})"
            else:
                change_msg += f" (decrease of {abs(diff):.2f}{month_info})"

            return {
                "type": label,
                "previous": prev_val,
                "current": curr_val,
                "difference": diff,
                "employee_id": emp_id,
                "department": department,
                "section": section,
                "prev_month": prev_month,
                "curr_month": curr_month,
                "message": change_msg
            }
        return None

    # Process current employees
    for i, curr_emp in enumerate(curr_data):
        if not curr_emp.get(id_field):
            print(f"Skipping employee with no {id_field}")
            continue

        emp_id = curr_emp[id_field]
        # Print progress for every employee for UI updates
        print(f"Comparing employee {i+1}/{len(curr_data)}: {emp_id}")

        # Get employee name if available
        emp_name = curr_emp.get("name", "Unknown")

        result = {
            "id": emp_id,
            "name": emp_name,
            "job_title": curr_emp.get("job_title", "Unknown"),
            "month": curr_emp.get("month", "Unknown"),
            "year": curr_emp.get("year", "Unknown"),
            "current_basic_salary": format_amount(curr_emp.get("basic_salary")),
            "current_gross_salary": format_amount(curr_emp.get("gross_salary")),
            "current_net_pay": format_amount(curr_emp.get("net_pay")),
            "current_deductions": curr_emp.get("deductions", {}),
            "current_earnings": curr_emp.get("earnings", {}),
            "changes": [],
            "detailed_changes": {
                "salary": [],
                "deductions": [],
                "earnings": [],
                "other": []
            }
        }

        # Check if employee exists in previous payroll
        if emp_id in prev_dict:
            prev_emp = prev_dict[emp_id]
            print(f"Found matching employee in previous payroll")

            # Add previous data to result
            result["previous_basic_salary"] = format_amount(prev_emp.get("basic_salary"))
            result["previous_gross_salary"] = format_amount(prev_emp.get("gross_salary"))
            result["previous_net_pay"] = format_amount(prev_emp.get("net_pay"))
            result["previous_deductions"] = prev_emp.get("deductions", {})
            result["previous_earnings"] = prev_emp.get("earnings", {})

            # Get employee details for change messages
            emp_id = curr_emp.get(id_field, "Unknown")
            department = curr_emp.get("department", "Unknown")
            section = curr_emp.get("section", "Unknown")

            # Get month information
            prev_month = prev_emp.get("month", "Unknown")
            curr_month = curr_emp.get("month", "Unknown")

            # Check for job title changes
            prev_job = prev_emp.get("job_title")
            curr_job = curr_emp.get("job_title")
            if prev_job and curr_job and prev_job != curr_job:
                change_msg = f"{emp_id}: {section}-{department}; Job title changed from '{prev_job}' to '{curr_job}' in {curr_month}"
                result["changes"].append(change_msg)
                result["detailed_changes"]["other"].append({
                    "type": "Job Title",
                    "previous": prev_job,
                    "current": curr_job,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": change_msg
                })
                print(change_msg)

            # Check for basic salary changes
            prev_basic = parse_amount(prev_emp.get("basic_salary"))
            curr_basic = parse_amount(curr_emp.get("basic_salary"))
            basic_change = calculate_change(prev_basic, curr_basic, "Basic Salary", emp_id, department, section, prev_month, curr_month)
            if basic_change:
                result["changes"].append(basic_change["message"])
                result["detailed_changes"]["salary"].append(basic_change)
                print(basic_change["message"])

            # Check for gross salary changes
            prev_gross = parse_amount(prev_emp.get("gross_salary"))
            curr_gross = parse_amount(curr_emp.get("gross_salary"))
            gross_change = calculate_change(prev_gross, curr_gross, "Gross Salary", emp_id, department, section, prev_month, curr_month)
            if gross_change:
                result["changes"].append(gross_change["message"])
                result["detailed_changes"]["salary"].append(gross_change)
                print(gross_change["message"])

            # Check for net pay changes
            prev_net = parse_amount(prev_emp.get("net_pay"))
            curr_net = parse_amount(curr_emp.get("net_pay"))
            net_change = calculate_change(prev_net, curr_net, "Net Pay", emp_id, department, section, prev_month, curr_month)
            if net_change:
                result["changes"].append(net_change["message"])
                result["detailed_changes"]["salary"].append(net_change)
                print(net_change["message"])

            # Check for deduction changes
            prev_deductions = prev_emp.get("deductions", {})
            curr_deductions = curr_emp.get("deductions", {})

            # Check for changes in existing deductions
            for deduction_name in set(prev_deductions.keys()) | set(curr_deductions.keys()):
                prev_amount = parse_amount(prev_deductions.get(deduction_name, "0"))
                curr_amount = parse_amount(curr_deductions.get(deduction_name, "0"))

                # New deduction
                if deduction_name not in prev_deductions and deduction_name in curr_deductions:
                    change_msg = f"{emp_id}: {section}-{department}; New deduction: {deduction_name} ({curr_amount:.2f}) in {curr_month}"
                    result["changes"].append(change_msg)
                    result["detailed_changes"]["deductions"].append({
                        "type": deduction_name,
                        "previous": 0,
                        "current": curr_amount,
                        "difference": curr_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": change_msg
                    })
                    print(change_msg)

                # Removed deduction
                elif deduction_name in prev_deductions and deduction_name not in curr_deductions:
                    change_msg = f"{emp_id}: {section}-{department}; Removed deduction: {deduction_name} (was {prev_amount:.2f} in {prev_month})"
                    result["changes"].append(change_msg)
                    result["detailed_changes"]["deductions"].append({
                        "type": deduction_name,
                        "previous": prev_amount,
                        "current": 0,
                        "difference": -prev_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": change_msg
                    })
                    print(change_msg)

                # Changed deduction amount
                elif prev_amount != curr_amount:
                    deduction_change = calculate_change(prev_amount, curr_amount, f"Deduction: {deduction_name}", emp_id, department, section, prev_month, curr_month)
                    if deduction_change:
                        result["changes"].append(deduction_change["message"])
                        result["detailed_changes"]["deductions"].append(deduction_change)
                        print(deduction_change["message"])

            # Check for earnings/allowances changes
            prev_earnings = prev_emp.get("earnings", {})
            curr_earnings = curr_emp.get("earnings", {})

            # Check for changes in existing earnings
            for earning_name in set(prev_earnings.keys()) | set(curr_earnings.keys()):
                prev_amount = parse_amount(prev_earnings.get(earning_name, "0"))
                curr_amount = parse_amount(curr_earnings.get(earning_name, "0"))

                # New earning
                if earning_name not in prev_earnings and earning_name in curr_earnings:
                    change_msg = f"{emp_id}: {section}-{department}; New allowance: {earning_name} ({curr_amount:.2f}) in {curr_month}"
                    result["changes"].append(change_msg)
                    result["detailed_changes"]["earnings"].append({
                        "type": earning_name,
                        "previous": 0,
                        "current": curr_amount,
                        "difference": curr_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": change_msg
                    })
                    print(change_msg)

                # Removed earning
                elif earning_name in prev_earnings and earning_name not in curr_earnings:
                    change_msg = f"{emp_id}: {section}-{department}; Removed allowance: {earning_name} (was {prev_amount:.2f} in {prev_month})"
                    result["changes"].append(change_msg)
                    result["detailed_changes"]["earnings"].append({
                        "type": earning_name,
                        "previous": prev_amount,
                        "current": 0,
                        "difference": -prev_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": change_msg
                    })
                    print(change_msg)

                # Changed earning amount
                elif prev_amount != curr_amount:
                    earning_change = calculate_change(prev_amount, curr_amount, f"Allowance: {earning_name}", emp_id, department, section, prev_month, curr_month)
                    if earning_change:
                        result["changes"].append(earning_change["message"])
                        result["detailed_changes"]["earnings"].append(earning_change)
                        print(earning_change["message"])

            # If no changes were detected
            if not result["changes"]:
                result["changes"].append("No significant changes detected")
                print("No significant changes detected")

        else:
            # Get employee details for new employee
            emp_id = curr_emp.get(id_field, "Unknown")
            department = curr_emp.get("department", "Unknown")
            section = curr_emp.get("section", "Unknown")
            curr_month = curr_emp.get("month", "Unknown")

            change_msg = f"{emp_id}: {section}-{department}; New employee (not in previous payroll) in {curr_month}"
            result["changes"].append(change_msg)
            result["detailed_changes"]["other"].append({
                "type": "Employment Status",
                "employee_id": emp_id,
                "department": department,
                "section": section,
                "curr_month": curr_month,
                "message": change_msg
            })
            print(change_msg)

        comparison.append(result)

    # Check for employees who were removed
    for prev_emp_id, prev_emp in prev_dict.items():
        if not any(emp.get(id_field) == prev_emp_id for emp in curr_data):
            emp_name = prev_emp.get("name", "Unknown")
            department = prev_emp.get("department", "Unknown")
            section = prev_emp.get("section", "Unknown")

            prev_month = prev_emp.get("month", "Unknown")
            change_msg = f"{prev_emp_id}: {section}-{department}; Employee removed (not in current payroll) after {prev_month}"
            print(change_msg)

            comparison.append({
                "id": prev_emp_id,
                "name": emp_name,
                "job_title": prev_emp.get("job_title", "Unknown"),
                "month": prev_emp.get("month", "Unknown"),
                "year": prev_emp.get("year", "Unknown"),
                "department": department,
                "section": section,
                "previous_basic_salary": format_amount(prev_emp.get("basic_salary")),
                "previous_gross_salary": format_amount(prev_emp.get("gross_salary")),
                "previous_net_pay": format_amount(prev_emp.get("net_pay")),
                "previous_deductions": prev_emp.get("deductions", {}),
                "previous_earnings": prev_emp.get("earnings", {}),
                "current_basic_salary": "N/A",
                "current_gross_salary": "N/A",
                "current_net_pay": "N/A",
                "current_deductions": {},
                "current_earnings": {},
                "changes": [change_msg],
                "detailed_changes": {
                    "other": [{
                        "type": "Employment Status",
                        "employee_id": prev_emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "message": change_msg
                    }]
                }
            })

    print(f"Comparison complete. Found {len(comparison)} entries.")
    return comparison

def generate_reports_old(comparison_data, output_dir):
    """Generate reports in various formats."""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

    print("Generating reports...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create Excel report
    excel_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.xlsx")
    wb = Workbook()

    # Summary worksheet
    ws_summary = wb.active
    ws_summary.title = "Summary"

    # Add title and timestamp
    ws_summary.cell(row=1, column=1, value="Payroll Comparison Report")
    ws_summary.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_summary.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add statistics
    ws_summary.cell(row=4, column=1, value="Statistics:").font = Font(bold=True)
    ws_summary.cell(row=5, column=1, value="Total employees analyzed:")
    ws_summary.cell(row=5, column=2, value=len(comparison_data))

    # Count employees with changes
    employees_with_changes = sum(1 for emp in comparison_data if any(change != "No significant changes detected" for change in emp["changes"]))
    ws_summary.cell(row=6, column=1, value="Employees with changes:")
    ws_summary.cell(row=6, column=2, value=employees_with_changes)

    # Count new employees
    new_employees = sum(1 for emp in comparison_data if "New employee" in str(emp["changes"][0]))
    ws_summary.cell(row=7, column=1, value="New employees:")
    ws_summary.cell(row=7, column=2, value=new_employees)

    # Count removed employees
    removed_employees = sum(1 for emp in comparison_data if "Employee removed" in str(emp["changes"][0]))
    ws_summary.cell(row=8, column=1, value="Removed employees:")
    ws_summary.cell(row=8, column=2, value=removed_employees)

    # Group employees by department
    departments = {}
    for emp in comparison_data:
        department = emp.get("department", "Unknown")
        if department not in departments:
            departments[department] = []
        departments[department].append(emp)

    # Create a worksheet for each department
    for department, employees in departments.items():
        # Create a safe worksheet name (max 31 chars, no invalid chars)
        safe_dept_name = department[:25].replace('/', '_').replace('\\', '_').replace('?', '_').replace('*', '_').replace('[', '_').replace(']', '_').replace(':', '_')
        ws_dept = wb.create_sheet(safe_dept_name)

        # Add department title
        ws_dept.cell(row=1, column=1, value=f"Department: {department}")
        ws_dept.cell(row=1, column=1).font = Font(bold=True, size=14)

        # Add headers
        headers = ["Employee ID", "Section", "Employee Name", "Previous Salary", "Current Salary", "Change",
                  "Previous Net Pay", "Current Net Pay", "Change", "Changes"]

        # Style the header row
        header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
        header_font = Font(bold=True)
        header_border = Border(bottom=Side(style='thin'))

        for col, header in enumerate(headers, 1):
            cell = ws_dept.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = header_border

        # Add employee data
        for row, emp in enumerate(employees, 4):
            ws_dept.cell(row=row, column=1, value=emp["id"])
            ws_dept.cell(row=row, column=2, value=emp.get("section", "Unknown"))
            ws_dept.cell(row=row, column=3, value=emp.get("name", "Unknown"))

            # Salary data
            prev_salary = emp.get("previous_basic_salary", "N/A")
            curr_salary = emp.get("current_basic_salary", "N/A")
            ws_dept.cell(row=row, column=4, value=prev_salary)
            ws_dept.cell(row=row, column=5, value=curr_salary)

            # Calculate salary change
            if prev_salary != "N/A" and curr_salary != "N/A":
                try:
                    prev_val = float(''.join(c for c in prev_salary if c.isdigit() or c == '.'))
                    curr_val = float(''.join(c for c in curr_salary if c.isdigit() or c == '.'))
                    if prev_val > 0:
                        diff = curr_val - prev_val
                        if diff > 0:
                            change_text = f"Increase: {diff:.2f}"
                        else:
                            change_text = f"Decrease: {abs(diff):.2f}"
                        ws_dept.cell(row=row, column=6, value=change_text)
                except (ValueError, TypeError):
                    ws_dept.cell(row=row, column=6, value="N/A")
            else:
                ws_dept.cell(row=row, column=6, value="N/A")

            # Net pay data
            prev_net = emp.get("previous_net_pay", "N/A")
            curr_net = emp.get("current_net_pay", "N/A")
            ws_dept.cell(row=row, column=7, value=prev_net)
            ws_dept.cell(row=row, column=8, value=curr_net)

            # Calculate net pay change
            if prev_net != "N/A" and curr_net != "N/A":
                try:
                    prev_val = float(''.join(c for c in prev_net if c.isdigit() or c == '.'))
                    curr_val = float(''.join(c for c in curr_net if c.isdigit() or c == '.'))
                    if prev_val > 0:
                        diff = curr_val - prev_val
                        if diff > 0:
                            change_text = f"Increase: {diff:.2f}"
                        else:
                            change_text = f"Decrease: {abs(diff):.2f}"
                        ws_dept.cell(row=row, column=9, value=change_text)
                except (ValueError, TypeError):
                    ws_dept.cell(row=row, column=9, value="N/A")
            else:
                ws_dept.cell(row=row, column=9, value="N/A")

            # Changes
            changes_text = "\n".join(emp["changes"])
            ws_dept.cell(row=row, column=10, value=changes_text)
            ws_dept.cell(row=row, column=10).alignment = Alignment(wrapText=True)

        # Adjust column widths
        for col in range(1, 11):
            max_length = 0
            for row in range(3, ws_dept.max_row + 1):
                cell = ws_dept.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max_length + 2
            ws_dept.column_dimensions[chr(64 + col)].width = adjusted_width

    # Detailed comparison worksheet
    ws_detail = wb.create_sheet("All Changes")

    # Add headers
    headers = ["Employee ID", "Department", "Section", "Employee Name", "Previous Salary", "Current Salary", "Change",
               "Previous Net Pay", "Current Net Pay", "Change", "Changes"]

    # Style the header row
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")

    header_font = Font(bold=True)
    header_border = Border(bottom=Side(style='thin'))

    for col, header in enumerate(headers, 1):
        cell = ws_detail.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border

    # Add data
    for row, emp in enumerate(comparison_data, 2):
        ws_detail.cell(row=row, column=1, value=emp["id"])
        ws_detail.cell(row=row, column=2, value=emp.get("department", "Unknown"))
        ws_detail.cell(row=row, column=3, value=emp.get("section", "Unknown"))
        ws_detail.cell(row=row, column=4, value=emp.get("name", "Unknown"))

        # Salary data
        prev_salary = emp.get("previous_basic_salary", "N/A")
        curr_salary = emp.get("current_basic_salary", "N/A")
        ws_detail.cell(row=row, column=5, value=prev_salary)
        ws_detail.cell(row=row, column=6, value=curr_salary)

        # Calculate salary change
        if prev_salary != "N/A" and curr_salary != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_salary if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_salary if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
                    ws_detail.cell(row=row, column=7, value=change_text)
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=7, value="N/A")
        else:
            ws_detail.cell(row=row, column=7, value="N/A")

        # Net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")
        ws_detail.cell(row=row, column=8, value=prev_net)
        ws_detail.cell(row=row, column=9, value=curr_net)

        # Calculate net pay change
        if prev_net != "N/A" and curr_net != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_net if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_net if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
                    ws_detail.cell(row=row, column=10, value=change_text)
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=10, value="N/A")
        else:
            ws_detail.cell(row=row, column=10, value="N/A")

        # Changes
        changes_text = "\n".join(emp["changes"])
        ws_detail.cell(row=row, column=11, value=changes_text)
        ws_detail.cell(row=row, column=11).alignment = Alignment(wrapText=True)

    # Format the worksheets
    for ws in [ws_summary, ws_detail]:
        # Adjust column widths
        max_col = 12 if ws == ws_detail else 10
        for col in range(1, max_col):
            max_length = 0
            for row in range(1, ws.max_row + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max_length + 2
            ws.column_dimensions[chr(64 + col)].width = adjusted_width

    # Save Excel file
    wb.save(excel_path)
    print(f"Excel: {excel_path}")

    # Create CSV report
    csv_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.csv")

    # Prepare data for CSV (flatten the changes array)
    csv_data = []
    for emp in comparison_data:
        emp_copy = emp.copy()
        emp_copy["changes"] = "; ".join(emp["changes"])
        csv_data.append(emp_copy)

    df = pd.DataFrame(csv_data)
    df.to_csv(csv_path, index=False)
    print(f"CSV: {csv_path}")

    # Create JSON report
    json_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.json")
    with open(json_path, 'w') as f:
        json.dump(comparison_data, f, indent=2)
    print(f"JSON: {json_path}")

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path
    }

    print("Report generation complete.")
    return reports

def main(prev_file, curr_file, output_dir, id_field="employee_id"):
    """
    Main function to process payroll files and generate comparison reports.

    Args:
        prev_file: Path to the previous month's payroll PDF
        curr_file: Path to the current month's payroll PDF
        output_dir: Directory to save the reports
        id_field: Field to use as the unique identifier for comparison (default: "employee_id")
    """
    print("Starting payroll audit...")
    print(f"Previous file: {prev_file}")
    print(f"Current file: {curr_file}")
    print(f"Output directory: {output_dir}")
    print(f"Using '{id_field}' as the unique identifier")

    # Extract payslips from PDFs
    print("\nExtracting text from previous payroll file...")
    prev_payslips = extract_payslips_from_pdf(prev_file)
    print(f"Found {len(prev_payslips)} potential payslips in previous file")

    print("\nExtracting text from current payroll file...")
    curr_payslips = extract_payslips_from_pdf(curr_file)
    print(f"Found {len(curr_payslips)} potential payslips in current file")

    # Parse payroll data
    print("\nParsing previous payroll data...")
    prev_data = parse_payroll_data(prev_payslips, prev_file)
    print(f"Successfully extracted data for {len(prev_data)} employees from previous file")

    print("\nParsing current payroll data...")
    curr_data = parse_payroll_data(curr_payslips, curr_file)
    print(f"Successfully extracted data for {len(curr_data)} employees from current file")

    # Compare payrolls using the specified identifier
    print("\nComparing payroll data...")
    comparison = compare_payrolls(prev_data, curr_data, id_field)
    print(f"Comparison complete. Found {len(comparison)} entries.")

    # Generate reports
    print("\nGenerating reports...")
    reports = generate_improved_reports(comparison, output_dir)
    print(f"Reports generated successfully:")
    print(f"Excel report: {reports['excel']}")
    print(f"CSV report: {reports['csv']}")
    print(f"JSON report: {reports['json']}")

    print("\nPayroll audit completed successfully.")
    return reports

def sort_pdf_by_identifier(pdf_path, output_path, id_field="employee_id"):
    """
    Sort a PDF file by the specified identifier and save the sorted PDF.

    Args:
        pdf_path: Path to the PDF file to sort
        output_path: Path to save the sorted PDF
        id_field: Field to use for sorting (default: "employee_id")
    """
    print(f"Sorting PDF by {id_field}...")
    print(f"Input file: {pdf_path}")
    print(f"Output file: {output_path}")

    # Extract payslips from PDF
    payslips = extract_payslips_from_pdf(pdf_path)

    # Parse payroll data with advanced layout detection
    employees = parse_payroll_data(payslips, pdf_path)

    # Sort employees by the specified identifier
    if id_field in ["employee_id", "ssf_no", "ghana_card_id", "name", "department", "section"]:
        # Sort by the specified field, handling None values
        sorted_employees = sorted(
            employees,
            key=lambda x: (x.get(id_field) is None, x.get(id_field, "").lower())
        )
    else:
        print(f"Invalid identifier: {id_field}. Using employee_id instead.")
        sorted_employees = sorted(
            employees,
            key=lambda x: (x.get("employee_id") is None, x.get("employee_id", "").lower())
        )

    # Get the page numbers in sorted order
    sorted_pages = [emp.get("page_num", 0) for emp in sorted_employees if emp.get("page_num", 0) > 0]

    # If we couldn't extract page numbers for all employees, we can't sort the PDF
    if len(sorted_pages) < len(employees) / 2:
        print("Warning: Could not determine page numbers for many employees.")
        print("PDF sorting may be incomplete or incorrect.")

    try:
        # Use PyPDF2 to create a new PDF with sorted pages
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            writer = PyPDF2.PdfWriter()

            # Add pages in sorted order
            for page_num in sorted_pages:
                if 1 <= page_num <= len(reader.pages):
                    writer.add_page(reader.pages[page_num - 1])

            # Add any pages that weren't in the sorted list (in case we missed some)
            all_pages = set(range(1, len(reader.pages) + 1))
            missing_pages = all_pages - set(sorted_pages)
            for page_num in missing_pages:
                writer.add_page(reader.pages[page_num - 1])

            # Save the sorted PDF
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)

        print(f"Sorted PDF saved to: {output_path}")
        return output_path

    except Exception as e:
        print(f"Error sorting PDF: {e}")
        return None

def build_data_table(pdf_path, output_path, selected_fields):
    """
    Build a data table from a PDF file with selected fields as columns.

    Args:
        pdf_path: Path to the PDF file
        output_path: Path to save the Excel file
        selected_fields: List of fields to include in the table
    """
    print(f"Building data table from {pdf_path}...")
    print(f"Output file: {output_path}")
    print(f"Selected fields: {selected_fields}")

    # Extract payslips from PDF
    payslips = extract_payslips_from_pdf(pdf_path)

    # Parse payroll data
    employees = parse_payroll_data(payslips)

    # Create a list to hold the table data
    table_data = []

    # Add header row
    header = []
    for field in selected_fields:
        if field == "deductions" or field == "earnings":
            # For deductions and earnings, we need to find all possible keys
            all_keys = set()
            for emp in employees:
                all_keys.update(emp.get(field, {}).keys())

            # Add each key as a separate column
            for key in sorted(all_keys):
                header.append(f"{field.capitalize()}: {key}")
        else:
            # Regular field
            header.append(field.capitalize().replace("_", " "))

    table_data.append(header)

    # Add data rows
    for emp in employees:
        row = []
        for field in selected_fields:
            if field == "deductions" or field == "earnings":
                # For deductions and earnings, add each value
                all_keys = set()
                for e in employees:
                    all_keys.update(e.get(field, {}).keys())

                for key in sorted(all_keys):
                    row.append(emp.get(field, {}).get(key, ""))
            else:
                # Regular field
                row.append(emp.get(field, ""))

        table_data.append(row)

    # Create a DataFrame
    df = pd.DataFrame(table_data[1:], columns=table_data[0])

    # Save to Excel
    df.to_excel(output_path, index=False)

    print(f"Data table saved to: {output_path}")
    return output_path

if __name__ == "__main__":
    import sys
    import argparse

    # Create the main parser
    parser = argparse.ArgumentParser(description='Payroll processing tools')
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Parser for the compare command
    compare_parser = subparsers.add_parser('compare', help='Compare payroll PDFs and generate reports')
    compare_parser.add_argument('prev_file', help='Path to the previous month\'s payroll PDF')
    compare_parser.add_argument('curr_file', help='Path to the current month\'s payroll PDF')
    compare_parser.add_argument('output_dir', help='Directory to save the reports')
    compare_parser.add_argument('--id-field', default='employee_id',
                        choices=['employee_id', 'ssf_no', 'ghana_card_id', 'name', 'department', 'section'],
                        help='Field to use as the unique identifier (default: employee_id)')

    # Parser for the sort command
    sort_parser = subparsers.add_parser('sort', help='Sort a PDF file by a specified identifier')
    sort_parser.add_argument('pdf_file', help='Path to the PDF file to sort')
    sort_parser.add_argument('output_file', help='Path to save the sorted PDF')
    sort_parser.add_argument('--id-field', default='employee_id',
                        choices=['employee_id', 'ssf_no', 'ghana_card_id', 'name', 'department', 'section'],
                        help='Field to use for sorting (default: employee_id)')

    # Parser for the build command
    build_parser = subparsers.add_parser('build', help='Build a data table from a PDF file')
    build_parser.add_argument('pdf_file', help='Path to the PDF file')
    build_parser.add_argument('output_file', help='Path to save the Excel file')
    build_parser.add_argument('--fields', nargs='+', required=True,
                        help='Fields to include in the table (space-separated)')

    # Parse arguments
    if len(sys.argv) > 1:
        args = parser.parse_args()

        if args.command == 'compare':
            main(args.prev_file, args.curr_file, args.output_dir, args.id_field)
        elif args.command == 'sort':
            sort_pdf_by_identifier(args.pdf_file, args.output_file, args.id_field)
        elif args.command == 'build':
            build_data_table(args.pdf_file, args.output_file, args.fields)
        else:
            parser.print_help()
    else:
        parser.print_help()
        sys.exit(1)
