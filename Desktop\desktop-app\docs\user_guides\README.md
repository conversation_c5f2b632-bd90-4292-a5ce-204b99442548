# TEMPLAR PAYROLL AUDITOR

A powerful desktop application for auditing and comparing payroll data.

## Features

- Compare payslips from different periods
- Sort payslips by Employee No.
- Build custom data tables from payslips
- Generate comprehensive reports in Excel, Word, and PDF formats
- Automatically detect and extract Employee No. from payslips

## Installation

### System Requirements

- Windows 10 or later
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space

### Installation Steps

1. Run the installer (`TEMPLAR PAYROLL AUDITOR Setup.exe`)
2. Follow the installation wizard
3. The application will be installed to `C:\TEMPLAR PAYROLL AUDITOR`
4. A desktop shortcut will be created automatically

### First Run

On first run, the application will:

1. Check for required dependencies
2. Install Tesseract OCR if needed
3. Configure system PATH variables

## Usage

1. Launch the application from the desktop shortcut
2. Select the PDF files you want to compare
3. Choose the output directory for reports
4. Click "Start Audit" to begin the comparison
5. View and export reports in various formats

## Architecture

The application consists of:

1. **Electron Frontend**: Provides the user interface and handles file selection
2. **Python Backend**: Processes PDF files and extracts data
3. **SQLite Database**: Stores extracted data and comparison results

## Employee No. Format

The application is configured to recognize Employee No. in the following formats:
- COP#### (e.g., COP2626)
- PW#### (e.g., PW0021)
- SEC#### (e.g., SEC0009)
- E#### (e.g., E0095)

The Employee No. is typically found at the top left of the payslip.

## Troubleshooting

### Missing Dependencies

If you encounter errors related to missing dependencies, try the following:

1. Select "Tools > Install Dependencies" from the menu
2. Restart the application after installation
3. If the issue persists, try installing the dependencies manually as described above

### Employee No. Extraction Issues

If the application is having trouble extracting Employee No. correctly:

1. Make sure Tesseract OCR is installed and in your PATH
2. Check that the Employee No. on your payslips matches one of the supported formats
3. If the issue persists, try selecting a different identifier for comparison

## Building from Source

To build the application from source:

1. Clone the repository
2. Install Node.js and npm
3. Run `npm install` to install dependencies
4. Run `build_production.bat` to create the production build
5. The installer will be created in the `dist` directory

## License

MIT
