@echo off
echo Preparing Python environment for packaging...

REM Create directory for Python embedded
mkdir python-embedded 2>nul

REM Download Python embedded package
echo Downloading Python embedded package...
powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.4/python-3.11.4-embed-amd64.zip' -OutFile 'python-embedded.zip'"

REM Extract Python embedded package
echo Extracting Python embedded package...
powershell -Command "Expand-Archive -Path 'python-embedded.zip' -DestinationPath 'python-embedded' -Force"

REM Download pip
echo Downloading pip...
powershell -Command "Invoke-WebRequest -Uri 'https://bootstrap.pypa.io/get-pip.py' -OutFile 'python-embedded/get-pip.py'"

REM Install pip
echo Installing pip...
python-embedded\python.exe python-embedded\get-pip.py

REM Modify python311._pth to enable site-packages
echo Enabling site-packages...
powershell -Command "(Get-Content python-embedded\python311._pth) -replace '#import site', 'import site' | Set-Content python-embedded\python311._pth"

REM Install required packages
echo Installing required packages...
python-embedded\python.exe -m pip install pymupdf opencv-python pytesseract pillow numpy pandas pdfplumber PyPDF2 matplotlib openpyxl python-docx reportlab

REM Create backend-dist directory
echo Creating backend-dist directory...
mkdir backend-dist 2>nul

REM Copy backend files to backend-dist
echo Copying backend files to backend-dist...
xcopy /E /I /Y ..\backend backend-dist

REM Create vendor directory
echo Creating vendor directory...
mkdir vendor 2>nul
mkdir vendor\tesseract 2>nul

REM Download Tesseract
echo Downloading Tesseract...
powershell -Command "Invoke-WebRequest -Uri 'https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.1.20230401.exe' -OutFile 'tesseract-installer.exe'"

REM Extract Tesseract (silent install to temp directory)
echo Extracting Tesseract...
tesseract-installer.exe /S /D=%CD%\vendor\tesseract

REM Create build directory for icons
echo Creating build directory for icons...
mkdir build 2>nul

REM Create a simple icon for the application
echo Creating icon for the application...
powershell -Command "$iconPath = 'build\icon.ico'; if (-not (Test-Path $iconPath)) { [System.Drawing.Icon]::ExtractAssociatedIcon('C:\Windows\System32\notepad.exe').Save($iconPath) }"

echo Python environment preparation complete!
