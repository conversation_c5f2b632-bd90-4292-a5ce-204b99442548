import os
import json
import pandas as pd
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

def generate_reports(comparison_data, output_dir):
    """Generate reports in various formats."""
    print("Generating reports...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create Excel report
    excel_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.xlsx")
    wb = Workbook()

    # Summary worksheet
    ws_summary = wb.active
    ws_summary.title = "Summary"

    # Add title and timestamp
    ws_summary.cell(row=1, column=1, value="Payroll Comparison Report")
    ws_summary.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_summary.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add statistics
    ws_summary.cell(row=4, column=1, value="Statistics:").font = Font(bold=True)
    ws_summary.cell(row=5, column=1, value="Total employees analyzed:")
    ws_summary.cell(row=5, column=2, value=len(comparison_data))

    # Count employees with changes
    employees_with_changes = sum(1 for emp in comparison_data if any(change != "No significant changes detected" for change in emp["changes"]))
    ws_summary.cell(row=6, column=1, value="Employees with changes:")
    ws_summary.cell(row=6, column=2, value=employees_with_changes)

    # Count new employees
    new_employees = sum(1 for emp in comparison_data if "New employee" in str(emp["changes"][0]))
    ws_summary.cell(row=7, column=1, value="New employees:")
    ws_summary.cell(row=7, column=2, value=new_employees)

    # Count removed employees
    removed_employees = sum(1 for emp in comparison_data if "Employee removed" in str(emp["changes"][0]))
    ws_summary.cell(row=8, column=1, value="Removed employees:")
    ws_summary.cell(row=8, column=2, value=removed_employees)

    # Save Excel file
    wb.save(excel_path)
    print(f"Excel: {excel_path}")

    # Create CSV report
    csv_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.csv")

    # Prepare data for CSV (flatten the changes array)
    csv_data = []
    for emp in comparison_data:
        emp_copy = emp.copy()
        emp_copy["changes"] = "; ".join(emp["changes"])
        csv_data.append(emp_copy)

    df = pd.DataFrame(csv_data)
    df.to_csv(csv_path, index=False)
    print(f"CSV: {csv_path}")

    # Create JSON report
    json_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.json")
    with open(json_path, 'w') as f:
        json.dump(comparison_data, f, indent=2)
    print(f"JSON: {json_path}")

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path
    }

    print("Report generation complete.")
    return reports
