// Custom progress updater script
// This script will be injected into the page to directly update the progress bars

// Create a global object to store progress information
window.progressData = {
  main: {
    current: 0,
    total: 2946,
    percent: 0
  }
};

// Function to directly update the progress bar
function updateProgressBar(type, currentPage, totalPages) {
  // Store the progress data in the main progress object
  window.progressData.main.current = currentPage;
  window.progressData.main.total = totalPages;
  window.progressData.main.percent = Math.floor((currentPage / totalPages) * 100);

  // Format the display text
  const formattedCount = `(${currentPage}/${totalPages})`;
  const percentText = `${window.progressData.main.percent}%`;

  // Get the main progress elements
  const progressBar = document.getElementById('main-progress-bar');
  const progressPercent = document.getElementById('main-progress-percent');
  const progressCount = document.getElementById('main-progress-count');

  // Update the DOM if elements exist
  if (progressBar) {
    // Use setAttribute for more reliable updates
    progressBar.setAttribute('style', `background-color: #00ff00; height: 100%; width: ${window.progressData.main.percent}%; position: absolute; left: 0; top: 0; transition: width 0.5s ease-in-out;`);
    console.log(`Updated main progress bar width to ${window.progressData.main.percent}%`);
  }

  if (progressPercent) {
    progressPercent.textContent = percentText;
    console.log(`Updated main progress percent text to ${percentText}`);
  }

  if (progressCount) {
    progressCount.textContent = formattedCount;
    console.log(`Updated main progress count text to ${formattedCount}`);
  }

  // Force a repaint to ensure the UI updates
  document.body.offsetHeight;
}

// Function to update the process phase
function updateProcessPhase(phase) {
  // Get all stage elements
  const stageExtraction = document.getElementById('stage-extraction');
  const stageParsing = document.getElementById('stage-parsing');
  const stageComparison = document.getElementById('stage-comparison');
  const stageReporting = document.getElementById('stage-reporting');

  // Reset all stages to inactive
  [stageExtraction, stageParsing, stageComparison, stageReporting].forEach(stage => {
    if (stage) {
      stage.classList.remove('active');
      stage.classList.remove('completed');
    }
  });

  // Set the current phase to active and previous phases to completed
  switch(phase) {
    case 'EXTRACTION':
      if (stageExtraction) stageExtraction.classList.add('active');
      break;
    case 'PARSING':
      if (stageExtraction) stageExtraction.classList.add('completed');
      if (stageParsing) stageParsing.classList.add('active');
      break;
    case 'PRE-AUDITING':
      if (stageExtraction) stageExtraction.classList.add('completed');
      if (stageParsing) stageParsing.classList.add('completed');
      if (stageComparison) stageComparison.classList.add('active');
      break;
    case 'REPORTING':
      if (stageExtraction) stageExtraction.classList.add('completed');
      if (stageParsing) stageParsing.classList.add('completed');
      if (stageComparison) stageComparison.classList.add('completed');
      if (stageReporting) stageReporting.classList.add('active');
      break;
  }

  console.log(`Updated process phase to: ${phase}`);
}

// Function to parse messages from the backend
function parseProgressMessage(message) {
  // Check for phase change messages
  const phaseMatch = message.match(/PHASE_CHANGE: (\w+-?\w*) \((\d+)\/(\d+)\)/);
  if (phaseMatch) {
    const phaseName = phaseMatch[1];
    const phaseStep = parseInt(phaseMatch[2]);
    const totalSteps = parseInt(phaseMatch[3]);

    // Update the process phase
    updateProcessPhase(phaseName);

    // Reset progress bar for the new phase
    updateProgressBar('main', 0, 100);
    return;
  }

  // Check for page processing messages
  const pageMatch = message.match(/Processing page (\d+)\/(\d+)/);
  if (pageMatch) {
    const currentPage = parseInt(pageMatch[1]);
    const totalPages = parseInt(pageMatch[2]);

    // Update the main progress bar
    updateProgressBar('main', currentPage, totalPages);
  }

  // Check for total pages information
  const totalPagesMatch = message.match(/Total pages in PDF: (\d+)/);
  if (totalPagesMatch) {
    const totalPages = parseInt(totalPagesMatch[1]);

    // Initialize the main progress bar
    updateProgressBar('main', 0, totalPages);
  }

  // Check for other progress indicators
  const progressMatch = message.match(/Progress: (\d+)\/(\d+)/);
  if (progressMatch) {
    const current = parseInt(progressMatch[1]);
    const total = parseInt(progressMatch[2]);

    // Update the main progress bar
    updateProgressBar('main', current, total);
  }
}

// Create a MutationObserver to watch for changes to the processing details box
function setupProgressObserver() {
  // Get the processing details box
  const processingDetailsBox = document.getElementById('processing-details-box');
  if (!processingDetailsBox) {
    console.error('Processing details box not found!');

    // Try to find it with a more general selector
    const processingDetailsBoxAlt = document.querySelector('.processing-details-box');
    if (!processingDetailsBoxAlt) {
      console.error('Processing details box not found with alternate selector!');
      return;
    }

    console.log('Found processing details box with alternate selector');

    // Create a MutationObserver to watch for changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Process each added node
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('processing-detail-entry')) {
              // Parse the message
              parseProgressMessage(node.textContent);
            } else if (node.nodeType === Node.TEXT_NODE) {
              // Parse text nodes as well
              parseProgressMessage(node.textContent);
            }
          });
        }
      });
    });

    // Start observing the processing details box
    observer.observe(processingDetailsBoxAlt, { childList: true, subtree: true });

    console.log('Progress observer setup complete with alternate selector');
    return;
  }

  // Create a MutationObserver to watch for changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Process each added node
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('processing-detail-entry')) {
            // Parse the message
            parseProgressMessage(node.textContent);
          } else if (node.nodeType === Node.TEXT_NODE) {
            // Parse text nodes as well
            parseProgressMessage(node.textContent);
          }
        });
      }
    });
  });

  // Start observing the processing details box
  observer.observe(processingDetailsBox, { childList: true, subtree: true });

  console.log('Progress observer setup complete');
}

// Function to directly check for backend progress messages
function checkBackendProgress(message) {
  if (message && typeof message === 'string') {
    // Parse the message directly
    parseProgressMessage(message);
    return true;
  }
  return false;
}

// Function to manually initialize progress bars
function initializeProgressBars() {
  // Check if the progress bar elements exist
  const mainProgressBar = document.getElementById('main-progress-bar');
  const mainProgressPercent = document.getElementById('main-progress-percent');
  const mainProgressCount = document.getElementById('main-progress-count');

  console.log('Checking progress bar elements from progress-updater.js:');
  console.log('Main progress bar:', mainProgressBar);
  console.log('Main progress percent:', mainProgressPercent);
  console.log('Main progress count:', mainProgressCount);

  // Initialize with default values if they exist
  if (mainProgressBar && mainProgressPercent && mainProgressCount) {
    mainProgressBar.style.width = '8%';
    mainProgressPercent.textContent = '8%';
    mainProgressCount.textContent = '(0/0)';
    console.log('Initialized main progress bar');
  }
}

// Initialize the progress updater when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Progress updater script loaded');

  // Initialize progress bars immediately
  initializeProgressBars();

  // Set up the observer after a short delay to ensure the DOM is ready
  setTimeout(() => {
    setupProgressObserver();

    // Try initializing again after a delay
    setTimeout(() => {
      initializeProgressBars();
    }, 2000);
  }, 1000);
});

// Expose functions globally
window.updateProgressBar = updateProgressBar;
window.checkBackendProgress = checkBackendProgress;
window.initializeProgressBars = initializeProgressBars;
