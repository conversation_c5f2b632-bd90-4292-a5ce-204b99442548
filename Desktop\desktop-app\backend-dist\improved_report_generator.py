import os
import json
import pandas as pd
import re
import sys
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from docx import Document
from docx.shared import Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_ORIENT, WD_SECTION
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

# HARDCODED EXCLUSION LIST - These items must NEVER appear in extraction or reports
# This is a strict requirement per user specifications
HARDCODED_EXCLUSIONS = [
    "NET PAY",
    "GROSS SALARY",
    "INCOME TAX",
    "BASIC SALARY"
]

def is_excluded_item(item_name):
    """
    Check if an item is in the hardcoded exclusion list.

    Args:
        item_name: The item name to check

    Returns:
        True if the item should be excluded, False otherwise
    """
    if not item_name:
        return False

    item_upper = item_name.upper().strip()

    # Check exact matches first
    for excluded_item in HARDCODED_EXCLUSIONS:
        if item_upper == excluded_item.upper():
            print(f"REPORT EXCLUSION: Hardcoded exclusion applied to '{item_name}' - exact match with '{excluded_item}'")
            return True

    # Check partial matches for variations
    for excluded_item in HARDCODED_EXCLUSIONS:
        excluded_upper = excluded_item.upper()
        if excluded_upper in item_upper or item_upper in excluded_upper:
            print(f"REPORT EXCLUSION: Hardcoded exclusion applied to '{item_name}' - partial match with '{excluded_item}'")
            return True

    return False

def filter_excluded_items_from_dict(items_dict, dict_type="items"):
    """
    Filter out excluded items from a dictionary.

    Args:
        items_dict: Dictionary of items to filter
        dict_type: Type of dictionary for logging purposes

    Returns:
        Filtered dictionary with excluded items removed
    """
    if not items_dict:
        return items_dict

    filtered_dict = {}
    excluded_count = 0

    for key, value in items_dict.items():
        if is_excluded_item(key):
            excluded_count += 1
            print(f"REPORT EXCLUSION: Filtered out excluded {dict_type} item '{key}' from report")
        else:
            filtered_dict[key] = value

    if excluded_count > 0:
        print(f"REPORT EXCLUSION: Filtered out {excluded_count} excluded {dict_type} items from report")

    return filtered_dict

# Import dictionaries for standardization
try:
    import payroll_dictionaries
    print("Imported payroll dictionaries for standardization in report generator")

    # Verify dictionaries are loaded correctly
    earnings_dict = payroll_dictionaries.get_earnings_dictionary()
    deductions_dict = payroll_dictionaries.get_deductions_dictionary()
    loans_dict = payroll_dictionaries.get_loans_dictionary()

    print(f"Loaded dictionaries in report generator - Earnings: {len(earnings_dict)} items, Deductions: {len(deductions_dict)} items, Loans: {len(loans_dict)} items")
    print(f"Earnings dictionary keys: {list(earnings_dict.keys())}")
    print(f"Deductions dictionary keys: {list(deductions_dict.keys())}")
    print(f"Loans dictionary keys: {list(loans_dict.keys())}")
except ImportError as e:
    print(f"Warning: payroll_dictionaries module not found in report generator. Standardization will be disabled. Error: {e}")
    payroll_dictionaries = None
except Exception as e:
    print(f"Error loading dictionaries in report generator: {e}")
    payroll_dictionaries = None

def extract_period_from_payslips(comparison_data, current_month=None, current_year=None, previous_month=None, previous_year=None):
    """Extract period (month and year) from payslip data or use provided values"""
    # If month and year information is provided directly, use it
    if current_month and current_year and previous_month and previous_year:
        curr_period = f"{current_month} {current_year}"
        prev_period = f"{previous_month} {previous_year}"
        print(f"Using provided period information: {prev_period} to {curr_period}")
        return prev_period, curr_period

    # Otherwise, look for period information in the data
    prev_period = "Previous Period"
    curr_period = "Current Period"

    # Try to find period information in the data
    for emp in comparison_data:
        # Check if period is directly available
        if "previous_period" in emp and emp["previous_period"]:
            prev_period = emp["previous_period"]
        if "current_period" in emp and emp["current_period"]:
            curr_period = emp["current_period"]

        # If we found both periods, break
        if prev_period != "Previous Period" and curr_period != "Current Period":
            break

        # Try to extract from changes text
        for change in emp.get("changes", []):
            # Look for patterns like "March 2025" or "April 2025"
            month_year_match = re.search(r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})', change)
            if month_year_match:
                # If we find a month/year in the changes, assume it's the current period
                curr_period = f"{month_year_match.group(1)} {month_year_match.group(2)}"
                # Make an educated guess for previous period (one month before)
                months = ["January", "February", "March", "April", "May", "June",
                          "July", "August", "September", "October", "November", "December"]
                curr_month_idx = months.index(month_year_match.group(1))
                prev_month_idx = (curr_month_idx - 1) % 12
                prev_year = int(month_year_match.group(2))
                if prev_month_idx == 11 and curr_month_idx == 0:  # December to January transition
                    prev_year -= 1
                prev_period = f"{months[prev_month_idx]} {prev_year}"
                break

    # If we still have generic period names and month/year info is partially available, use what we have
    if curr_period == "Current Period" and current_month and current_year:
        curr_period = f"{current_month} {current_year}"
    if prev_period == "Previous Period" and previous_month and previous_year:
        prev_period = f"{previous_month} {previous_year}"

    return prev_period, curr_period

def generate_improved_reports(comparison_data, output_dir, current_month=None, current_year=None, previous_month=None, previous_year=None, report_name=None, report_designation=None):
    """
    Generate improved reports in various formats (Excel, CSV, JSON, Word, PDF).

    This function creates detailed reports with specific worksheets and formats
    as requested by the user.

    Args:
        comparison_data: List of employee comparison data dictionaries
        output_dir: Directory to save the reports
        current_month: Current month name (optional)
        current_year: Current year (optional)
        previous_month: Previous month name (optional)
        previous_year: Previous year (optional)
        report_name: Name of the person generating the report (optional)
        report_designation: Designation of the person generating the report (optional)

    Returns:
        Dictionary with paths to the generated reports
    """

    # Define internal loan types (case-insensitive partial matching)
    internal_loan_types = [
        "SALARY ADVANCE-MINS",
        "BUILDING-MINISTERS",
        "SALARY ADVANCE-STAFF",
        "RENT ADVANCE",
        "SALARY ADVANCE MISSI",
        "SALARY ADVNACE MISSIONS",
        "RENT ADVANCE MISSIONS",
        "STAFF CREDIT UNION LOAN",
        "PENSIONS SALARY ADVA",
        "PENSIONS RENT ADVANCE"
    ]

    # Function to check if a loan type is internal
    def is_internal_loan(loan_type):
        loan_type_upper = loan_type.upper()
        # Handle truncated names by checking if the loan type contains any of the internal loan types
        for internal_type in internal_loan_types:
            # Check for partial matches to handle truncation
            if internal_type.upper() in loan_type_upper or loan_type_upper in internal_type.upper():
                return True

            # Special case for truncated "ADVANCE"
            if "ADV" in loan_type_upper and any(internal_type.upper().replace("ADVANCE", "ADV") in loan_type_upper for internal_type in internal_loan_types):
                return True

            # Special case for truncated "MISSIONS"
            if "MISS" in loan_type_upper and any(internal_type.upper().replace("MISSIONS", "MISS") in loan_type_upper for internal_type in internal_loan_types):
                return True

            # Special case for truncated "LOAN"
            if any(internal_type.upper().replace("LOAN", "") in loan_type_upper for internal_type in internal_loan_types):
                return True

        return False

    # Extract new loans from comparison data
    new_internal_loans = []
    new_external_loans = []

    # Serial number counters
    internal_sn = 1
    external_sn = 1

    for emp in comparison_data:
        emp_id = emp.get("id", "")
        emp_name = emp.get("name", "")
        department = emp.get("department", "")

        # Check for new loans in detailed_changes
        if "detailed_changes" in emp and "loans" in emp["detailed_changes"]:
            for loan in emp["detailed_changes"]["loans"]:
                # Check if this is a new loan
                if "message" in loan and "New loan" in loan.get("clean_message", "") or "added in" in loan.get("clean_message", ""):
                    loan_type = loan.get("type", "").split(" - ")[0] if " - " in loan.get("type", "") else loan.get("type", "")

                    # Get loan details
                    loan_amount = 0
                    current_deduction = 0

                    # Extract loan amount (Balance B/F) and current deduction
                    if "current" in loan and isinstance(loan["current"], str):
                        # Try to parse the current field which might contain the loan details
                        current_str = loan["current"]

                        # Extract Balance B/F
                        balance_match = re.search(r"Balance B/F[:\s]+([0-9,.]+)", current_str)
                        if balance_match:
                            try:
                                loan_amount = float(balance_match.group(1).replace(",", ""))
                            except ValueError:
                                pass

                        # Extract Current Deduction
                        deduction_match = re.search(r"Current Deduction[:\s]+([0-9,.]+)", current_str)
                        if deduction_match:
                            try:
                                current_deduction = float(deduction_match.group(1).replace(",", ""))
                            except ValueError:
                                pass

                    # Create loan entry
                    loan_entry = {
                        "employee_id": emp_id,
                        "employee_name": emp_name,
                        "department": department,
                        "loan_type": loan_type,
                        "loan_amount": loan_amount,
                        "current_deduction": current_deduction,
                        "remarks": ""  # Empty for now, can be filled later if needed
                    }

                    # Categorize as internal or external loan
                    if is_internal_loan(loan_type):
                        loan_entry["sn"] = internal_sn
                        new_internal_loans.append(loan_entry)
                        internal_sn += 1
                    else:
                        loan_entry["sn"] = external_sn
                        new_external_loans.append(loan_entry)
                        external_sn += 1

    # Debug function to print employee data structure
    def debug_employee_data(data):
        print("\n=== DEBUG: EMPLOYEE DATA STRUCTURE ===")
        for i, emp in enumerate(data[:3]):  # Print first 3 employees for debugging
            print(f"\nEmployee {i+1}:")
            print(f"ID: {emp.get('id', 'N/A')}")
            print(f"Name: {emp.get('name', 'N/A')}")
            print(f"Department: {emp.get('department', 'N/A')}")
            print(f"Section: {emp.get('section', 'N/A')}")
        print("=== END DEBUG ===\n")

    # Print debug information about the data structure
    debug_employee_data(comparison_data)
    print("Generating improved reports...")

    # Create a dedicated reports directory structure in the app folder
    # This ensures reports are only saved to the Report Manager, not the upload folder
    app_dir = os.path.dirname(os.path.abspath(__file__))

    # Main reports directory
    reports_dir = os.path.join(app_dir, "reports")

    # Create subdirectories for each tab in the Report Manager
    payroll_audit_dir = os.path.join(reports_dir, "payroll_audit_reports")
    sorted_pdfs_dir = os.path.join(reports_dir, "sorted_pdfs")
    data_tables_dir = os.path.join(reports_dir, "data_tables")

    # Create all directories if they don't exist
    for directory in [reports_dir, payroll_audit_dir, sorted_pdfs_dir, data_tables_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # Use the payroll_audit_dir for audit reports
    output_dir = payroll_audit_dir
    print(f"Saving payroll audit reports to dedicated folder: {payroll_audit_dir}")

    # Create timestamp for unique filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Extract period information from the data, using provided month/year if available
    # Ensure we have actual month/year values, not None
    if current_month and current_year:
        curr_period = f"{current_month} {current_year}"
    else:
        curr_period = "April 2025"  # Default to April 2025 as shown in the screenshot

    if previous_month and previous_year:
        prev_period = f"{previous_month} {previous_year}"
    else:
        prev_period = "March 2025"  # Default to March 2025

    print(f"Using period information: Previous: {prev_period}, Current: {curr_period}")

    # Implement the new file naming convention with run numbers
    # Format: Payroll_Audit_Report_April_2025_Run_01
    # The run number resets if the month changes

    # Get the month and year from the period
    month_year = curr_period.replace(" ", "_")

    # Check if there are existing reports for this month/year
    run_number = 1

    # List all files in the output directory
    try:
        existing_files = os.listdir(output_dir)
        # Filter for files that match our pattern
        pattern = f"Payroll_Audit_Report_{month_year}_Run_"
        matching_files = [f for f in existing_files if pattern in f]

        # Extract the highest run number
        if matching_files:
            run_numbers = []
            for file in matching_files:
                # Extract the run number from the filename
                try:
                    # The run number is the last part of the filename before the extension
                    # e.g., Payroll_Audit_Report_April_2025_Run_01.xlsx -> 01
                    run_part = file.split("Run_")[1].split(".")[0]
                    run_numbers.append(int(run_part))
                except (IndexError, ValueError):
                    continue

            if run_numbers:
                # Get the highest run number and increment by 1
                run_number = max(run_numbers) + 1

        print(f"Using run number: {run_number} for {month_year}")
    except Exception as e:
        print(f"Error determining run number: {e}")
        # Fall back to run number 1
        run_number = 1

    # Format the run number with leading zeros
    run_number_str = f"{run_number:02d}"

    # Create the base filename
    base_filename = f"Payroll_Audit_Report_{month_year}_Run_{run_number_str}"



    # Format the filename with period information
    # Replace spaces with underscores and remove any special characters
    curr_period_clean = curr_period.replace(" ", "_").replace(":", "").replace(",", "")
    prev_period_clean = prev_period.replace(" ", "_").replace(":", "").replace(",", "")
    period_str = f"{curr_period_clean}_vs_{prev_period_clean}"

    # Create Excel report
    excel_path = os.path.join(output_dir, f"{base_filename}.xlsx")
    wb = Workbook()

    # Create the required worksheets
    ws_kpi = wb.active
    ws_kpi.title = "1. CRITICAL KPI SHEET"
    ws_no_changes = wb.create_sheet("2. EMPLOYEES-NO CHANGES")
    ws_new = wb.create_sheet("3. NEW EMPLOYEES")
    ws_removed = wb.create_sheet("4. REMOVED EMPLOYEES")
    ws_internal_loans = wb.create_sheet("5. COP INTERNAL LOANS")
    ws_external_loans = wb.create_sheet("6. EXTERNAL LOANS")

    # Define common styles
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    header_font = Font(bold=True)
    header_border = Border(bottom=Side(style='thin'))

    # Define styles for highlighting changes
    increase_fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Light green
    decrease_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")  # Light red

    # Categorize employees
    employees_with_significant_changes = []
    employees_without_changes = []
    new_employees_list = []
    removed_employees_list = []

    for emp in comparison_data:
        # Check if employee is new or removed
        is_new = any("New employee" in str(change) for change in emp["changes"])
        is_removed = any("Employee removed" in str(change) for change in emp["changes"])

        if is_new:
            new_employees_list.append(emp)
        elif is_removed:
            removed_employees_list.append(emp)
        # Check if employee has changes in focus areas (using the has_focus_area_changes flag)
        elif emp.get("has_focus_area_changes", False) or any(change != "No significant changes detected" for change in emp["changes"]):
            employees_with_significant_changes.append(emp)
        else:
            employees_without_changes.append(emp)

    # ===== CRITICAL KPI SHEET =====
    # Add title
    ws_kpi.cell(row=1, column=1, value=f"Payroll Audit Report: {curr_period} vs {prev_period}")
    ws_kpi.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_kpi.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    ws_kpi.cell(row=3, column=1, value=f"Period: {prev_period} to {curr_period}")

    # Function to standardize field names using dictionaries
    def standardize_field_name(field_name, field_type):
        """
        Standardize a field name using the appropriate dictionary.

        Args:
            field_name: The field name to standardize
            field_type: The type of field ('earnings', 'deductions', or 'loans')

        Returns:
            The standardized field name or the original if no match is found
        """
        if 'payroll_dictionaries' not in globals() or payroll_dictionaries is None:
            return field_name

        try:
            if field_type == 'earnings':
                dictionary = payroll_dictionaries.get_earnings_dictionary()
                standardized = payroll_dictionaries.standardize_item_name(field_name, dictionary)
                if standardized != field_name:
                    print(f"Standardized earnings field in report: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'deductions':
                dictionary = payroll_dictionaries.get_deductions_dictionary()
                standardized = payroll_dictionaries.standardize_item_name(field_name, dictionary)
                if standardized != field_name:
                    print(f"Standardized deductions field in report: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'loans':
                dictionary = payroll_dictionaries.get_loans_dictionary()
                standardized = payroll_dictionaries.standardize_item_name(field_name, dictionary)
                if standardized != field_name:
                    print(f"Standardized loan field in report: {field_name} -> {standardized}")
                return standardized
            else:
                return field_name
        except Exception as e:
            print(f"Error standardizing field name: {e}")
            return field_name

    def standardize_text_with_dictionary(text):
        """
        Standardize item names within a text string using all dictionaries.
        This function searches for variations within the text and replaces them with standard names.

        Args:
            text: The text string to standardize

        Returns:
            The standardized text string
        """
        if not text or 'payroll_dictionaries' not in globals() or payroll_dictionaries is None:
            return text

        try:
            # Get all dictionaries
            earnings_dict = payroll_dictionaries.get_earnings_dictionary()
            deductions_dict = payroll_dictionaries.get_deductions_dictionary()
            loans_dict = payroll_dictionaries.get_loans_dictionary()

            # Create a combined list of all variations and their standard names
            all_variations = []

            # Add earnings variations
            for standard_name, variations in earnings_dict.items():
                for variation in variations:
                    all_variations.append((variation, standard_name, 'earnings'))

            # Add deductions variations
            for standard_name, variations in deductions_dict.items():
                for variation in variations:
                    all_variations.append((variation, standard_name, 'deductions'))

            # Add loans variations
            for standard_name, variations in loans_dict.items():
                for variation in variations:
                    all_variations.append((variation, standard_name, 'loans'))

            # Sort variations by length (longest first) to avoid partial replacements
            all_variations.sort(key=lambda x: len(x[0]), reverse=True)

            # Replace all variations with their standard names
            standardized_text = text
            for variation, standard_name, field_type in all_variations:
                if variation in standardized_text:
                    old_text = standardized_text
                    standardized_text = standardized_text.replace(variation, standard_name)
                    if old_text != standardized_text:
                        print(f"Standardized {field_type} in text: '{variation}' -> '{standard_name}'")

            return standardized_text

        except Exception as e:
            print(f"Error standardizing text with dictionary: {e}")
            return text

    # Collect all unique earnings, deductions, and other fields across all employees
    all_earnings = set()
    all_deductions = set()
    all_loan_fields = set()
    other_fields = set(["Basic Salary", "Taxable Income"])  # Add other important fields

    for emp in comparison_data:
        # Current period items
        if "current_earnings" in emp:
            # Standardize earnings keys and filter out excluded items
            standardized_earnings = {}
            for key, value in emp["current_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                if not is_excluded_item(std_key):
                    standardized_earnings[std_key] = value
                else:
                    print(f"REPORT EXCLUSION: Filtered out excluded current earnings item '{std_key}' (original: '{key}')")
            emp["current_earnings"] = standardized_earnings
            all_earnings.update(emp["current_earnings"].keys())

        if "current_deductions" in emp:
            # Standardize deductions keys and filter out excluded items
            standardized_deductions = {}
            for key, value in emp["current_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                if not is_excluded_item(std_key):
                    standardized_deductions[std_key] = value
                else:
                    print(f"REPORT EXCLUSION: Filtered out excluded current deductions item '{std_key}' (original: '{key}')")
            emp["current_deductions"] = standardized_deductions
            all_deductions.update(emp["current_deductions"].keys())

        # Previous period items
        if "previous_earnings" in emp:
            # Standardize earnings keys and filter out excluded items
            standardized_earnings = {}
            for key, value in emp["previous_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                if not is_excluded_item(std_key):
                    standardized_earnings[std_key] = value
                else:
                    print(f"REPORT EXCLUSION: Filtered out excluded previous earnings item '{std_key}' (original: '{key}')")
            emp["previous_earnings"] = standardized_earnings
            all_earnings.update(emp["previous_earnings"].keys())

        if "previous_deductions" in emp:
            # Standardize deductions keys and filter out excluded items
            standardized_deductions = {}
            for key, value in emp["previous_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                if not is_excluded_item(std_key):
                    standardized_deductions[std_key] = value
                else:
                    print(f"REPORT EXCLUSION: Filtered out excluded previous deductions item '{std_key}' (original: '{key}')")
            emp["previous_deductions"] = standardized_deductions
            all_deductions.update(emp["previous_deductions"].keys())

        # Current loan details
        if "loan_details" in emp:
            # Standardize loan types
            standardized_loan_details = {}
            for loan_type, loan_data in emp["loan_details"].items():
                std_loan_type = standardize_field_name(loan_type, 'loans')
                standardized_loan_details[std_loan_type] = loan_data

                all_loan_fields.add(f"LOAN: {std_loan_type}")
                if "balance_bf" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - BALANCE B/F")
                if "current_deduction" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - CURRENT DEDUCTION")
                if "outstanding_balance" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - OUTST. BALANCE")

            emp["loan_details"] = standardized_loan_details

        # Previous loan details
        if "previous_loan_details" in emp:
            # Standardize loan types
            standardized_prev_loan_details = {}
            for loan_type, loan_data in emp["previous_loan_details"].items():
                std_loan_type = standardize_field_name(loan_type, 'loans')
                standardized_prev_loan_details[std_loan_type] = loan_data

                all_loan_fields.add(f"LOAN: {std_loan_type}")
                if "balance_bf" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - BALANCE B/F")
                if "current_deduction" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - CURRENT DEDUCTION")
                if "outstanding_balance" in loan_data:
                    all_loan_fields.add(f"LOAN: {std_loan_type} - OUTST. BALANCE")

            emp["previous_loan_details"] = standardized_prev_loan_details

    # Sort the items alphabetically
    all_earnings = sorted(all_earnings)
    all_deductions = sorted(all_deductions)
    all_loan_fields = sorted(all_loan_fields)
    other_fields = sorted(other_fields)

    # Print debug information about standardized fields
    print(f"\nStandardized fields for reports:")
    print(f"Earnings fields: {all_earnings}")
    print(f"Deductions fields: {all_deductions}")
    print(f"Loan fields: {all_loan_fields}")
    print(f"Other fields: {other_fields}\n")

    # Final standardization of all data before report generation
    # This ensures that any item names that weren't caught earlier are standardized
    print("Performing final standardization of all data before report generation...")
    for emp in comparison_data:
        # Standardize current earnings one more time and filter excluded items
        if "current_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["current_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                if not is_excluded_item(std_key):
                    standardized_earnings[std_key] = value
            emp["current_earnings"] = standardized_earnings

        # Standardize previous earnings one more time and filter excluded items
        if "previous_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["previous_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                if not is_excluded_item(std_key):
                    standardized_earnings[std_key] = value
            emp["previous_earnings"] = standardized_earnings

        # Standardize current deductions one more time and filter excluded items
        if "current_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["current_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                if not is_excluded_item(std_key):
                    standardized_deductions[std_key] = value
            emp["current_deductions"] = standardized_deductions

        # Standardize previous deductions one more time and filter excluded items
        if "previous_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["previous_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                if not is_excluded_item(std_key):
                    standardized_deductions[std_key] = value
            emp["previous_deductions"] = standardized_deductions

        # Standardize current loan details one more time
        if "loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["loan_details"] = standardized_loans

        # Standardize previous loan details one more time
        if "previous_loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["previous_loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["previous_loan_details"] = standardized_loans

        # Standardize detailed changes one more time and filter excluded items
        if "detailed_changes" in emp:
            # Standardize earnings changes and filter excluded items
            if "earnings" in emp["detailed_changes"]:
                filtered_earnings_changes = []
                for change in emp["detailed_changes"]["earnings"]:
                    if "type" in change:
                        std_type = standardize_field_name(change["type"], 'earnings')
                        if not is_excluded_item(std_type):
                            change["type"] = std_type
                            filtered_earnings_changes.append(change)
                        else:
                            print(f"REPORT EXCLUSION: Filtered out excluded earnings change for '{std_type}' (original: '{change['type']}')")
                emp["detailed_changes"]["earnings"] = filtered_earnings_changes

            # Standardize deductions changes and filter excluded items
            if "deductions" in emp["detailed_changes"]:
                filtered_deductions_changes = []
                for change in emp["detailed_changes"]["deductions"]:
                    if "type" in change:
                        std_type = standardize_field_name(change["type"], 'deductions')
                        if not is_excluded_item(std_type):
                            change["type"] = std_type
                            filtered_deductions_changes.append(change)
                        else:
                            print(f"REPORT EXCLUSION: Filtered out excluded deductions change for '{std_type}' (original: '{change['type']}')")
                emp["detailed_changes"]["deductions"] = filtered_deductions_changes

            # Standardize loans changes
            if "loans" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["loans"]:
                    if "type" in change:
                        loan_type = change["type"]
                        # Handle loan types with additional info (e.g., "LOAN TYPE - BALANCE B/F")
                        if " - " in loan_type:
                            loan_type, additional = loan_type.split(" - ", 1)
                            std_loan_type = standardize_field_name(loan_type, 'loans')
                            change["type"] = f"{std_loan_type} - {additional}"
                        else:
                            change["type"] = standardize_field_name(loan_type, 'loans')

    print("Final standardization complete.")

    # Create headers for KPI sheet
    kpi_headers = ["EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT"]

    # Add GROSS SALARY headers
    kpi_headers.extend([f"GROSS SALARY {prev_period}", f"GROSS SALARY {curr_period}", "% CHANGE"])

    # Add NET PAY headers
    kpi_headers.extend([f"NET PAY {prev_period}", f"NET PAY {curr_period}", "% CHANGE"])

    # Add TOTAL DEDUCTIONS headers
    kpi_headers.extend([f"TOTAL DEDUCTIONS {prev_period}", f"TOTAL DEDUCTIONS {curr_period}", "% CHANGE"])

    # Add OTHER FIELDS headers
    for field in other_fields:
        kpi_headers.extend([f"{field} {prev_period}", f"{field} {curr_period}", "% CHANGE"])

    # Add EARNINGS headers
    for earning in all_earnings:
        kpi_headers.extend([f"{earning} {prev_period}", f"{earning} {curr_period}", "% CHANGE"])

    # Add DEDUCTIONS headers
    for deduction in all_deductions:
        kpi_headers.extend([f"{deduction} {prev_period}", f"{deduction} {curr_period}", "% CHANGE"])

    # Add LOAN FIELDS headers
    for loan_field in all_loan_fields:
        kpi_headers.extend([f"{loan_field} {prev_period}", f"{loan_field} {curr_period}", "% CHANGE"])

    # Apply headers to KPI worksheet
    for col, header in enumerate(kpi_headers, 1):
        cell = ws_kpi.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border

    # Function to safely convert string amounts to float
    def safe_float(value):
        if value is None or value == "N/A":
            return 0.0
        if isinstance(value, (int, float)):
            return float(value)
        try:
            # Remove commas and other non-numeric characters except decimal point
            clean_value = ''.join(c for c in str(value) if c.isdigit() or c == '.')
            return float(clean_value) if clean_value else 0.0
        except (ValueError, TypeError):
            return 0.0

    # Function to calculate percentage change
    def calc_percent_change(prev_val, curr_val):
        prev_val = safe_float(prev_val)
        curr_val = safe_float(curr_val)

        if prev_val == 0:
            return "N/A" if curr_val == 0 else "New"

        percent_change = ((curr_val - prev_val) / prev_val) * 100
        # Round to whole number with no decimal places as requested
        return f"{'+' if percent_change >= 0 else ''}{int(percent_change)}%"

    # Add employee data to KPI sheet
    row_idx = 5
    for emp in comparison_data:
        # Skip new and removed employees for KPI sheet
        is_new = any("New employee" in str(change) for change in emp["changes"])
        is_removed = any("Employee removed" in str(change) for change in emp["changes"])

        if is_new or is_removed:
            continue

        col_idx = 1

        # Basic employee info
        ws_kpi.cell(row=row_idx, column=col_idx, value=emp["id"])
        col_idx += 1

        ws_kpi.cell(row=row_idx, column=col_idx, value=emp.get("name", ""))
        col_idx += 1

        ws_kpi.cell(row=row_idx, column=col_idx, value=emp.get("department", ""))
        col_idx += 1

        # GROSS SALARY
        prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
        curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))

        ws_kpi.cell(row=row_idx, column=col_idx, value=prev_gross)
        col_idx += 1

        ws_kpi.cell(row=row_idx, column=col_idx, value=curr_gross)
        col_idx += 1

        # % CHANGE for GROSS SALARY
        percent_change = calc_percent_change(prev_gross, curr_gross)
        cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

        # Color code the cell based on change
        if percent_change != "N/A" and percent_change != "New":
            if percent_change.startswith("+"):
                cell.fill = increase_fill
            elif not percent_change.startswith("+"):
                cell.fill = decrease_fill

        col_idx += 1

        # NET PAY
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")

        ws_kpi.cell(row=row_idx, column=col_idx, value=prev_net)
        col_idx += 1

        ws_kpi.cell(row=row_idx, column=col_idx, value=curr_net)
        col_idx += 1

        # % CHANGE for NET PAY
        percent_change = calc_percent_change(prev_net, curr_net)
        cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

        # Color code the cell based on change
        if percent_change != "N/A" and percent_change != "New":
            if percent_change.startswith("+"):
                cell.fill = increase_fill
            elif not percent_change.startswith("+"):
                cell.fill = decrease_fill

        col_idx += 1

        # TOTAL DEDUCTIONS
        # Calculate total deductions for previous period
        prev_deductions = emp.get("previous_deductions", {})
        prev_total_deductions = sum(safe_float(val) for val in prev_deductions.values())

        # Calculate total deductions for current period
        curr_deductions = emp.get("current_deductions", {})
        curr_total_deductions = sum(safe_float(val) for val in curr_deductions.values())

        ws_kpi.cell(row=row_idx, column=col_idx, value=f"{prev_total_deductions:.2f}")
        col_idx += 1

        ws_kpi.cell(row=row_idx, column=col_idx, value=f"{curr_total_deductions:.2f}")
        col_idx += 1

        # % CHANGE for TOTAL DEDUCTIONS
        percent_change = calc_percent_change(prev_total_deductions, curr_total_deductions)
        cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

        # Color code the cell based on change
        if percent_change != "N/A" and percent_change != "New":
            if percent_change.startswith("+"):
                cell.fill = increase_fill
            elif not percent_change.startswith("+"):
                cell.fill = decrease_fill

        col_idx += 1

        # OTHER FIELDS
        for field in other_fields:
            # Handle Basic Salary
            if field == "Basic Salary":
                prev_val = emp.get("previous_basic_salary", "N/A")
                curr_val = emp.get("current_basic_salary", "N/A")
            # Handle Taxable Income
            elif field == "Taxable Income":
                prev_val = emp.get("previous_taxable_income", "N/A")
                curr_val = emp.get("current_taxable_income", "N/A")
            else:
                prev_val = "N/A"
                curr_val = "N/A"

            ws_kpi.cell(row=row_idx, column=col_idx, value=prev_val)
            col_idx += 1

            ws_kpi.cell(row=row_idx, column=col_idx, value=curr_val)
            col_idx += 1

            # % CHANGE for this field
            percent_change = calc_percent_change(prev_val, curr_val)
            cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

            # Color code the cell based on change
            if percent_change != "N/A" and percent_change != "New":
                if percent_change.startswith("+"):
                    cell.fill = increase_fill
                elif not percent_change.startswith("+"):
                    cell.fill = decrease_fill

            col_idx += 1

        # EARNINGS
        prev_earnings = emp.get("previous_earnings", {})
        curr_earnings = emp.get("current_earnings", {})

        for earning in all_earnings:
            prev_val = prev_earnings.get(earning, "N/A")
            curr_val = curr_earnings.get(earning, "N/A")

            ws_kpi.cell(row=row_idx, column=col_idx, value=prev_val)
            col_idx += 1

            ws_kpi.cell(row=row_idx, column=col_idx, value=curr_val)
            col_idx += 1

            # % CHANGE for this earning
            percent_change = calc_percent_change(prev_val, curr_val)
            cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

            # Color code the cell based on change
            if percent_change != "N/A" and percent_change != "New":
                if percent_change.startswith("+"):
                    cell.fill = increase_fill
                elif not percent_change.startswith("+"):
                    cell.fill = decrease_fill

            col_idx += 1

        # DEDUCTIONS
        for deduction in all_deductions:
            prev_val = prev_deductions.get(deduction, "N/A")
            curr_val = curr_deductions.get(deduction, "N/A")

            ws_kpi.cell(row=row_idx, column=col_idx, value=prev_val)
            col_idx += 1

            ws_kpi.cell(row=row_idx, column=col_idx, value=curr_val)
            col_idx += 1

            # % CHANGE for this deduction
            percent_change = calc_percent_change(prev_val, curr_val)
            cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

            # Color code the cell based on change
            if percent_change != "N/A" and percent_change != "New":
                if percent_change.startswith("+"):
                    cell.fill = increase_fill
                elif not percent_change.startswith("+"):
                    cell.fill = decrease_fill

            col_idx += 1

        # LOAN FIELDS
        prev_loans = emp.get("previous_loan_details", {})
        curr_loans = emp.get("loan_details", {})

        for loan_field in all_loan_fields:
            # Extract loan type and field from the combined field name
            if " - " in loan_field:
                loan_type, field_name = loan_field.replace("LOAN: ", "").split(" - ", 1)
            else:
                loan_type = loan_field.replace("LOAN: ", "")
                field_name = "outstanding_balance"  # Default field

            # Get previous and current values
            prev_loan = prev_loans.get(loan_type, {})
            curr_loan = curr_loans.get(loan_type, {})

            # Map field names to the actual keys in the loan dictionaries
            field_map = {
                "BALANCE B/F": "balance_bf",
                "CURRENT DEDUCTION": "current_deduction",
                "OUTST. BALANCE": "outstanding_balance"
            }

            # Get the actual field key
            field_key = field_map.get(field_name, "outstanding_balance")

            # Get values
            prev_val = prev_loan.get(field_key, "N/A")
            curr_val = curr_loan.get(field_key, "N/A")

            ws_kpi.cell(row=row_idx, column=col_idx, value=prev_val)
            col_idx += 1

            ws_kpi.cell(row=row_idx, column=col_idx, value=curr_val)
            col_idx += 1

            # % CHANGE for this loan field
            percent_change = calc_percent_change(prev_val, curr_val)
            cell = ws_kpi.cell(row=row_idx, column=col_idx, value=percent_change)

            # Color code the cell based on change
            if percent_change != "N/A" and percent_change != "New":
                if percent_change.startswith("+"):
                    cell.fill = increase_fill
                elif not percent_change.startswith("+"):
                    cell.fill = decrease_fill

            col_idx += 1

        row_idx += 1

    # ===== OTHER SHEETS =====
    # Define headers for other sheets
    other_headers = ["EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "ITEMIZED CHANGE"]

    # Apply headers to other worksheets
    other_worksheets = [ws_no_changes, ws_new, ws_removed]
    for ws in other_worksheets:
        # Add title
        ws.cell(row=1, column=1, value=f"Payroll Audit Report: {curr_period} vs {prev_period}")
        ws.cell(row=1, column=1).font = Font(bold=True, size=14)
        ws.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        ws.cell(row=3, column=1, value=f"Period: {prev_period} to {curr_period}")

        # Add headers
        for col, header in enumerate(other_headers, 1):
            cell = ws.cell(row=4, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = header_border

    # Function to add employee data to other worksheets
    def add_employee_data(worksheet, employees, start_row=5):
        for idx, emp in enumerate(employees, start_row):
            # Basic employee info
            worksheet.cell(row=idx, column=1, value=emp["id"])
            worksheet.cell(row=idx, column=2, value=emp.get("name", ""))
            worksheet.cell(row=idx, column=3, value=emp.get("department", ""))

            # Itemized changes
            significant_changes = [change for change in emp["changes"] if change != "No significant changes detected"]

            if significant_changes:
                changes_text = "\n".join(significant_changes)
            else:
                changes_text = "No changes"

            worksheet.cell(row=idx, column=4, value=changes_text)
            worksheet.cell(row=idx, column=4).alignment = Alignment(wrapText=True)

    # Add data to each worksheet
    add_employee_data(ws_no_changes, employees_without_changes)
    add_employee_data(ws_new, new_employees_list)
    add_employee_data(ws_removed, removed_employees_list)

    # Add loan tables to Excel sheets
    # Define loan table headers
    loan_headers = ["SN", "EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "LOAN TYPE", "LOAN AMOUNT", "CURRENT DEDUCTION", "REMARKS"]

    # Set landscape orientation for loan sheets
    # Use string value instead of constant since PageSetup import is problematic
    ws_internal_loans.page_setup.orientation = 'landscape'
    ws_external_loans.page_setup.orientation = 'landscape'

    # Add title to internal loans sheet
    ws_internal_loans.cell(row=1, column=1, value=f"COP INTERNAL LOANS - {curr_period}")
    ws_internal_loans.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_internal_loans.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add headers to internal loans sheet
    for col, header in enumerate(loan_headers, 1):
        cell = ws_internal_loans.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border

    # Add data to internal loans sheet
    for idx, loan in enumerate(new_internal_loans, 5):
        ws_internal_loans.cell(row=idx, column=1, value=loan["sn"])
        ws_internal_loans.cell(row=idx, column=2, value=loan["employee_id"])
        ws_internal_loans.cell(row=idx, column=3, value=loan["employee_name"])
        ws_internal_loans.cell(row=idx, column=4, value=loan["department"])
        ws_internal_loans.cell(row=idx, column=5, value=loan["loan_type"])
        ws_internal_loans.cell(row=idx, column=6, value=loan["loan_amount"])
        ws_internal_loans.cell(row=idx, column=7, value=loan["current_deduction"])
        ws_internal_loans.cell(row=idx, column=8, value=loan["remarks"])

    # Add title to external loans sheet
    ws_external_loans.cell(row=1, column=1, value=f"EXTERNAL LOANS - {curr_period}")
    ws_external_loans.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_external_loans.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add headers to external loans sheet
    for col, header in enumerate(loan_headers, 1):
        cell = ws_external_loans.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border

    # Add data to external loans sheet
    for idx, loan in enumerate(new_external_loans, 5):
        ws_external_loans.cell(row=idx, column=1, value=loan["sn"])
        ws_external_loans.cell(row=idx, column=2, value=loan["employee_id"])
        ws_external_loans.cell(row=idx, column=3, value=loan["employee_name"])
        ws_external_loans.cell(row=idx, column=4, value=loan["department"])
        ws_external_loans.cell(row=idx, column=5, value=loan["loan_type"])
        ws_external_loans.cell(row=idx, column=6, value=loan["loan_amount"])
        ws_external_loans.cell(row=idx, column=7, value=loan["current_deduction"])
        ws_external_loans.cell(row=idx, column=8, value=loan["remarks"])

    # Format all worksheets
    all_worksheets = [ws_kpi, ws_no_changes, ws_new, ws_removed, ws_internal_loans, ws_external_loans]
    for ws in all_worksheets:
        # Adjust column widths
        for col in range(1, ws.max_column + 1):
            max_length = 0
            for row in range(4, ws.max_row + 1):  # Start from header row
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value).split('\n')[0]))  # Take first line for width calculation
            adjusted_width = min(max_length + 2, 50)  # Cap width at 50 characters
            ws.column_dimensions[get_column_letter(col)].width = adjusted_width

    # Enable filtering for all worksheets
    for ws in all_worksheets:
        if ws.max_row > 4:  # Only add filter if there's data
            ws.auto_filter.ref = f"A4:{get_column_letter(ws.max_column)}{ws.max_row}"

    # Freeze panes for better navigation
    for ws in all_worksheets:
        ws.freeze_panes = "A5"  # Freeze header row

    # Save Excel file
    try:
        wb.save(excel_path)
        print(f"Excel report saved to: {excel_path}")
    except Exception as e:
        print(f"Error saving Excel file: {e}")
        excel_path = None

    # Create CSV report
    csv_path = os.path.join(output_dir, f"{base_filename}.csv")

    # Prepare data for CSV with the requested column headings
    csv_data = []
    for emp in comparison_data:
        # Get gross salary data
        prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
        curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))

        # Get net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")

        # Calculate change
        change_text = "N/A"
        is_new = any("New employee" in str(change) for change in emp.get("changes", []))
        is_removed = any("Employee removed" in str(change) for change in emp.get("changes", []))

        if prev_net != "N/A" and curr_net != "N/A" and not is_new and not is_removed:
            try:
                prev_val = float(''.join(c for c in str(prev_net) if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in str(curr_net) if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
            except (ValueError, TypeError):
                pass
        elif is_new:
            change_text = "New Employee"
        elif is_removed:
            change_text = "Employee Removed"

        # Get all changes without filtering
        significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

        # Include all changes as per requirements
        itemized_changes = "; ".join(significant_changes) if significant_changes else "No changes"

        emp_copy = {
            "EMPLOYEE NO.": emp["id"],
            "DEPARTMENT": emp.get("department", "ALL DEPARTMENTS"),
            "GROSS Previous Period": prev_gross,
            "GROSS Current Period": curr_gross,
            "NET Previous Period": prev_net,
            "NET Current Period": curr_net,
            "CHANGE (increase or decrease)": change_text,
            "ITEMIZED CHANGE": itemized_changes
        }
        csv_data.append(emp_copy)

    try:
        df = pd.DataFrame(csv_data)
        df.to_csv(csv_path, index=False)
        print(f"CSV report saved to: {csv_path}")
    except Exception as e:
        print(f"Error saving CSV file: {e}")
        csv_path = None

    # Create JSON report
    json_path = os.path.join(output_dir, f"{base_filename}.json")

    # Prepare data for JSON with the requested format
    json_data = []
    for emp in comparison_data:
        # Get gross salary data
        prev_gross = emp.get("previous_gross_salary", emp.get("previous_basic_salary", "N/A"))
        curr_gross = emp.get("current_gross_salary", emp.get("current_basic_salary", "N/A"))

        # Get net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")

        # Calculate change
        change_text = "N/A"
        is_new = any("New employee" in str(change) for change in emp.get("changes", []))
        is_removed = any("Employee removed" in str(change) for change in emp.get("changes", []))

        if prev_net != "N/A" and curr_net != "N/A" and not is_new and not is_removed:
            try:
                prev_val = float(''.join(c for c in str(prev_net) if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in str(curr_net) if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
            except (ValueError, TypeError):
                pass
        elif is_new:
            change_text = "New Employee"
        elif is_removed:
            change_text = "Employee Removed"

        # Get all changes without filtering
        significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

        # Include all changes as per requirements
        filtered_changes = significant_changes

        json_emp = {
            "EMPLOYEE NO.": emp["id"],
            "DEPARTMENT": emp.get("department", "ALL DEPARTMENTS"),
            "GROSS Previous Period": prev_gross,
            "GROSS Current Period": curr_gross,
            "NET Previous Period": prev_net,
            "NET Current Period": curr_net,
            "CHANGE (increase or decrease)": change_text,
            "ITEMIZED CHANGE": filtered_changes
        }
        json_data.append(json_emp)

    try:
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        print(f"JSON report saved to: {json_path}")
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        json_path = None

    # Create Word report
    word_path = os.path.join(output_dir, f"{base_filename}.docx")

    # Final standardization of all data before Word report generation
    print("Performing final standardization of all data before Word report generation...")
    for emp in comparison_data:
        # Standardize current earnings one more time
        if "current_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["current_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["current_earnings"] = standardized_earnings

        # Standardize previous earnings one more time
        if "previous_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["previous_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["previous_earnings"] = standardized_earnings

        # Standardize current deductions one more time
        if "current_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["current_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["current_deductions"] = standardized_deductions

        # Standardize previous deductions one more time
        if "previous_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["previous_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["previous_deductions"] = standardized_deductions

        # Standardize current loan details one more time
        if "loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["loan_details"] = standardized_loans

        # Standardize previous loan details one more time
        if "previous_loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["previous_loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["previous_loan_details"] = standardized_loans

        # Standardize detailed changes one more time
        if "detailed_changes" in emp:
            # Standardize earnings changes
            if "earnings" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["earnings"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'earnings')

            # Standardize deductions changes
            if "deductions" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["deductions"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'deductions')

            # Standardize loans changes
            if "loans" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["loans"]:
                    if "type" in change:
                        loan_type = change["type"]
                        # Handle loan types with additional info (e.g., "LOAN TYPE - BALANCE B/F")
                        if " - " in loan_type:
                            loan_type, additional = loan_type.split(" - ", 1)
                            std_loan_type = standardize_field_name(loan_type, 'loans')
                            change["type"] = f"{std_loan_type} - {additional}"
                        else:
                            change["type"] = standardize_field_name(loan_type, 'loans')

    print("Final standardization for Word report complete.")

    try:
        # Create a new Word document
        doc = Document()

        # Add page numbers and footer
        # Access the sections collection
        sections = doc.sections
        for section in sections:
            # Access the footer
            footer = section.footer

            # Add a paragraph to the footer
            footer_para = footer.paragraphs[0]

            # Add page number field - use a simpler approach that doesn't rely on field codes
            footer_para.text = "Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025"

            # Note: We're using a static "Page 1" since we can't use field codes
            # In a real implementation, we would need to use a different approach
            # to add dynamic page numbers

            # Center align the footer text
            footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Set document properties with the exact format from the screenshot
        title = doc.add_heading(f'PAYROLL PRE-AUDIT REPORT: {curr_period}', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Make sure the title is bold
        for run in title.runs:
            run.bold = True

        # Add a horizontal line under the title
        title_paragraph = title.paragraph_format
        title_paragraph.space_after = Pt(6)

        # Add timestamp, period, and signature information in a table
        info_table = doc.add_table(rows=2, cols=2)
        info_table.style = 'Table Grid'
        info_table.autofit = True

        # Left column - Generated date and period
        info_table.cell(0, 0).text = f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        info_table.cell(1, 0).text = f'Period: {curr_period}'

        # Right column - Signature information if provided
        if report_name and report_designation:
            info_table.cell(0, 1).text = f'Generated by: {report_name}'
            info_table.cell(1, 1).text = f'Designation: {report_designation}'

        # Format the table - remove borders and set alignment
        for row in info_table.rows:
            for cell in row.cells:
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(10)

        # Add some space after the table
        doc.add_paragraph()

        # Add statistics section with the exact format from the screenshot
        stats_heading = doc.add_heading('Statistics', level=1)
        stats_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Make sure the heading is bold
        for run in stats_heading.runs:
            run.bold = True

        stats_table = doc.add_table(rows=4, cols=2)
        stats_table.style = 'Table Grid'

        # Align the table to the left
        stats_table.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Set table width to match the screenshot
        for cell in stats_table.columns[0].cells:
            cell.width = Inches(3)
        for cell in stats_table.columns[1].cells:
            cell.width = Inches(1.5)

        # Count employees with changes
        employees_with_changes_count = len(employees_with_significant_changes)
        new_employees_count = len(new_employees_list)
        removed_employees_count = len(removed_employees_list)

        # Add statistics data
        stats_rows = [
            ("Total employees analyzed:", str(len(comparison_data))),
            ("Employees with changes:", str(employees_with_changes_count)),
            ("New employees:", str(new_employees_count)),
            ("Removed employees:", str(removed_employees_count))
        ]

        for i, (label, value) in enumerate(stats_rows):
            stats_table.cell(i, 0).text = label
            stats_table.cell(i, 1).text = value

            # Apply formatting to match the screenshot
            stats_table.cell(i, 0).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
            stats_table.cell(i, 1).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Add itemized changes section with the exact format from the screenshot
        itemized_heading = doc.add_heading('Itemized Changes', level=1)
        itemized_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Make sure the heading is bold
        for run in itemized_heading.runs:
            run.bold = True

        # Add a single department heading for all employees
        dept_heading = doc.add_paragraph('Department: All Departments', style='Heading 2')
        dept_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
        dept_heading.paragraph_format.space_after = Pt(6)

        # Make sure the department heading is bold
        for run in dept_heading.runs:
            run.bold = True

        # Process all employees with changes
        for emp in comparison_data:
            # Skip employees with no changes
            if all(change == "No significant changes detected" for change in emp.get("changes", [])):
                continue

            # Get all changes without filtering
            significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

            # Process changes to ensure they have proper formatting
            processed_changes = []
            for change in significant_changes:
                # Remove employee identifiers if present
                if ": " in change and "; " in change:
                    change = change.split("; ", 1)[1]

                # Ensure month information is present and formatted correctly
                if " in " not in change:
                    change = change + f" in {curr_period}"
                elif " in None" in change:
                    change = change.replace(" in None", f" in {curr_period}")

                # Fix period placeholders
                change = change.replace("Current Period", curr_period)
                change = change.replace("Previous Period", prev_period)
                change = change.replace("current period", curr_period)
                change = change.replace("previous period", prev_period)

                processed_changes.append(change)

            significant_changes = processed_changes

            if significant_changes or "previous_gross_salary" in emp or "previous_net_pay" in emp:
                # Format employee ID with department and name only
                # Example: "COP0209: ABUAKWA AREA - MINISTERS - APPIAH-AIDOO A"
                emp_id = emp.get("id", "")

                # Get department directly from the employee data
                # Department is a critical field that must be extracted for all employees
                # Department: ABUAKWA AREA - MINISTERS (from the payslip)
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Get employee name
                name = emp.get("name", "")

                # Print debug information
                print(f"DEBUG - Employee ID: {emp_id}, Department: {dept}, Name: {name}")

                # CRITICAL FIX: Make sure we're not using section as name
                section = emp.get("section", "")
                if name and section and name == section:
                    print(f"DEBUG - Name '{name}' is same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for employee {emp_id} - this should not happen")

                # Only add the name if it's valid and not the same as section
                if name and name != "Unknown" and (not section or name != section):
                    # Add the name after the department
                    emp_header = f"{emp_header} - {name}"

                # Print the final header for debugging
                print(f"DEBUG - Final employee header: {emp_header}")

                doc.add_paragraph(emp_header, style='List Bullet')

                # Initialize change index
                change_index = 1

                # Add all changes
                for change in significant_changes:
                    # Clean up the change message to match the expected format
                    clean_change = change
                    if ":" in clean_change and ";" in clean_change:
                        clean_change = clean_change.split(";", 1)[1].strip()

                    # Standardize the text using the dictionary
                    # This is where we identify and replace variations within the text
                    standardized_change = standardize_text_with_dictionary(clean_change)

                    # Add the standardized change to the document
                    p = doc.add_paragraph(f"{change_index}. {standardized_change}")
                    p.paragraph_format.left_indent = Inches(0.5)
                    change_index += 1

        # Add new employees section if any
        if new_employees_list:
            doc.add_heading('New Employees', level=1)
            for emp in new_employees_list:
                emp_id = emp.get("id", "")

                # Get department directly from the employee data
                # Department is a critical field that must be extracted for all employees
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for new employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Get employee name
                name = emp.get("name", "")

                # Print debug information
                print(f"DEBUG - New Employee ID: {emp_id}, Department: {dept}, Name: {name}")

                # If name is missing or "Unknown", try to get it from the detailed_changes
                if not name or name == "Unknown":
                    # Check if we have name info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "employee_name" in change and change["employee_name"]:
                                        name = change["employee_name"]
                                        print(f"DEBUG - Found name in detailed_changes: {name}")
                                        break
                                if name and name != "Unknown":
                                    break

                # If department is missing or "Unknown", try to get it from the detailed_changes
                if not dept or dept == "Unknown":
                    # Check if we have department info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        break
                                if dept and dept != "Unknown":
                                    break

                # Make sure we're not using section as name
                section = emp.get("section", "")
                if name == section:
                    print(f"DEBUG - Name is same as section, trying to find better name")
                    # Try to extract name from the changes
                    if "changes" in emp and emp["changes"]:
                        for change in emp["changes"]:
                            if ": " in change and "; " in change:
                                parts = change.split("; ", 1)
                                if ": " in parts[0]:
                                    id_dept = parts[0].split(": ", 1)
                                    if len(id_dept) > 1 and id_dept[0] == emp_id:
                                        # This might contain the department
                                        dept_candidate = id_dept[1]
                                        if dept_candidate != section:
                                            dept = dept_candidate
                                            print(f"DEBUG - Found better department: {dept}")

                # CRITICAL FIX: Make sure we're not using section as name
                # Only clear the name if it's exactly the same as section (not just containing it)
                # This prevents clearing valid names that might contain the section name as part of a longer string
                if name and section and name == section and len(section.split()) <= 2:
                    print(f"DEBUG - Name '{name}' is exactly the same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting new employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in new employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for new employee {emp_id} - this should not happen")

                # Add the name if it exists and is valid
                # Based on user feedback: ALL CAPS names are valid and expected
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final new employee header: {emp_header}")

                doc.add_paragraph(emp_header, style='List Bullet')

        # Add removed employees section if any
        if removed_employees_list:
            doc.add_heading('Removed Employees', level=1)
            for emp in removed_employees_list:
                emp_id = emp.get("id", "")

                # Get department directly from the employee data
                # Department is a critical field that must be extracted for all employees
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for removed employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Get employee name
                name = emp.get("name", "")

                # Print debug information
                print(f"DEBUG - Removed Employee ID: {emp_id}, Department: {dept}, Name: {name}")

                # If name is missing or "Unknown", try to get it from the detailed_changes
                if not name or name == "Unknown":
                    # Check if we have name info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "employee_name" in change and change["employee_name"]:
                                        name = change["employee_name"]
                                        print(f"DEBUG - Found name in detailed_changes: {name}")
                                        break
                                if name and name != "Unknown":
                                    break

                # If department is missing or "Unknown", try to get it from the detailed_changes
                if not dept or dept == "Unknown":
                    # Check if we have department info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        break
                                if dept and dept != "Unknown":
                                    break

                # Make sure we're not using section as name
                section = emp.get("section", "")
                if name == section:
                    print(f"DEBUG - Name is same as section, trying to find better name")
                    # Try to extract name from the changes
                    if "changes" in emp and emp["changes"]:
                        for change in emp["changes"]:
                            if ": " in change and "; " in change:
                                parts = change.split("; ", 1)
                                if ": " in parts[0]:
                                    id_dept = parts[0].split(": ", 1)
                                    if len(id_dept) > 1 and id_dept[0] == emp_id:
                                        # This might contain the department
                                        dept_candidate = id_dept[1]
                                        if dept_candidate != section:
                                            dept = dept_candidate
                                            print(f"DEBUG - Found better department: {dept}")

                # CRITICAL FIX: Make sure we're not using section as name
                # Only clear the name if it's exactly the same as section (not just containing it)
                # This prevents clearing valid names that might contain the section name as part of a longer string
                if name and section and name == section and len(section.split()) <= 2:
                    print(f"DEBUG - Name '{name}' is exactly the same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting removed employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in removed employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for removed employee {emp_id} - this should not happen")

                # Add the name if it exists and is valid
                # Based on user feedback: ALL CAPS names are valid and expected
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final removed employee header: {emp_header}")

                doc.add_paragraph(emp_header, style='List Bullet')

        # Add loan tables as appendix
        if new_internal_loans or new_external_loans:
            # Add a page break before the appendix
            doc.add_page_break()

            # Add appendix heading
            doc.add_heading('APPENDIX: NEW LOANS', level=1)

            # Add internal loans table if there are any
            if new_internal_loans:
                doc.add_heading('COP INTERNAL LOANS', level=2)

                # Create table with headers
                table = doc.add_table(rows=1, cols=8)
                table.style = 'Table Grid'

                # Set table to landscape orientation
                section = doc.add_section(WD_SECTION.NEW_PAGE)
                section.orientation = WD_ORIENT.LANDSCAPE

                # Add headers
                header_cells = table.rows[0].cells
                for i, header in enumerate(["SN", "EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "LOAN TYPE", "LOAN AMOUNT", "CURRENT DEDUCTION", "REMARKS"]):
                    header_cells[i].text = header
                    header_cells[i].paragraphs[0].runs[0].font.bold = True

                # Add data rows
                for loan in new_internal_loans:
                    row_cells = table.add_row().cells
                    row_cells[0].text = str(loan["sn"])
                    row_cells[1].text = loan["employee_id"]
                    row_cells[2].text = loan["employee_name"]
                    row_cells[3].text = loan["department"]
                    # Standardize the loan type using the dictionary
                    standardized_loan_type = standardize_text_with_dictionary(loan["loan_type"])
                    row_cells[4].text = standardized_loan_type
                    row_cells[5].text = f"{loan['loan_amount']:.2f}"
                    row_cells[6].text = f"{loan['current_deduction']:.2f}"
                    row_cells[7].text = loan["remarks"]

            # Add external loans table if there are any
            if new_external_loans:
                # Add a page break if we already added the internal loans table
                if new_internal_loans:
                    doc.add_page_break()

                doc.add_heading('EXTERNAL LOANS', level=2)

                # Create table with headers
                table = doc.add_table(rows=1, cols=8)
                table.style = 'Table Grid'

                # Set table to landscape orientation
                section = doc.add_section(WD_SECTION.NEW_PAGE)
                section.orientation = WD_ORIENT.LANDSCAPE

                # Add headers
                header_cells = table.rows[0].cells
                for i, header in enumerate(["SN", "EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "LOAN TYPE", "LOAN AMOUNT", "CURRENT DEDUCTION", "REMARKS"]):
                    header_cells[i].text = header
                    header_cells[i].paragraphs[0].runs[0].font.bold = True

                # Add data rows
                for loan in new_external_loans:
                    row_cells = table.add_row().cells
                    row_cells[0].text = str(loan["sn"])
                    row_cells[1].text = loan["employee_id"]
                    row_cells[2].text = loan["employee_name"]
                    row_cells[3].text = loan["department"]
                    # Standardize the loan type using the dictionary
                    standardized_loan_type = standardize_text_with_dictionary(loan["loan_type"])
                    row_cells[4].text = standardized_loan_type
                    row_cells[5].text = f"{loan['loan_amount']:.2f}"
                    row_cells[6].text = f"{loan['current_deduction']:.2f}"
                    row_cells[7].text = loan["remarks"]

        # Save the Word document
        doc.save(word_path)
        print(f"Word: {word_path}")
    except Exception as e:
        print(f"Error creating Word report: {e}")
        word_path = None

    # Create PDF report (similar structure to Word report)
    pdf_path = os.path.join(output_dir, f"{base_filename}.pdf")

    # Final standardization of all data before PDF report generation
    print("Performing final standardization of all data before PDF report generation...")
    for emp in comparison_data:
        # Standardize current earnings one more time
        if "current_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["current_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["current_earnings"] = standardized_earnings

        # Standardize previous earnings one more time
        if "previous_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["previous_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["previous_earnings"] = standardized_earnings

        # Standardize current deductions one more time
        if "current_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["current_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["current_deductions"] = standardized_deductions

        # Standardize previous deductions one more time
        if "previous_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["previous_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["previous_deductions"] = standardized_deductions

        # Standardize current loan details one more time
        if "loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["loan_details"] = standardized_loans

        # Standardize previous loan details one more time
        if "previous_loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["previous_loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["previous_loan_details"] = standardized_loans

        # Standardize detailed changes one more time
        if "detailed_changes" in emp:
            # Standardize earnings changes
            if "earnings" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["earnings"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'earnings')

            # Standardize deductions changes
            if "deductions" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["deductions"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'deductions')

            # Standardize loans changes
            if "loans" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["loans"]:
                    if "type" in change:
                        loan_type = change["type"]
                        # Handle loan types with additional info (e.g., "LOAN TYPE - BALANCE B/F")
                        if " - " in loan_type:
                            loan_type, additional = loan_type.split(" - ", 1)
                            std_loan_type = standardize_field_name(loan_type, 'loans')
                            change["type"] = f"{std_loan_type} - {additional}"
                        else:
                            change["type"] = standardize_field_name(loan_type, 'loans')

    print("Final standardization for PDF report complete.")

    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib import colors
        from reportlab.pdfgen import canvas

        # Create a custom canvas class to add page numbers and footer
        class NumberedCanvas(canvas.Canvas):
            def __init__(self, *args, **kwargs):
                canvas.Canvas.__init__(self, *args, **kwargs)
                self._saved_page_states = []

            def showPage(self):
                self._saved_page_states.append(dict(self.__dict__))
                self._startPage()

            def save(self):
                """Add page numbers and footer to each page"""
                num_pages = len(self._saved_page_states)
                for state in self._saved_page_states:
                    self.__dict__.update(state)
                    self.draw_page_number(num_pages)
                    canvas.Canvas.showPage(self)
                canvas.Canvas.save(self)

            def draw_page_number(self, page_count):
                # Add page number and creator information
                self.setFont("Helvetica", 9)
                self.drawCentredString(letter[0] / 2, 20,
                                      f"Page {self._pageNumber} | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025")

        # Create PDF document with our custom canvas
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        styles = getSampleStyleSheet()

        # Create custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            alignment=0,  # 0 = left, 1 = center, 2 = right
            fontName='Helvetica-Bold',
            fontSize=18
        )

        heading1_style = ParagraphStyle(
            'CustomHeading1',
            parent=styles['Heading1'],
            alignment=0,  # Left alignment
            fontName='Helvetica-Bold',
            fontSize=14
        )

        heading2_style = ParagraphStyle(
            'CustomHeading2',
            parent=styles['Heading2'],
            alignment=0,  # Left alignment
            fontName='Helvetica-Bold',
            fontSize=12
        )

        normal_style = styles['Normal']

        # List item style
        list_style = ParagraphStyle(
            'ListItem',
            parent=styles['Normal'],
            leftIndent=20,
            firstLineIndent=0,
            spaceBefore=2,
            spaceAfter=2
        )

        # Create content elements
        elements = []

        # Add title
        elements.append(Paragraph(f'PAYROLL PRE-AUDIT REPORT: {curr_period}', title_style))
        elements.append(Spacer(1, 6))

        # Create a table for date, period, and signature information
        info_data = [
            [f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', ''],
            [f'Period: {curr_period}', '']
        ]

        # Add signature information if provided
        if report_name and report_designation:
            info_data[0][1] = f'Generated by: {report_name}'
            info_data[1][1] = f'Designation: {report_designation}'

        info_table = Table(info_data, colWidths=[300, 200])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),  # Align all cells to the left
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),  # Use Helvetica font
            ('FONTSIZE', (0, 0), (-1, -1), 10),  # Use 10pt font size
        ]))

        elements.append(info_table)
        elements.append(Spacer(1, 12))

        # Add statistics section
        elements.append(Paragraph('Statistics', heading1_style))

        # Count employees with changes
        employees_with_changes_count = len(employees_with_significant_changes)
        new_employees_count = len(new_employees_list)
        removed_employees_count = len(removed_employees_list)

        stats_data = [
            ["Total employees analyzed:", str(len(comparison_data))],
            ["Employees with changes:", str(employees_with_changes_count)],
            ["New employees:", str(new_employees_count)],
            ["Removed employees:", str(removed_employees_count)]
        ]

        stats_table = Table(stats_data, colWidths=[200, 100])
        stats_table.setStyle(TableStyle([
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),  # Align all cells to the left
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),  # Make all text bold
        ]))

        # Ensure the table is left-aligned in the document
        elements.append(stats_table)
        elements.append(Spacer(1, 12))

        # Add itemized changes section with the exact format from the screenshot
        elements.append(Paragraph('Itemized Changes', heading1_style))

        # Add a single department heading for all employees
        elements.append(Paragraph('Department: All Departments', heading2_style))
        elements.append(Spacer(1, 6))

        # Process all employees with changes
        for emp in comparison_data:
            # Skip employees with no changes
            if all(change == "No significant changes detected" for change in emp.get("changes", [])):
                continue

            # Get all changes without filtering
            significant_changes = [change for change in emp.get("changes", []) if change != "No significant changes detected"]

            # Process changes to ensure they have proper formatting
            processed_changes = []
            for change in significant_changes:
                # Remove employee identifiers if present
                if ": " in change and "; " in change:
                    change = change.split("; ", 1)[1]

                # Ensure month information is present and formatted correctly
                if " in " not in change:
                    change = change + f" in {curr_period}"
                elif " in None" in change:
                    change = change.replace(" in None", f" in {curr_period}")

                # Fix period placeholders
                change = change.replace("Current Period", curr_period)
                change = change.replace("Previous Period", prev_period)
                change = change.replace("current period", curr_period)
                change = change.replace("previous period", prev_period)

                processed_changes.append(change)

            significant_changes = processed_changes

            if significant_changes or "previous_gross_salary" in emp or "previous_net_pay" in emp:
                # Format employee ID with department and name only
                # Example: "COP0209: ABUAKWA AREA - MINISTERS - APPIAH-AIDOO A"
                emp_id = emp.get("id", "")

                # Get department using the advanced extraction system
                # Department is a critical field that must be extracted for all employees
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for PDF employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes for PDF: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text for PDF: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Debug output to see what's happening with employee data
                print(f"DEBUG - Employee ID: {emp_id}, Department: {dept}")

                # Get the employee name directly from the comparison data
                name = emp.get("name", "")
                print(f"DEBUG - Employee Name: {name}")

                # If name is missing or "Unknown", try to get it from the detailed_changes
                if not name or name == "Unknown":
                    # Check if we have name info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "employee_name" in change and change["employee_name"]:
                                        name = change["employee_name"]
                                        print(f"DEBUG - Found name in detailed_changes: {name}")
                                        break
                                if name and name != "Unknown":
                                    break

                # If department is missing or "Unknown", try to get it from the detailed_changes
                if not dept or dept == "Unknown":
                    # Check if we have department info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes: {dept}")
                                        break
                                if dept and dept != "Unknown":
                                    break

                # Make sure we're not using section as name
                section = emp.get("section", "")
                if name == section:
                    print(f"DEBUG - Name is same as section, trying to find better name")
                    # Try to extract name from the changes
                    if "changes" in emp and emp["changes"]:
                        for change in emp["changes"]:
                            if ": " in change and "; " in change:
                                parts = change.split("; ", 1)
                                if ": " in parts[0]:
                                    id_dept = parts[0].split(": ", 1)
                                    if len(id_dept) > 1 and id_dept[0] == emp_id:
                                        # This might contain the department
                                        dept_candidate = id_dept[1]
                                        if dept_candidate != section:
                                            dept = dept_candidate
                                            print(f"DEBUG - Found better department: {dept}")

                # CRITICAL FIX: Make sure we're not using section as name
                section = emp.get("section", "")

                # Only clear the name if it's exactly the same as section (not just containing it)
                # This prevents clearing valid names that might contain the section name as part of a longer string
                if name and section and name == section and len(section.split()) <= 2:
                    print(f"DEBUG - Name '{name}' is exactly the same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting PDF employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in PDF employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for PDF employee {emp_id} - this should not happen")

                # Add the name if it exists and is valid
                # Enhanced validation to ensure we only include real person names
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Check if name is not just an organizational term
                    org_terms = ["AREA", "DISTRICT", "REGION", "DEPARTMENT", "SECTION", "MINISTERS", "HEADQUARTERS",
                                "BANK", "DETAILS", "EMPLOYEE", "EMPLOYER", "INFORMATION"]

                    # Only add the name if:
                    # 1. It's not an organizational term
                    # 2. It's not too short (at least 4 characters)
                    # 3. It contains at least one space (first and last name)
                    # 4. It starts with a capital letter
                    if (not any(name.strip() == term for term in org_terms) and
                        len(name) >= 4 and
                        ' ' in name and
                        name[0].isupper() and
                        not any(term.lower() in name.lower() for term in ["BANK", "DETAILS", "EMPLOYEE BANK", "ACCOUNT"])):

                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final PDF employee header: {emp_header}")

                elements.append(Paragraph(f"• {emp_header}", list_style))

                # Add all changes with simple numbering
                start_idx = 1
                for i, change in enumerate(significant_changes, start_idx):
                    # Clean up the change message to match the expected format
                    clean_change = change
                    if ":" in clean_change and ";" in clean_change:
                        clean_change = clean_change.split(";", 1)[1].strip()

                    # Standardize the text using the dictionary
                    # This is where we identify and replace variations within the text
                    standardized_change = standardize_text_with_dictionary(clean_change)

                    # Add the standardized change to the document
                    elements.append(Paragraph(f"    {i}. {standardized_change}", list_style))

        elements.append(Spacer(1, 6))

        # Add new employees section if any
        if new_employees_list:
            elements.append(Paragraph('New Employees', heading1_style))
            for emp in new_employees_list:
                emp_id = emp.get("id", "")

                # Get department using the advanced extraction system
                # Department is a critical field that must be extracted for all employees
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for PDF new employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes for PDF new employee: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text for PDF new employee: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Debug output to see what's happening with employee data
                print(f"DEBUG - PDF New Employee ID: {emp_id}, Department: {dept}")

                # Get the employee name directly from the comparison data
                name = emp.get("name", "")
                print(f"DEBUG - New Employee Name: {name}")

                # If name is missing or "Unknown", try to get it from the detailed_changes
                if not name or name == "Unknown":
                    # Check if we have name info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "employee_name" in change and change["employee_name"]:
                                        name = change["employee_name"]
                                        print(f"DEBUG - Found name in detailed_changes: {name}")
                                        break
                                if name and name != "Unknown":
                                    break

                # Department and Section are distinct entities and must not be substituted
                # We should never use Section as a fallback for Department
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department still missing for PDF new employee {emp_id} after advanced extraction")

                # Make sure we're not using section as name
                section = emp.get("section", "")
                if name == section:
                    print(f"DEBUG - Name is same as section, trying to find better name")
                    # Try to extract name from the changes
                    if "changes" in emp and emp["changes"]:
                        for change in emp["changes"]:
                            if ": " in change and "; " in change:
                                parts = change.split("; ", 1)
                                if ": " in parts[0]:
                                    id_dept = parts[0].split(": ", 1)
                                    if len(id_dept) > 1 and id_dept[0] == emp_id:
                                        # This might contain the department
                                        dept_candidate = id_dept[1]
                                        if dept_candidate != section:
                                            dept = dept_candidate
                                            print(f"DEBUG - Found better department: {dept}")

                # CRITICAL FIX: Make sure we're not using section as name
                # Only clear the name if it's exactly the same as section (not just containing it)
                # This prevents clearing valid names that might contain the section name as part of a longer string
                if name and section and name == section and len(section.split()) <= 2:
                    print(f"DEBUG - Name '{name}' is exactly the same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting PDF new employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in PDF new employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for PDF new employee {emp_id} - this should not happen")

                # Add the name if it exists and is valid
                # Based on user feedback: ALL CAPS names are valid and expected
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final PDF new employee header: {emp_header}")

                elements.append(Paragraph(f"• {emp_header}", list_style))
            elements.append(Spacer(1, 12))

        # Add removed employees section if any
        if removed_employees_list:
            elements.append(Paragraph('Removed Employees', heading1_style))
            for emp in removed_employees_list:
                emp_id = emp.get("id", "")

                # Get department using the advanced extraction system
                # Department is a critical field that must be extracted for all employees
                dept = emp.get("department", "")

                # CRITICAL: Ensure department is always available
                # If department is missing, try harder to find it in the employee data
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department missing for PDF removed employee {emp_id} - attempting advanced extraction")

                    # Check if we have department info in the detailed_changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "department" in change and change["department"]:
                                        dept = change["department"]
                                        print(f"DEBUG - Found department in detailed_changes for PDF removed employee: {dept}")
                                        # Update the main employee record with this department
                                        emp["department"] = dept
                                        break
                                if dept and dept != "Unknown":
                                    break

                    # If still missing, try to extract from changes text
                    if not dept or dept == "Unknown":
                        if "changes" in emp and emp["changes"]:
                            for change in emp["changes"]:
                                # Look for department pattern in change text
                                dept_match = re.search(r"(?:Department|DEPARTMENT):\s*([A-Za-z0-9\s\-\.]+)", change)
                                if dept_match:
                                    dept = dept_match.group(1).strip()
                                    print(f"DEBUG - Extracted department from changes text for PDF removed employee: {dept}")
                                    # Update the main employee record with this department
                                    emp["department"] = dept
                                    break

                # Debug output to see what's happening with employee data
                print(f"DEBUG - PDF Removed Employee ID: {emp_id}, Department: {dept}")

                # Get the employee name directly from the comparison data
                name = emp.get("name", "")
                print(f"DEBUG - Removed Employee Name: {name}")

                # If name is missing or "Unknown", try to get it from the detailed_changes
                if not name or name == "Unknown":
                    # Check if we have name info in the detailed changes
                    if "detailed_changes" in emp:
                        for change_type in ["salary", "deductions", "earnings", "other"]:
                            if change_type in emp["detailed_changes"]:
                                for change in emp["detailed_changes"][change_type]:
                                    if "employee_name" in change and change["employee_name"]:
                                        name = change["employee_name"]
                                        print(f"DEBUG - Found name in detailed_changes: {name}")
                                        break
                                if name and name != "Unknown":
                                    break

                # Department and Section are distinct entities and must not be substituted
                # We should never use Section as a fallback for Department
                if not dept or dept == "Unknown":
                    print(f"WARNING - Department still missing for PDF removed employee {emp_id} after advanced extraction")

                # Make sure we're not using section as name
                section = emp.get("section", "")
                if name == section:
                    print(f"DEBUG - Name is same as section, trying to find better name")
                    # Try to extract name from the changes
                    if "changes" in emp and emp["changes"]:
                        for change in emp["changes"]:
                            if ": " in change and "; " in change:
                                parts = change.split("; ", 1)
                                if ": " in parts[0]:
                                    id_dept = parts[0].split(": ", 1)
                                    if len(id_dept) > 1 and id_dept[0] == emp_id:
                                        # This might contain the department
                                        dept_candidate = id_dept[1]
                                        if dept_candidate != section:
                                            dept = dept_candidate
                                            print(f"DEBUG - Found better department: {dept}")

                # CRITICAL FIX: Make sure we're not using section as name
                # Only clear the name if it's exactly the same as section (not just containing it)
                # This prevents clearing valid names that might contain the section name as part of a longer string
                if name and section and name == section and len(section.split()) <= 2:
                    print(f"DEBUG - Name '{name}' is exactly the same as section, clearing name")
                    name = None  # Clear the name to prevent confusion

                # Format the employee header with emp_id and dept
                # Format: "COP2626: Abuakwa Area - EMPLOYEE NAME"
                # Department and Section are distinct entities and must not be substituted
                print(f"DEBUG - Formatting PDF removed employee header with Department: '{dept}'")

                # CRITICAL: Department must always be included, never use Section as fallback
                if dept:
                    emp_header = f"{emp_id}: {dept}"
                    print(f"DEBUG - Successfully included Department in PDF removed employee header: {dept}")
                else:
                    # If department is missing, log a warning but don't substitute with section
                    emp_header = f"{emp_id}"
                    print(f"WARNING - Department missing for PDF removed employee {emp_id} - this should not happen")

                # Add the name if it exists and is valid
                # Based on user feedback: ALL CAPS names are valid and expected
                if name and name != "Unknown" and name.strip():
                    # Clean up the name - remove any trailing punctuation or extra spaces
                    name = name.strip().rstrip('.:,;')

                    # Minimal validation - just check for exact matches with organizational terms
                    org_terms = ["EMPLOYEE BANK DETAILS", "EARNINGS", "DEDUCTIONS", "LOANS"]

                    # Only exclude the name if it's exactly one of these terms
                    if not any(name == term for term in org_terms):
                        # Add the name after the department
                        emp_header = f"{emp_header} - {name}"
                        print(f"DEBUG - Added name to header: {name}")

                # Print the final header for debugging
                print(f"DEBUG - Final PDF removed employee header: {emp_header}")

                elements.append(Paragraph(f"• {emp_header}", list_style))

        # Add loan tables as appendix
        if new_internal_loans or new_external_loans:
            # Add a page break before the appendix
            elements.append(PageBreak())

            # Add appendix heading
            elements.append(Paragraph('APPENDIX: NEW LOANS', heading1_style))

            # Add internal loans table if there are any
            if new_internal_loans:
                elements.append(Paragraph('COP INTERNAL LOANS', heading2_style))
                elements.append(Spacer(1, 12))

                # Create table data with headers
                internal_loan_data = [["SN", "EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "LOAN TYPE", "LOAN AMOUNT", "CURRENT DEDUCTION", "REMARKS"]]

                # Add data rows
                for loan in new_internal_loans:
                    # Standardize the loan type using the dictionary
                    standardized_loan_type = standardize_text_with_dictionary(loan["loan_type"])

                    internal_loan_data.append([
                        str(loan["sn"]),
                        loan["employee_id"],
                        loan["employee_name"],
                        loan["department"],
                        standardized_loan_type,
                        f"{loan['loan_amount']:.2f}",
                        f"{loan['current_deduction']:.2f}",
                        loan["remarks"]
                    ])

                # Create table
                internal_loan_table = Table(internal_loan_data, repeatRows=1)

                # Style the table
                internal_loan_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Center SN column
                    ('ALIGN', (5, 1), (6, -1), 'RIGHT'),   # Right-align amount columns
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                # Add table to elements
                elements.append(internal_loan_table)

            # Add external loans table if there are any
            if new_external_loans:
                # Add a page break if we already added the internal loans table
                if new_internal_loans:
                    elements.append(PageBreak())

                elements.append(Paragraph('EXTERNAL LOANS', heading2_style))
                elements.append(Spacer(1, 12))

                # Create table data with headers
                external_loan_data = [["SN", "EMPLOYEE NO.", "EMPLOYEE NAME", "DEPARTMENT", "LOAN TYPE", "LOAN AMOUNT", "CURRENT DEDUCTION", "REMARKS"]]

                # Add data rows
                for loan in new_external_loans:
                    # Standardize the loan type using the dictionary
                    standardized_loan_type = standardize_text_with_dictionary(loan["loan_type"])

                    external_loan_data.append([
                        str(loan["sn"]),
                        loan["employee_id"],
                        loan["employee_name"],
                        loan["department"],
                        standardized_loan_type,
                        f"{loan['loan_amount']:.2f}",
                        f"{loan['current_deduction']:.2f}",
                        loan["remarks"]
                    ])

                # Create table
                external_loan_table = Table(external_loan_data, repeatRows=1)

                # Style the table
                external_loan_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('ALIGN', (0, 0), (0, -1), 'CENTER'),  # Center SN column
                    ('ALIGN', (5, 1), (6, -1), 'RIGHT'),   # Right-align amount columns
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ]))

                # Add table to elements
                elements.append(external_loan_table)

        # Build the PDF with our custom canvas
        doc.build(elements, canvasmaker=NumberedCanvas)
        print(f"PDF: {pdf_path}")
    except Exception as e:
        print(f"Error creating PDF report: {e}")
        pdf_path = None

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path,
        "word": word_path,
        "pdf": pdf_path
    }

    print("Report generation complete.")
    return reports
