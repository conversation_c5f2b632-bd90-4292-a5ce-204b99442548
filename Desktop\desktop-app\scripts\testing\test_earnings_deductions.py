"""
Test script to specifically analyze EARNINGS and DEDUCTIONS sections from MAIN1.pdf
"""

import sys
import os
import json
import re
import PyPDF2

def extract_sections(payslip_text):
    """Extract EARNINGS and DEDUCTIONS sections from payslip text."""
    results = {
        "raw_text": payslip_text,
        "earnings_section": None,
        "deductions_section": None,
        "earnings_items": {},
        "deductions_items": {},
        "actual_earnings": [],
        "actual_deductions": []
    }

    # Try to manually identify the actual earnings and deductions based on the PDF layout
    lines = payslip_text.split('\n')
    for line in lines:
        # Look for patterns that indicate earnings or deductions
        if 'BASIC SALARY' in line:
            match = re.search(r'BASIC SALARY\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"BASIC SALARY: {value}")

        if 'TAXABLE SALARY' in line:
            match = re.search(r'TAXABLE SALARY\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"TAXABLE SALARY: {value}")

        if 'LEAVE ALLOWANCE' in line:
            match = re.search(r'LEAVE ALLOWANCE\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"LEAVE ALLOWANCE: {value}")

        if 'EDUCATIONAL SUBSIDY' in line:
            match = re.search(r'EDUCATIONAL SUBSIDY\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"EDUCATIONAL SUBSIDY: {value}")

        if 'RENT ELEMENT' in line:
            match = re.search(r'RENT ELEMENT\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"RENT ELEMENT: {value}")

        if '2ND FUEL ELEMENT' in line:
            match = re.search(r'2ND FUEL ELEMENT\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"2ND FUEL ELEMENT: {value}")

        if '1ST FUEL ELEMENT' in line:
            match = re.search(r'1ST FUEL ELEMENT\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"1ST FUEL ELEMENT: {value}")

        if 'RESPONSIBILITY' in line and 'HEADS' in line:
            match = re.search(r'RESPONSIBILITY\s+HEADS\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_earnings"].append(f"RESPONSIBILITY HEADS: {value}")

        if 'INCOME TAX' in line:
            match = re.search(r'INCOME TAX\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"INCOME TAX: {value}")

        if 'SSF EEMPLOYEE' in line:
            match = re.search(r'SSF EEMPLOYEE\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"SSF EEMPLOYEE: {value}")

        if 'PENT. MIN WELFARE FUND' in line:
            match = re.search(r'PENT. MIN WELFARE FUND\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"PENT. MIN WELFARE FUND: {value}")

        if 'SCHOLARSHIP FUND' in line:
            match = re.search(r'SCHOLARSHIP FUND.*?\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"SCHOLARSHIP FUND: {value}")

        if 'TITHES' in line:
            match = re.search(r'TITHES\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"TITHES: {value}")

        if "MINISTERS' PENSION" in line or 'MINISTERS PENSION' in line:
            match = re.search(r'MINISTERS.*?PENSION.*?CAT\s+\d+\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"MINISTERS' PENSION: {value}")

        if 'Loan Deductions' in line:
            match = re.search(r'Loan Deductions\s+([\d,.]+)', line)
            value = match.group(1) if match else 'Not found'
            results["actual_deductions"].append(f"Loan Deductions: {value}")

    # Extract earnings section using regex
    earnings_section = re.search(r"(?:EARNINGS)(?:.*?)(?:DEDUCTIONS|GROSS SALARY|NET PAY|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if earnings_section:
        results["earnings_section"] = earnings_section.group(0)

        # Extract earnings items
        earnings_items = re.finditer(r"([A-Z][A-Z\s\-\.0-9]+)\s+([\d,.]+)", results["earnings_section"])
        for item in earnings_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()

            # Skip section headers
            if item_name == "EARNINGS":
                continue

            # Add to earnings dictionary
            results["earnings_items"][item_name] = item_value

    # Extract deductions section using regex
    deductions_section = re.search(r"(?:DEDUCTIONS)(?:.*?)(?:NET PAY|EMPLOYER'S CONTRIBUTION|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if deductions_section:
        results["deductions_section"] = deductions_section.group(0)

        # Extract deductions items
        deduction_items = re.finditer(r"([A-Z][A-Z\s\-\.0-9]+)\s+([\d,.]+)", results["deductions_section"])
        for item in deduction_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()

            # Skip section headers
            if item_name == "DEDUCTIONS":
                continue

            # Add to deductions dictionary
            results["deductions_items"][item_name] = item_value

    return results

def main():
    pdf_path = r'C:\Users\<USER>\Desktop\MAIN1.pdf'

    # Extract text from PDF
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        page = reader.pages[0]
        text = page.extract_text()

    # Extract sections
    results = extract_sections(text)

    # Print raw text for reference
    print("RAW TEXT FROM PDF:")
    print("-" * 50)
    print(results["raw_text"])
    print("-" * 50)

    # Print actual items identified manually
    print("\nACTUAL EARNINGS ITEMS (manually identified):")
    for item in results["actual_earnings"]:
        print(f"  {item}")

    print("\nACTUAL DEDUCTIONS ITEMS (manually identified):")
    for item in results["actual_deductions"]:
        print(f"  {item}")

    # Print extracted sections
    print("\nEXTRACTED EARNINGS SECTION:")
    print("-" * 50)
    print(results["earnings_section"])
    print("-" * 50)

    print("\nEXTRACTED DEDUCTIONS SECTION:")
    print("-" * 50)
    print(results["deductions_section"])
    print("-" * 50)

    # Print extracted items
    print("\nEXTRACTED EARNINGS ITEMS:")
    for name, value in results["earnings_items"].items():
        print(f"  {name}: {value}")

    print("\nEXTRACTED DEDUCTIONS ITEMS:")
    for name, value in results["deductions_items"].items():
        print(f"  {name}: {value}")

    # Compare actual vs extracted
    print("\nCOMPARISON ANALYSIS:")
    print("Items that should be in EARNINGS but were not extracted or were extracted incorrectly:")
    for item in results["actual_earnings"]:
        item_name = item.split(":")[0].strip()
        item_value = item.split(":")[1].strip()
        found = False
        for extracted_name, extracted_value in results["earnings_items"].items():
            if item_name in extracted_name and item_value == extracted_value:
                found = True
                break
        if not found:
            print(f"  Missing/Incorrect: {item}")

    print("\nItems that should be in DEDUCTIONS but were not extracted or were extracted incorrectly:")
    for item in results["actual_deductions"]:
        item_name = item.split(":")[0].strip()
        item_value = item.split(":")[1].strip()
        found = False
        for extracted_name, extracted_value in results["deductions_items"].items():
            if item_name in extracted_name and item_value == extracted_value:
                found = True
                break
        if not found:
            print(f"  Missing/Incorrect: {item}")

if __name__ == "__main__":
    main()
