<!-- Bank Adviser Content -->
<div class="content-container hidden" id="bank-adviser-content">
  <h2>Bank Adviser</h2>
  <p class="description">Standardize and verify bank advice data against payslip information.</p>

  <div class="bank-adviser-container">
    <!-- File Selection Section -->
    <div class="file-selection-section">
      <h3>File Selection</h3>
      <div class="file-selector">
        <p>Bank Advice Excel File</p>
        <div class="file-input-container">
          <input type="text" id="bank-advice-file-path" readonly>
          <button id="bank-advice-browse-btn">Browse</button>
        </div>
        <p class="error-message" id="bank-advice-file-error"></p>
      </div>

      <div class="file-selector">
        <p>Payslip PDF Files</p>
        <div class="file-input-container">
          <input type="text" id="payslip-files-path" readonly>
          <button id="payslip-files-browse-btn">Browse</button>
        </div>
        <p class="error-message" id="payslip-files-error"></p>
      </div>

      <div class="file-selector">
        <p>ALL ALLOWANCES PDF File (Optional)</p>
        <div class="file-input-container">
          <input type="text" id="allowances-file-path" readonly>
          <button id="allowances-browse-btn">Browse</button>
        </div>
        <p class="error-message" id="allowances-file-error"></p>
      </div>

      <div class="file-selector">
        <p>AWARDS & GRANTS PDF File (Optional)</p>
        <div class="file-input-container">
          <input type="text" id="awards-file-path" readonly>
          <button id="awards-browse-btn">Browse</button>
        </div>
        <p class="error-message" id="awards-file-error"></p>
      </div>
    </div>

    <!-- Excel Structure Settings Section (Initially Hidden) -->
    <div class="excel-structure-section" id="excel-structure-section" style="display: none;">
      <h3>Excel Structure Settings</h3>
      <p class="description">These settings were automatically detected from your Excel file. You can adjust them if needed.</p>
      
      <div class="excel-structure-settings">
        <div class="setting-group">
          <label for="header-row">Header Row:</label>
          <input type="number" id="header-row" min="1" value="4">
          <p class="setting-description">The row number containing column headers</p>
        </div>
        
        <div class="setting-group">
          <label for="data-start-row">Data Start Row:</label>
          <input type="number" id="data-start-row" min="1" value="5">
          <p class="setting-description">The row number where data begins</p>
        </div>
        
        <div class="setting-group">
          <label for="first-column">First Column:</label>
          <input type="text" id="first-column" value="A" maxlength="2">
          <p class="setting-description">The letter of the first column to read</p>
        </div>
        
        <div class="setting-actions">
          <button id="reload-columns-btn">Reload Columns with These Settings</button>
        </div>
      </div>
    </div>

    <!-- Column Mapping Section (Initially Hidden) -->
    <div class="column-mapping-section" id="column-mapping-section" style="display: none;">
      <h3>Column Mapping</h3>
      <p class="description">Map Excel columns to standard fields required for bank advice processing.</p>
      
      <div class="column-mapping-container">
        <table class="column-mapping-table">
          <tbody id="column-mapping-body">
            <!-- Column mapping will be added here dynamically -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-container">
      <button id="start-bank-adviser-btn" disabled>Process Bank Advice</button>
    </div>

    <!-- Results Section -->
    <div id="bank-adviser-results"></div>
  </div>
</div>
