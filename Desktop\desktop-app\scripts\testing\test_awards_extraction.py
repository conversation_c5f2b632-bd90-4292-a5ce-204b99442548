import os
import sys
import json
import re
import logging

# Configure logging to output to console
logging.basicConfig(
    level=logging.WARNING,  # Use WARNING level to reduce verbosity
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AwardsExtractor')

# Print immediate feedback
print("<PERSON><PERSON><PERSON> started. Attempting to extract data from AWARDS & GRANTS PDF...")

class AwardsExtractor:
    """
    Class for testing extraction of data from AWARDS & GRANTS PDF.
    """

    def __init__(self):
        """Initialize the AwardsExtractor class."""
        self.awards_data = {}

    def extract_awards_data(self, awards_file):
        """
        Extract data from AWARDS & GRANTS PDF.

        Args:
            awards_file (str): Path to the AWARDS & GRANTS PDF file
        """
        logger.info(f"Extracting data from awards file: {awards_file}")

        # Initialize awards data dictionary
        self.awards_data = {}

        # Check if the file exists
        if not awards_file or not os.path.exists(awards_file):
            logger.warning(f"Awards file not found or not provided: {awards_file}")
            logger.info("Using empty awards data - no mock data will be generated")
            # Return empty data structure - no mock data
            return

        try:
            # Import PyPDF2 for PDF extraction
            import PyPDF2

            # Open the PDF file
            with open(awards_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)

                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                logger.info(f"Awards PDF has {num_pages} pages")
                print(f"Awards PDF has {num_pages} pages")

                # Initialize variables for tracking the current section
                current_section = None

                # Process each page
                for page_num in range(num_pages):
                    # Print progress
                    print(f"Processing page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Print the first page text for debugging
                    if page_num == 0:
                        print("\nFirst page text sample:")
                        print(text[:500] + "..." if len(text) > 500 else text)
                        print("\nFirst few lines:")
                        lines = text.split('\n')
                        for i, line in enumerate(lines[:10]):
                            print(f"Line {i+1}: {line}")

                    # Split the text into lines
                    lines = text.split('\n')

                    # Process each line
                    for line in lines:
                        # Look for section headers (focus areas and subtitles)
                        # More comprehensive pattern to match various award types
                        section_match = re.search(r'^([\w\s\-\&\(\)]+(?:AWARD|GRANT|SERVICE|RECOGNITION|ACHIEVEMENT|BONUS))', line, re.IGNORECASE)
                        if section_match:
                            current_section = section_match.group(1).strip()
                            logger.info(f"Found award section: {current_section}")
                            print(f"Found award section: {current_section}")
                            continue

                        # Also check for lines that might be section headers without specific keywords
                        if line.isupper() and len(line.strip()) > 5 and len(line.strip()) < 50 and not re.search(r'\d', line):
                            current_section = line.strip()
                            logger.info(f"Found potential award section (uppercase line): {current_section}")
                            print(f"Found potential award section (uppercase line): {current_section}")
                            continue

                        # Look for lines with "LSTG" or similar that might indicate awards
                        lstg_match = re.search(r'\b(LSTG|AWARD|GRANT|SERVICE|RECOGNITION)\b', line, re.IGNORECASE)
                        if lstg_match and current_section is None:
                            current_section = "LONG SERVICE AWARD"
                            logger.info(f"Found LSTG indicator, using section: {current_section}")
                            print(f"Found LSTG indicator, using section: {current_section}")
                            continue

                    # After processing all lines for sections, extract employee data
                    # This is more efficient than trying to extract data line by line
                    employee_data = self._extract_employee_data_from_text(text, current_section, "award")

                    # Process the extracted employee data
                    for employee_no, amount, section in employee_data:
                        # Store the data
                        if employee_no not in self.awards_data:
                            self.awards_data[employee_no] = {
                                'EMPLOYEE_NO': employee_no,
                                'AWARDS & GRANTS': 0.0,
                                'BREAKDOWN': []
                            }

                        # Add the amount to the total
                        self.awards_data[employee_no]['AWARDS & GRANTS'] += amount

                        # Add the award type to the breakdown
                        if section and amount > 0:
                            # Check if this award type is already in the breakdown
                            if section not in self.awards_data[employee_no]['BREAKDOWN']:
                                self.awards_data[employee_no]['BREAKDOWN'].append(section)

                        logger.info(f"Found award for employee {employee_no}: {section} = {amount}")
                        print(f"Found award for employee {employee_no}: {section} = {amount}")

            # Convert breakdown lists to strings
            for employee_no, data in self.awards_data.items():
                if isinstance(data['BREAKDOWN'], list):
                    data['BREAKDOWN'] = ', '.join(data['BREAKDOWN'])

        except Exception as e:
            logger.error(f"Error extracting data from awards file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            print(f"Error extracting data: {str(e)}")
            print(traceback.format_exc())

        logger.info(f"Extracted awards data for {len(self.awards_data)} employees")
        print(f"\nExtracted awards data for {len(self.awards_data)} employees")
        
        # Print the extracted data for debugging
        if self.awards_data:
            print("\nSample of extracted data:")
            for i, (employee_no, data) in enumerate(list(self.awards_data.items())[:5]):
                print(f"  Employee {employee_no}: {data}")
            
            if len(self.awards_data) > 5:
                print(f"  ... and {len(self.awards_data) - 5} more entries")
        else:
            print("No data was extracted!")

    def _extract_employee_data_from_text(self, text, current_section, data_type="award"):
        """
        Extract employee data from text using improved patterns.

        Args:
            text (str): The text to extract data from
            current_section (str): The current section being processed
            data_type (str): The type of data being extracted ("allowance" or "award")

        Returns:
            list: List of tuples containing (employee_no, amount, section)
        """
        results = []

        # Split text into lines
        lines = text.split('\n')

        # Process each line
        for i, line in enumerate(lines):
            # Look for employee information - more comprehensive pattern
            # This pattern looks for employee IDs in various formats:
            # - Alpha followed by numbers (e.g., COP1234, EMP001)
            # - Just numbers with optional prefix/suffix (e.g., 12345, #12345)
            employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
            if employee_match and current_section:
                # Extract employee number
                employee_no = employee_match.group(1).strip()
                logger.info(f"Found employee in {data_type}: {employee_no} in line: {line}")

                # For the AWARDS section, we know the amount is in a specific position
                # The format is similar to: "COP1337 AYISI DAVID 4,111.45 6,691.86"
                # Where 4,111.45 is the amount we want
                
                # Split the line by spaces
                parts = line.split()
                
                # Check if we have enough parts and the employee code is at the beginning
                if len(parts) >= 4 and employee_no in parts[0]:
                    try:
                        # The amount should be the third element from the end
                        amount_str = parts[-2].replace(',', '')
                        amount = float(amount_str)
                        logger.info(f"Found amount for employee {employee_no}: {amount}")
                        results.append((employee_no, amount, current_section))
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Could not extract amount for {employee_no}: {str(e)}")
                        
                        # Fallback to regex pattern
                        amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                        if amount_match:
                            try:
                                # Remove commas and other non-numeric characters before converting to float
                                amount_str = amount_match.group(1).replace(',', '')
                                amount_str = re.sub(r'[^\d.]', '', amount_str)
                                amount = float(amount_str)
                                logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                                results.append((employee_no, amount, current_section))
                            except ValueError:
                                logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
                else:
                    logger.warning(f"Line format not as expected for {employee_no}: {line}")
                    
                    # Fallback to original regex pattern
                    amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                    if amount_match:
                        try:
                            # Remove commas and other non-numeric characters before converting to float
                            amount_str = amount_match.group(1).replace(',', '')
                            amount_str = re.sub(r'[^\d.]', '', amount_str)
                            amount = float(amount_str)
                            logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                            results.append((employee_no, amount, current_section))
                        except ValueError:
                            logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")

        return results

    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.
        
        Args:
            output_file (str): Path to the output Excel file
        """
        try:
            import pandas as pd
            
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.awards_data.items():
                data_list.append(data)
                
            df = pd.DataFrame(data_list)
            
            # Reorder columns
            columns = ['EMPLOYEE_NO', 'AWARDS & GRANTS', 'BREAKDOWN']
            df = df[columns]
            
            # Save to Excel
            df.to_excel(output_file, index=False)
            
            print(f"Successfully saved data to Excel file: {output_file}")
            print(f"Total records saved: {len(df)}")
            
        except Exception as e:
            logger.error(f"Error saving data to Excel file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            print(f"Error saving to Excel: {str(e)}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_awards_extraction.py <awards_pdf_file> [output_excel_file]")
        sys.exit(1)
    
    awards_file = sys.argv[1]
    
    # Default output file is awards_extract.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "awards_extract.xlsx")
    
    # If output file is provided, use it
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print(f"File path: {awards_file}")
    print(f"File exists: {os.path.exists(awards_file)}")
    print(f"Output file: {output_file}")
    
    try:
        # Import PyPDF2 for PDF extraction
        import PyPDF2
        print("PyPDF2 imported successfully")
        
        # Try to open the file to verify it's a valid PDF
        with open(awards_file, 'rb') as file:
            try:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)
                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                print(f"Successfully opened PDF. It has {num_pages} pages.")
                
                # Try to extract text from the first page
                if num_pages > 0:
                    page = pdf_reader.pages[0]
                    text = page.extract_text()
                    print(f"First page text sample: {text[:100]}...")
            except Exception as e:
                print(f"Error reading PDF: {str(e)}")
                import traceback
                print(traceback.format_exc())
    except Exception as e:
        print(f"Error importing or using PyPDF2: {str(e)}")
        import traceback
        print(traceback.format_exc())
    
    print("Starting extraction...")
    extractor = AwardsExtractor()
    extractor.extract_awards_data(awards_file)
    
    # Save to Excel if data was extracted
    if extractor.awards_data:
        extractor.save_to_excel(output_file)
    else:
        print("No data to save to Excel.")

if __name__ == "__main__":
    main()
