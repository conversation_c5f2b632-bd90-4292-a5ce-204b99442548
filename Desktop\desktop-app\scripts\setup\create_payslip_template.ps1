$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$workbook = $excel.Workbooks.Add()
$worksheet = $workbook.Worksheets.Item(1)
$worksheet.Name = 'Payslip Dictionary'

# Create headers
$worksheet.Cells.Item(1, 1) = 'SECTION'
$worksheet.Cells.Item(1, 2) = 'ITEM'
$worksheet.Cells.Item(1, 3) = 'FORMAT'
$worksheet.Cells.Item(1, 4) = 'DESCRIPTION'
$worksheet.Cells.Item(1, 5) = 'INCLUDE_IN_REPORT'

# Format headers
$range = $worksheet.Range('A1:E1')
$range.Font.Bold = $true
$range.Interior.ColorIndex = 15

# Set column widths
$worksheet.Columns.Item(1).ColumnWidth = 25
$worksheet.Columns.Item(2).ColumnWidth = 25
$worksheet.Columns.Item(3).ColumnWidth = 20
$worksheet.Columns.Item(4).ColumnWidth = 40
$worksheet.Columns.Item(5).ColumnWidth = 20

# Add data
$row = 2

# PERSONAL DETAILS SECTION
$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Employee No.'
$worksheet.Cells.Item($row, 3) = '[TC][.]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Employee Name'
$worksheet.Cells.Item($row, 3) = '[TC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'SSF No.'
$worksheet.Cells.Item($row, 3) = '[TC][.]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Ghana Card ID'
$worksheet.Cells.Item($row, 3) = '[TC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Section'
$worksheet.Cells.Item($row, 3) = '[TC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Department'
$worksheet.Cells.Item($row, 3) = '[TC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$worksheet.Cells.Item($row, 2) = 'Job Title'
$worksheet.Cells.Item($row, 3) = '[TC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# EARNINGS SECTION
$worksheet.Cells.Item($row, 1) = 'EARNINGS'
$worksheet.Cells.Item($row, 2) = 'EARNINGS'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'EARNINGS'
$worksheet.Cells.Item($row, 2) = 'GROSS SALARY'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'EARNINGS'
$worksheet.Cells.Item($row, 2) = 'NET PAY'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# DEDUCTIONS SECTION
$worksheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$worksheet.Cells.Item($row, 2) = 'DEDUCTIONS'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$worksheet.Cells.Item($row, 2) = 'TOTAL DEDUCTIONS'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

$worksheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$worksheet.Cells.Item($row, 2) = 'TAXABLE SALARY'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# EMPLOYERS CONTRIBUTION SECTION
$worksheet.Cells.Item($row, 1) = 'EMPLOYERS CONTRIBUTION'
$worksheet.Cells.Item($row, 2) = "Employer's Contributions"
$worksheet.Cells.Item($row, 3) = "[TC][']"
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# LOANS SECTION
$worksheet.Cells.Item($row, 1) = 'LOANS'
$worksheet.Cells.Item($row, 2) = 'LOANS'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# EMPLOYEE BANK DETAILS SECTION
$worksheet.Cells.Item($row, 1) = 'EMPLOYEE BANK DETAILS'
$worksheet.Cells.Item($row, 2) = 'EMPLOYEE BANK DETAILS'
$worksheet.Cells.Item($row, 3) = '[UC]'
$worksheet.Cells.Item($row, 5) = 'YES'
$row++

# Save the workbook
$workbook.SaveAs("C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx")
$excel.Quit()

# Clean up COM objects
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($worksheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Excel template created successfully at C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx"
