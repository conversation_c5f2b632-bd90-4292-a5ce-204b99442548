// This file handles the splash screen functionality
const { app, BrowserWindow } = require('electron');
const path = require('path');

let splashWindow = null;

/**
 * Creates a splash window that appears immediately
 * @returns {Electron.BrowserWindow} The splash window
 */
function createSplashWindow() {
  // Create the splash window with show: true to make it visible immediately
  splashWindow = new BrowserWindow({
    width: 800,
    height: 600,
    frame: true,
    alwaysOnTop: true,
    show: true, // Show immediately
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    backgroundColor: '#1a237e', // Set background color to match the header
  });

  // Load the splash screen HTML
  splashWindow.loadFile(path.join(__dirname, 'splash.html'));
  
  // Log when the window is shown
  splashWindow.on('show', () => {
    console.log('Splash window shown');
  });

  // Log when the window is ready to show (for debugging)
  splashWindow.once('ready-to-show', () => {
    console.log('Splash window ready-to-show event fired');
    // Window is already visible because of show: true
  });

  return splashWindow;
}

/**
 * Updates the splash screen progress
 * @param {number} progress - Progress percentage (0-100)
 * @param {string} text - Status text to display
 */
function updateSplashScreen(progress, text) {
  if (splashWindow && !splashWindow.isDestroyed()) {
    console.log(`Updating splash screen: ${progress}% - ${text}`);
    splashWindow.webContents.send('splash-message', {
      progress,
      text
    });
  }
}

/**
 * Closes the splash screen
 * @param {function} callback - Function to call after the splash screen is closed
 */
function closeSplashScreen(callback) {
  if (splashWindow && !splashWindow.isDestroyed()) {
    console.log('Closing splash window');
    
    // Set a listener for the closed event
    splashWindow.on('closed', () => {
      console.log('Splash window closed');
      splashWindow = null;
      if (callback) callback();
    });
    
    // Close the window
    splashWindow.close();
  } else {
    // If the splash window doesn't exist or is already destroyed, call the callback immediately
    if (callback) callback();
  }
}

module.exports = {
  createSplashWindow,
  updateSplashScreen,
  closeSplashScreen
};
