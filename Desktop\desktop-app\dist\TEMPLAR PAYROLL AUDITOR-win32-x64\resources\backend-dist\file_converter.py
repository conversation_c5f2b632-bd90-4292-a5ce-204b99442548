#!/usr/bin/env python3
"""
File Converter for Payroll Auditor

This script provides functions to convert between different file formats:
- Excel to Word
- Excel to PDF
- Word to PDF

It uses Python libraries to handle the conversions without relying on external tools.
"""

import os
import sys
import pandas as pd
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
import openpyxl
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def excel_to_docx(excel_path, docx_path):
    """
    Convert Excel file to Word document.

    Args:
        excel_path: Path to the Excel file
        docx_path: Path to save the Word document

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Converting Excel to Word: {excel_path} -> {docx_path}")

        # Load the Excel workbook
        wb = openpyxl.load_workbook(excel_path)

        # Create a new Word document
        doc = Document()

        # Add title
        title = doc.add_heading('Payroll Audit Report', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add timestamp
        timestamp = doc.add_paragraph()
        timestamp.add_run(f"Generated from: {os.path.basename(excel_path)}")
        timestamp.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Process each worksheet
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]

            # Add sheet name as heading
            doc.add_heading(sheet_name, level=2)

            # Get all rows and columns with data
            rows = list(sheet.rows)
            if not rows:
                doc.add_paragraph("No data in this sheet")
                continue

            # Create table - no row limit, handle all data
            num_rows = len(rows)  # Process all rows
            num_cols = len(rows[0])

            # Create table with header row
            table = doc.add_table(rows=num_rows, cols=num_cols)
            table.style = 'Table Grid'

            # Add header row
            header_row = table.rows[0]
            for col_idx, cell in enumerate(rows[0]):
                header_cell = header_row.cells[col_idx]
                header_cell.text = str(cell.value) if cell.value is not None else ""
                # Style header
                for paragraph in header_cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True

            # Add data rows
            for row_idx in range(1, num_rows):
                for col_idx, cell in enumerate(rows[row_idx]):
                    table_cell = table.rows[row_idx].cells[col_idx]
                    table_cell.text = str(cell.value) if cell.value is not None else ""

            # Add a page break after each sheet except the last one
            if sheet_name != wb.sheetnames[-1]:
                doc.add_page_break()

        # Save the Word document
        doc.save(docx_path)
        print(f"Word document saved to: {docx_path}")
        return True

    except Exception as e:
        print(f"Error converting Excel to Word: {str(e)}")
        return False

def excel_to_pdf(excel_path, pdf_path):
    """
    Convert Excel file to PDF.

    Args:
        excel_path: Path to the Excel file
        pdf_path: Path to save the PDF

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Converting Excel to PDF: {excel_path} -> {pdf_path}")

        # Load the Excel workbook
        wb = openpyxl.load_workbook(excel_path)

        # Create a PDF document
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        elements = []

        # Add styles
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        heading_style = styles['Heading2']
        normal_style = styles['Normal']

        # Add title
        elements.append(Paragraph('Payroll Audit Report', title_style))
        elements.append(Paragraph(f"Generated from: {os.path.basename(excel_path)}", normal_style))
        elements.append(Spacer(1, 0.25*inch))

        # Process each worksheet
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]

            # Add sheet name as heading
            elements.append(Paragraph(sheet_name, heading_style))
            elements.append(Spacer(1, 0.1*inch))

            # Get all rows and columns with data
            data = []
            for row in sheet.rows:
                row_data = []
                for cell in row:
                    row_data.append(str(cell.value) if cell.value is not None else "")
                data.append(row_data)

            if not data:
                elements.append(Paragraph("No data in this sheet", normal_style))
                continue

            # Create table with pagination for large datasets
            # Split data into chunks of 500 rows for better PDF rendering
            chunk_size = 500
            data_chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]

            for chunk_index, chunk in enumerate(data_chunks):
                # Add page number if multiple chunks
                if len(data_chunks) > 1 and chunk_index > 0:
                    elements.append(Paragraph(f"Page {chunk_index + 1} of {len(data_chunks)}", normal_style))
                    elements.append(Spacer(1, 0.1*inch))

                # Create table for this chunk
                table = Table(chunk if chunk_index == 0 else [chunk[0]] + chunk[1:])  # Include header row for first chunk

                # Style the table
                style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ])
                table.setStyle(style)

                # Add table to elements
                elements.append(table)
                elements.append(Spacer(1, 0.5*inch))

        # Build the PDF
        doc.build(elements)
        print(f"PDF saved to: {pdf_path}")
        return True

    except Exception as e:
        print(f"Error converting Excel to PDF: {str(e)}")
        return False

def main():
    """Main function to handle command-line arguments."""
    if len(sys.argv) < 4:
        print("Usage: python file_converter.py <format> <input_file> <output_file>")
        print("Formats: docx, pdf")
        print("Example: python file_converter.py docx input.xlsx output.docx")
        print("Example: python file_converter.py pdf input.xlsx output.pdf")
        return

    format_type = sys.argv[1].lower()
    input_file = sys.argv[2]
    output_file = sys.argv[3]

    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist")
        return

    if format_type == "docx":
        if input_file.endswith(".xlsx") or input_file.endswith(".xls"):
            excel_to_docx(input_file, output_file)
        else:
            print("Error: Input file must be an Excel file (.xlsx or .xls)")

    elif format_type == "pdf":
        if input_file.endswith(".xlsx") or input_file.endswith(".xls"):
            excel_to_pdf(input_file, output_file)
        else:
            print("Error: Input file must be an Excel or Word file")

    else:
        print(f"Error: Unsupported format '{format_type}'")
        print("Supported formats: docx, pdf")

if __name__ == "__main__":
    main()