// Data Builder functionality
let dataBuilderDictionary = {};

// Initialize the Data Builder
async function initDataBuilder() {
  // Load the dictionary data
  await loadDictionaryForDataBuilder();

  // Set up tab switching
  setupDataBuilderTabs();

  // Set up select/deselect all buttons
  setupSelectDeselectButtons();

  // Set up event listener for dictionary updates
  setupDictionaryUpdateListener();
}

// Set up event listener for dictionary updates
function setupDictionaryUpdateListener() {
  if (window.appEvents) {
    // Listen for dictionary updates
    window.appEvents.on('dictionaryUpdated', async () => {
      console.log('Dictionary updated, refreshing Data Builder fields');

      // Show notification
      showNotification('Dictionary updated, refreshing fields', 'info');

      // Reload the dictionary data
      await loadDictionaryForDataBuilder();
    });
  }
}

// Show a notification
function showNotification(message, type) {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;

  // Add to the document
  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// Load the dictionary data for the Data Builder
async function loadDictionaryForDataBuilder() {
  try {
    // Get the dictionary from the backend
    dataBuilderDictionary = await window.api.getEnhancedDictionary();

    // Populate the fields tables
    populateFieldsTables();
  } catch (error) {
    console.error('Error loading dictionary for Data Builder:', error);
    showNotification('Error loading dictionary data. Please try again.', 'error');
  }
}

// Populate the fields tables with items from the dictionary
function populateFieldsTables() {
  // Define the sections to populate
  const sections = [
    { id: 'personal-details', name: 'PERSONAL DETAILS' },
    { id: 'earnings', name: 'EARNINGS' },
    { id: 'deductions', name: 'DEDUCTIONS' },
    { id: 'employers-contribution', name: 'EMPLOYERS CONTRIBUTION' },
    { id: 'loans', name: 'LOANS' },
    { id: 'bank-details', name: 'EMPLOYEE BANK DETAILS' }
  ];

  // Populate each section
  sections.forEach(section => {
    populateFieldsTable(section.id, section.name);
  });
}

// Populate a specific fields table
function populateFieldsTable(sectionId, sectionName) {
  const tableBody = document.getElementById(`${sectionId}-fields`);
  if (!tableBody) {
    console.error(`Table body not found for section: ${sectionId}`);
    return;
  }

  // Clear the table
  tableBody.innerHTML = '';

  // Check if the section exists in the dictionary
  if (!dataBuilderDictionary[sectionName] || !dataBuilderDictionary[sectionName].items) {
    console.log(`No items found for section: ${sectionName}`);

    // Add a message row
    const messageRow = document.createElement('tr');
    const messageCell = document.createElement('td');
    messageCell.colSpan = 2;
    messageCell.textContent = 'No items found in dictionary. Add items in the Dictionary Manager.';
    messageCell.style.textAlign = 'center';
    messageCell.style.padding = '20px';
    messageRow.appendChild(messageCell);
    tableBody.appendChild(messageRow);

    return;
  }

  // Get the items for this section
  const items = dataBuilderDictionary[sectionName].items;

  // Add a row for each item
  Object.keys(items).forEach(itemName => {
    const itemData = items[itemName];

    // Create a new row
    const row = document.createElement('tr');

    // Item name cell
    const nameCell = document.createElement('td');
    nameCell.textContent = itemName;
    row.appendChild(nameCell);

    // Include in table cell
    const includeCell = document.createElement('td');

    // Create a toggle switch
    const toggleContainer = document.createElement('label');
    toggleContainer.className = 'toggle-switch';

    const includeCheckbox = document.createElement('input');
    includeCheckbox.type = 'checkbox';
    includeCheckbox.className = 'toggle-input field-selector';
    includeCheckbox.dataset.field = itemName;
    includeCheckbox.dataset.section = sectionName;

    // Check if this is a common field that should be checked by default
    const commonFields = ['Employee ID', 'Employee Name'];
    if (commonFields.includes(itemName)) {
      includeCheckbox.checked = true;
    }

    // Create the slider element
    const slider = document.createElement('span');
    slider.className = 'toggle-slider';

    // Assemble the toggle
    toggleContainer.appendChild(includeCheckbox);
    toggleContainer.appendChild(slider);
    includeCell.appendChild(toggleContainer);
    row.appendChild(includeCell);

    // Add the row to the table
    tableBody.appendChild(row);
  });
}

// Set up tab switching for the Data Builder
function setupDataBuilderTabs() {
  const tabButtons = document.querySelectorAll('.fields-tab-button');
  const tabContents = document.querySelectorAll('.fields-tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // Add active class to the clicked button
      button.classList.add('active');

      // Get the corresponding content ID
      const contentId = button.id.replace('-tab', '-content');

      // Add active class to the corresponding content
      const content = document.getElementById(contentId);
      if (content) {
        content.classList.add('active');
      }
    });
  });
}

// Set up select/deselect all buttons
function setupSelectDeselectButtons() {
  // Define the sections
  const sections = [
    'personal-details',
    'earnings',
    'deductions',
    'employers-contribution',
    'loans',
    'bank-details',
    'general'
  ];

  // Set up buttons for each section
  sections.forEach(section => {
    const selectAllBtn = document.getElementById(`select-all-${section}`);
    const deselectAllBtn = document.getElementById(`deselect-all-${section}`);

    if (selectAllBtn) {
      selectAllBtn.addEventListener('click', () => {
        const checkboxes = document.querySelectorAll(`#${section}-fields .field-selector`);
        checkboxes.forEach(checkbox => {
          checkbox.checked = true;
        });
      });
    }

    if (deselectAllBtn) {
      deselectAllBtn.addEventListener('click', () => {
        const checkboxes = document.querySelectorAll(`#${section}-fields .field-selector`);
        checkboxes.forEach(checkbox => {
          checkbox.checked = false;
        });
      });
    }
  });
}

// Get all selected fields for the Data Builder
function getSelectedFields() {
  const selectedFields = [];

  // Get all checked field selectors
  const checkedFields = document.querySelectorAll('.field-selector:checked');

  checkedFields.forEach(field => {
    const fieldName = field.dataset.field;
    const sectionName = field.dataset.section;

    // For general fields, use the field name directly
    if (sectionName === 'general') {
      selectedFields.push(fieldName);
    } else {
      // For section fields, use the section and field name
      selectedFields.push(`${sectionName}:${fieldName}`);
    }
  });

  return selectedFields;
}

// Initialize the Data Builder when the page loads
document.addEventListener('DOMContentLoaded', () => {
  // Add notification styles
  addNotificationStyles();

  // Initialize the Data Builder
  initDataBuilder();
});

// Add notification styles
function addNotificationStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .notification {
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 10px 20px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      z-index: 1000;
      animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
    }

    .notification.success {
      background-color: #4caf50;
    }

    .notification.error {
      background-color: #f44336;
    }

    .notification.info {
      background-color: #2196F3;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
  `;
  document.head.appendChild(style);
}

// Override the startBuild function to use our new field selection
async function startBuild() {
  // Validate inputs
  if (!buildInputFilePath) {
    if (buildInputFileError) {
      buildInputFileError.textContent = 'Please select a PDF file to process';
    }
    return;
  }

  // Get the selected fields
  const selectedFields = getSelectedFields();

  if (selectedFields.length === 0) {
    buildResults.innerHTML = `
      <div class="error-container">
        <h4>Error Building Data Table</h4>
        <p class="error">Please select at least one field to include in the data table</p>
      </div>
    `;
    return;
  }

  try {
    // Clear previous results
    buildResults.innerHTML = `
      <div class="progress-container">
        <p>Building data table...</p>
        <div class="processing-details-container">
          <h5>Processing Details</h5>
          <div class="processing-details-box" id="processing-details-box"></div>
        </div>
      </div>
    `;

    // Determine the output file path
    let outputFilePath = buildOutputFilePath;
    if (!outputFilePath) {
      const lastDotIndex = buildInputFilePath.lastIndexOf('.');
      const lastSlashIndex = buildInputFilePath.lastIndexOf('\\');
      if (lastDotIndex > 0 && lastSlashIndex > 0) {
        const baseName = buildInputFilePath.substring(lastSlashIndex + 1, lastDotIndex);
        outputFilePath = buildInputFilePath.substring(0, lastSlashIndex + 1) + baseName + '_data.xlsx';
      } else {
        outputFilePath = buildInputFilePath + '_data.xlsx';
      }
    }

    console.log('Starting data build with:', {
      pdfFile: buildInputFilePath,
      outputFile: outputFilePath,
      fields: selectedFields
    });

    // Call the backend process
    const result = await window.api.buildDataTable({
      pdfFile: buildInputFilePath,
      outputFile: outputFilePath,
      fields: selectedFields
    });

    console.log('Received result from backend:', result);

    if (result && result.success) {
      // Display results
      buildResults.innerHTML = `
        <div class="success-container">
          <h4>Data Table Building Completed Successfully</h4>
          <p>The data table has been saved to:</p>
          <p class="file-path">${result.outputFile}</p>
          <div class="action-buttons">
            <button id="open-data-table-btn" class="action-btn">Open Excel</button>
          </div>
        </div>
      `;

      // Add event listeners for the action buttons
      const openDataTableBtn = document.getElementById('open-data-table-btn');
      if (openDataTableBtn) {
        openDataTableBtn.addEventListener('click', () => {
          // Open the data table
          window.open(`file://${result.outputFile}`, '_blank');
        });
      }
    } else {
      // Display error
      buildResults.innerHTML = `
        <div class="error-container">
          <h4>Error Building Data Table</h4>
          <p class="error">${result ? result.error : 'Unknown error occurred'}</p>
        </div>
      `;
    }
  } catch (error) {
    console.error('Error building data table:', error);

    // Display error
    buildResults.innerHTML = `
      <div class="error-container">
        <h4>Error Building Data Table</h4>
        <p class="error">${error.message}</p>
      </div>
    `;
  }
}
