<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Progress Bar Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    
    .progress-container {
      width: 100%;
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
      padding: 10px;
      margin-top: 15px;
    }
    
    .progress-row {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .progress-label {
      width: 120px;
      text-align: left;
      font-size: 14px;
    }
    
    .progress-bar-container {
      flex-grow: 1;
      margin: 0 10px;
      background-color: #e0e0e0;
      height: 12px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }
    
    .progress-bar {
      background-color: #4caf50;
      height: 100%;
      width: 0%;
      position: absolute;
      left: 0;
      top: 0;
      transition: width 0.3s ease-out;
    }
    
    .progress-text {
      width: 100px;
      text-align: right;
      font-size: 14px;
    }
    
    .controls {
      margin-top: 20px;
    }
    
    button {
      padding: 8px 16px;
      margin-right: 10px;
      background-color: #2196f3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    button:hover {
      background-color: #0b7dda;
    }
  </style>
</head>
<body>
  <h2>Progress Bar Test</h2>
  
  <div class="progress-container">
    <div class="progress-row">
      <div class="progress-label">Previous Payslips:</div>
      <div class="progress-bar-container">
        <div id="previous-progress-bar" class="progress-bar"></div>
      </div>
      <div id="previous-progress-text" class="progress-text">000/2946 (0%)</div>
    </div>
    
    <div class="progress-row">
      <div class="progress-label">Current Payslips:</div>
      <div class="progress-bar-container">
        <div id="current-progress-bar" class="progress-bar"></div>
      </div>
      <div id="current-progress-text" class="progress-text">000/2946 (0%)</div>
    </div>
  </div>
  
  <div class="controls">
    <button id="update-previous">Update Previous (10%)</button>
    <button id="update-current">Update Current (20%)</button>
    <button id="reset">Reset</button>
  </div>
  
  <script>
    // Function to update progress bar
    function updateProgressBar(type, currentPage, totalPages) {
      const progressBar = document.getElementById(`${type}-progress-bar`);
      const progressText = document.getElementById(`${type}-progress-text`);
      
      if (!progressBar || !progressText) {
        console.error(`Progress elements not found for ${type}`);
        return;
      }
      
      // Calculate percentage
      const percent = Math.floor((currentPage / totalPages) * 100);
      
      // Format the count with leading zeros
      const formattedCount = String(currentPage).padStart(3, '0');
      const formattedText = `${formattedCount}/${totalPages} (${percent}%)`;
      
      // Update the DOM
      progressBar.style.width = `${percent}%`;
      progressText.textContent = formattedText;
      
      console.log(`Updated ${type} progress: ${formattedText}`);
    }
    
    // Set up event listeners
    document.getElementById('update-previous').addEventListener('click', () => {
      updateProgressBar('previous', 295, 2946);
    });
    
    document.getElementById('update-current').addEventListener('click', () => {
      updateProgressBar('current', 589, 2946);
    });
    
    document.getElementById('reset').addEventListener('click', () => {
      updateProgressBar('previous', 0, 2946);
      updateProgressBar('current', 0, 2946);
    });
  </script>
</body>
</html>
