const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Enable detailed logging
process.env.ELECTRON_ENABLE_LOGGING = true;
process.env.ELECTRON_ENABLE_STACK_DUMPING = true;

console.log('Starting application check...');
console.log('Current directory:', __dirname);

// Check if critical files exist
const files = [
  'index.html',
  'main.js',
  'preload.js',
  'renderer.js',
  'styles.css'
];

console.log('Checking for required files:');
files.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  console.log(`- ${file}: ${exists ? 'Found' : 'MISSING'}`);
});

// Create a minimal window to test
function createTestWindow() {
  console.log('Creating test window...');
  
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  
  win.once('ready-to-show', () => {
    console.log('Test window ready to show');
    win.show();
    
    // Close the window after 2 seconds
    setTimeout(() => {
      console.log('Test complete, closing window');
      win.close();
    }, 2000);
  });
  
  win.loadFile(path.join(__dirname, 'index.html'));
  console.log('Loading index.html');
  
  win.on('closed', () => {
    console.log('Window closed');
    app.quit();
  });
}

app.whenReady().then(() => {
  console.log('Electron app is ready');
  createTestWindow();
}).catch(error => {
  console.error('Failed to initialize app:', error);
});

app.on('window-all-closed', () => {
  console.log('All windows closed, exiting');
  app.quit();
});
