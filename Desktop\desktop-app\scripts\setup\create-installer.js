const { MSICreator } = require('electron-wix-msi');
const path = require('path');

// Define input and output directories
const APP_DIR = path.resolve(__dirname, './dist/TEMPLAR PAYROLL AUDITOR-win32-x64');
const OUT_DIR = path.resolve(__dirname, './dist/windows-installer');

// Create a new MSICreator instance
const msiCreator = new MSICreator({
  appDirectory: APP_DIR,
  outputDirectory: OUT_DIR,
  description: 'A powerful desktop application for auditing and comparing payroll data',
  exe: 'TEMPLAR PAYROLL AUDITOR',
  name: 'TEMPLAR PAYROLL AUDITOR',
  manufacturer: 'Templar',
  version: '1.0.0',
  ui: {
    chooseDirectory: true
  },
  defaultInstallationPath: 'C:\\TEMPLAR PAYROLL AUDITOR'
});

// Create the MSI installer
async function createInstaller() {
  try {
    // Create the .wxs template file
    await msiCreator.create();
    
    // Compile the installer
    await msiCreator.compile();
    
    console.log('Installer created successfully!');
  } catch (error) {
    console.error('Error creating installer:', error);
  }
}

createInstaller();
