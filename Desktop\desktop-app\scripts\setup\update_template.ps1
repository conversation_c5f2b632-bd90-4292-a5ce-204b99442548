$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false

# Open the existing workbook
$workbook = $excel.Workbooks.Open("C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx")

# Get the Dictionary worksheet
$dictSheet = $workbook.Worksheets.Item("Payslip Dictionary")

# Change ITEM to LINE ITEM in header
$dictSheet.Cells.Item(1, 2) = 'LINE ITEM'

# Find and update loan-related value formats
$lastRow = $dictSheet.UsedRange.Rows.Count

for ($row = 2; $row -le $lastRow; $row++) {
    $section = $dictSheet.Cells.Item($row, 1).Text
    $item = $dictSheet.Cells.Item($row, 2).Text
    
    # Update loan-related items
    if ($section -eq "LOANS") {
        if ($item -eq "LOAN" -or $item -eq "BALANCE B/F" -or $item -eq "CURRENT DEDUCTION" -or $item -eq "OUST. BALANCE") {
            # These are column headers
            $dictSheet.Cells.Item($row, 4) = "Column Header"
        }
        elseif ($item -ne "LOANS" -and $item -ne "SALARY ADVANCE-MINS") {
            # These are numeric values
            $dictSheet.Cells.Item($row, 4) = "Numeric with decimal places"
        }
    }
    
    # Update SALARY ADVANCE-MINS value format
    if ($section -eq "LOANS" -and $item -eq "SALARY ADVANCE-MINS") {
        $dictSheet.Cells.Item($row, 4) = "Numeric with decimal places"
    }
}

# Save the workbook
$workbook.Save()
$excel.Quit()

# Clean up COM objects
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($dictSheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Template updated successfully at C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx"
