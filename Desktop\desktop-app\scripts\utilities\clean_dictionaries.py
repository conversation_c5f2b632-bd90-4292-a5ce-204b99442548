"""
Clean Dictionaries Script

This script removes all default dictionary items from the application.
It preserves any user-entered dictionary items and creates empty dictionaries if none exist.
"""

import os
import json
import sys

# Define the path for storing dictionaries
DICT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend-dist', 'dictionaries')
EARNINGS_DICT_PATH = os.path.join(DICT_DIR, 'earnings_dictionary.json')
DEDUCTIONS_DICT_PATH = os.path.join(DICT_DIR, 'deductions_dictionary.json')
LOANS_DICT_PATH = os.path.join(DICT_DIR, 'loans_dictionary.json')

# Ensure the dictionaries directory exists
os.makedirs(DICT_DIR, exist_ok=True)

# Default dictionaries with common variations - used to identify default items
DEFAULT_EARNINGS = {
    "BASIC SALARY": ["BASIC", "BASE SALARY", "BASE PAY", "BASIC PAY"],
    "HOUSING ALLOWANCE": ["HOUSING", "HOUSE ALLOWANCE", "ACCOMMODATION", "HOUSING ALLWNCE"],
    "TRANSPORT ALLOWANCE": ["TRANSPORT", "TRANSPORTATION", "TRANSPORT ALLWNCE"],
    "RESPONSIBILITY ALLOWANCE": ["RESPONSIBILITY", "RESP ALLOWANCE", "RESP ALLWNCE"],
    "OVERTIME": ["OT", "OVER TIME", "O/T", "OVERTIME PAYMENT"],
    "BONUS": ["PERFORMANCE BONUS", "ANNUAL BONUS", "QUARTERLY BONUS"],
    "MEDICAL ALLOWANCE": ["MEDICAL", "HEALTH ALLOWANCE", "MEDICAL ALLWNCE"],
    "UTILITY ALLOWANCE": ["UTILITY", "UTILITIES", "UTILITY ALLWNCE"],
    "ACTING ALLOWANCE": ["ACTING", "ACTING ALLWNCE"],
    "SPECIAL ALLOWANCE": ["SPECIAL", "SPECIAL ALLWNCE"]
}

DEFAULT_DEDUCTIONS = {
    "INCOME TAX": ["PAYE", "TAX", "INCOME TAX DEDUCTION"],
    "PENSION": ["PENSION CONTRIBUTION", "PENSION DEDUCTION", "RETIREMENT"],
    "HEALTH INSURANCE": ["HEALTH", "MEDICAL INSURANCE", "MEDICAL DEDUCTION"],
    "SOCIAL SECURITY": ["SOCIAL SECURITY CONTRIBUTION", "SSF", "SSNIT"],
    "LOAN REPAYMENT": ["LOAN", "LOAN DEDUCTION", "LOAN PAYMENT"],
    "UNION DUES": ["UNION", "UNION CONTRIBUTION", "ASSOCIATION DUES"],
    "PROVIDENT FUND": ["PROVIDENT", "PROVIDENT CONTRIBUTION"],
    "WELFARE": ["WELFARE CONTRIBUTION", "WELFARE FUND"],
    "SALARY ADVANCE": ["ADVANCE", "SALARY ADVANCE DEDUCTION"],
    "CHARITY": ["CHARITY CONTRIBUTION", "DONATION"]
}

DEFAULT_LOANS = {
    "PERSONAL LOAN": ["PERSONAL", "STAFF LOAN", "EMPLOYEE LOAN"],
    "CAR LOAN": ["CAR", "VEHICLE LOAN", "AUTO LOAN"],
    "HOUSING LOAN": ["HOUSING", "MORTGAGE", "HOME LOAN"],
    "EDUCATION LOAN": ["EDUCATION", "SCHOOL LOAN", "STUDY LOAN"],
    "EMERGENCY LOAN": ["EMERGENCY", "URGENT LOAN"],
    "SALARY ADVANCE": ["ADVANCE", "SALARY ADVANCE LOAN"],
    "FURNITURE LOAN": ["FURNITURE", "HOUSEHOLD ITEMS LOAN"],
    "COMPUTER LOAN": ["COMPUTER", "LAPTOP LOAN", "IT EQUIPMENT LOAN"],
    "MEDICAL LOAN": ["MEDICAL", "HEALTH LOAN", "HOSPITAL LOAN"],
    "TRAVEL LOAN": ["TRAVEL", "VACATION LOAN", "HOLIDAY LOAN"]
}

def load_dictionary(dict_path):
    """Load a dictionary from file or return empty dict if it doesn't exist."""
    try:
        if os.path.exists(dict_path):
            with open(dict_path, 'r') as f:
                return json.load(f)
        else:
            return {}
    except Exception as e:
        print(f"Error loading dictionary {dict_path}: {e}")
        return {}

def save_dictionary(dict_path, dictionary):
    """Save a dictionary to file."""
    try:
        with open(dict_path, 'w') as f:
            json.dump(dictionary, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving dictionary {dict_path}: {e}")
        return False

def clean_dictionary(current_dict, default_dict):
    """Remove default items from the dictionary, keeping only user-entered items."""
    cleaned_dict = {}

    # Keep only items that are not in the default dictionary
    for key, value in current_dict.items():
        if key not in default_dict:
            cleaned_dict[key] = value
            print(f"Keeping user-entered item: {key}")

    return cleaned_dict

def main():
    """Main function to clean dictionaries."""
    # Process earnings dictionary
    print("\nCleaning earnings dictionary...")
    earnings_dict = load_dictionary(EARNINGS_DICT_PATH)
    cleaned_earnings = clean_dictionary(earnings_dict, DEFAULT_EARNINGS)
    earnings_saved = save_dictionary(EARNINGS_DICT_PATH, cleaned_earnings)
    print(f"Earnings dictionary cleaned. Kept {len(cleaned_earnings)} user-entered items.")

    # Process deductions dictionary
    print("\nCleaning deductions dictionary...")
    deductions_dict = load_dictionary(DEDUCTIONS_DICT_PATH)
    cleaned_deductions = clean_dictionary(deductions_dict, DEFAULT_DEDUCTIONS)
    deductions_saved = save_dictionary(DEDUCTIONS_DICT_PATH, cleaned_deductions)
    print(f"Deductions dictionary cleaned. Kept {len(cleaned_deductions)} user-entered items.")

    # Process loans dictionary
    print("\nCleaning loans dictionary...")
    loans_dict = load_dictionary(LOANS_DICT_PATH)
    cleaned_loans = clean_dictionary(loans_dict, DEFAULT_LOANS)
    loans_saved = save_dictionary(LOANS_DICT_PATH, cleaned_loans)
    print(f"Loans dictionary cleaned. Kept {len(cleaned_loans)} user-entered items.")

    if earnings_saved and deductions_saved and loans_saved:
        print("\nAll dictionaries have been cleaned successfully!")
        return 0
    else:
        print("\nError cleaning dictionaries.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
