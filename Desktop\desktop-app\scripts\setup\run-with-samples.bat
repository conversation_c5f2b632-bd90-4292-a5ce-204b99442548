@echo off
echo Starting Templar Payroll Auditor with sample files...

set PREV_FILE=..\backend\previous_payroll_sample.pdf
set CURR_FILE=..\backend\current_payroll_sample.pdf
set OUTPUT_DIR=..\backend

echo Previous file: %PREV_FILE%
echo Current file: %CURR_FILE%
echo Output directory: %OUTPUT_DIR%

echo Running Python backend directly...
cd ..\backend
python main.py "%PREV_FILE%" "%CURR_FILE%" "%OUTPUT_DIR%"

echo.
echo Python backend completed. Check the reports in the backend directory.
echo.

pause
