#!/usr/bin/env python3
"""
Read Excel Columns

This script reads the column names from an Excel file and returns them as a JSON array.
"""

import os
import sys
import json
import pandas as pd

def read_excel_columns(file_path, header_row=3, first_column='A'):
    """
    Read the column names from an Excel file.

    Args:
        file_path (str): Path to the Excel file
        header_row (int): Row number (0-based) containing the column headers
        first_column (str): First column letter (e.g., 'A', 'B', etc.)

    Returns:
        list: List of column names
    """
    try:
        print(f"Reading Excel file: {os.path.basename(file_path)}")
        print(f"Header row: {header_row}, First column: {first_column}")

        # Function to convert column letter to index (A=0, B=1, etc.)
        def col_letter_to_index(letter):
            letter = letter.upper()
            result = 0
            for i, char in enumerate(reversed(letter)):
                result += (ord(char) - ord('A') + 1) * (26 ** i)
            return result - 1  # 0-based index

        # Calculate the first column index
        first_col_index = col_letter_to_index(first_column)
        print(f"First column index: {first_col_index}")

        # Read the Excel file with the specified header row
        df = pd.read_excel(file_path, header=header_row)

        # If first column is specified, slice the dataframe
        if first_col_index > 0:
            df = df.iloc[:, first_col_index:]

        # Get the column names
        columns = df.columns.tolist()
        print(f"Found {len(columns)} columns: {columns}")

        return columns
    except Exception as e:
        print(f"Error reading Excel file: {str(e)}", file=sys.stderr)
        return []

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python read_excel_columns.py <excel_file> [header_row] [first_column]", file=sys.stderr)
        sys.exit(1)

    file_path = sys.argv[1]
    header_row = int(sys.argv[2]) if len(sys.argv) > 2 else 3  # Default to row 4 (0-based index)
    first_column = sys.argv[3] if len(sys.argv) > 3 else 'A'  # Default to column A

    columns = read_excel_columns(file_path, header_row, first_column)
    print(json.dumps(columns))
