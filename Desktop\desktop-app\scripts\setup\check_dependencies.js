const { exec, execSync } = require('child_process');
const { dialog } = require('electron');
const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Check if a program exists in PATH or common installation directories
 * @param {string} programName - The name of the program to check
 * @param {Array<string>} possiblePaths - Array of possible installation paths
 * @returns {string|null} - Path to the program if found, null otherwise
 */
function findProgramPath(programName, possiblePaths = []) {
    console.log(`Checking for ${programName} in system...`);

    // First check if it's in PATH using 'where' command
    try {
        console.log(`Checking PATH using 'where ${programName}'...`);
        const result = execSync(`where ${programName} 2>nul`, { encoding: 'utf8', stdio: ['pipe', 'pipe', 'ignore'] });
        if (result && result.trim()) {
            const path = result.trim().split('\n')[0];
            console.log(`Found ${programName} in PATH: ${path}`);
            return path;
        }
    } catch (error) {
        console.log(`${programName} not found using 'where' command`);
    }

    // Try to get the PATH environment variable directly
    try {
        console.log('Checking PATH environment variable directly...');
        const pathEnv = process.env.PATH || '';
        console.log(`PATH: ${pathEnv}`);

        // Split PATH and check each directory
        const pathDirs = pathEnv.split(';');
        for (const dir of pathDirs) {
            if (!dir) continue;

            const exePath = path.join(dir, `${programName}.exe`);
            console.log(`Checking ${exePath}`);
            if (fs.existsSync(exePath)) {
                console.log(`Found ${programName} in PATH at: ${exePath}`);
                return exePath;
            }
        }
    } catch (error) {
        console.log(`Error checking PATH environment variable: ${error.message}`);
    }

    // Check common installation directories
    console.log('Checking common installation directories...');
    for (const dir of possiblePaths) {
        // Check if directory exists first
        if (fs.existsSync(dir)) {
            console.log(`Directory exists: ${dir}`);

            // Check for the executable in this directory
            const exePath = path.join(dir, `${programName}.exe`);
            console.log(`Checking ${exePath}`);
            if (fs.existsSync(exePath)) {
                console.log(`Found ${programName} at: ${exePath}`);
                return exePath;
            }

            // Special case for Tesseract: check if it's in the directory without .exe
            // This is to handle cases where the PATH points to the directory but not the executable
            if (programName === 'tesseract') {
                console.log(`Found Tesseract directory: ${dir}`);
                return dir; // Return the directory path, which is enough to know Tesseract is installed
            }
        } else {
            console.log(`Directory does not exist: ${dir}`);
        }
    }

    // Check Program Files directories
    const programFilesDirs = [
        'C:\\Program Files',
        'C:\\Program Files (x86)'
    ];

    console.log('Searching in Program Files directories...');
    for (const baseDir of programFilesDirs) {
        try {
            if (!fs.existsSync(baseDir)) continue;

            const dirs = fs.readdirSync(baseDir);
            for (const dir of dirs) {
                // Look for directories that might contain the program
                if (dir.toLowerCase().includes(programName.toLowerCase())) {
                    const fullDir = path.join(baseDir, dir);
                    console.log(`Checking ${fullDir} for ${programName}...`);

                    // Check for the executable in this directory
                    const exePath = path.join(fullDir, `${programName}.exe`);
                    if (fs.existsSync(exePath)) {
                        console.log(`Found ${programName} at: ${exePath}`);
                        return exePath;
                    }

                    // Also check in bin subdirectory
                    const binPath = path.join(fullDir, 'bin', `${programName}.exe`);
                    if (fs.existsSync(binPath)) {
                        console.log(`Found ${programName} at: ${binPath}`);
                        return binPath;
                    }
                }
            }
        } catch (error) {
            console.log(`Error searching in ${baseDir}: ${error.message}`);
        }
    }

    console.log(`${programName} not found in any location`);
    return null;
}

/**
 * Add a directory to the PATH environment variable
 * @param {string} dirPath - The directory to add to PATH
 * @returns {boolean} - True if successful, false otherwise
 */
function addToPath(dirPath) {
    try {
        // Get the current PATH
        const currentPath = process.env.PATH || '';

        // Check if the directory is already in PATH
        if (currentPath.includes(dirPath)) {
            console.log(`${dirPath} is already in PATH`);
            return true;
        }

        // Add the directory to PATH
        console.log(`Adding ${dirPath} to PATH...`);
        execSync(`setx PATH "${currentPath};${dirPath}"`, { encoding: 'utf8' });

        // Update the current process's PATH
        process.env.PATH = `${currentPath};${dirPath}`;

        return true;
    } catch (error) {
        console.error(`Error adding ${dirPath} to PATH:`, error);
        return false;
    }
}

/**
 * Check if all required dependencies are installed
 * @param {Electron.BrowserWindow} mainWindow - The main application window
 * @param {boolean} showSuccessMessage - Whether to show a success message when all dependencies are installed
 * @param {boolean} requireTesseract - Whether to require Tesseract OCR to be installed
 * @param {boolean} silentMode - Whether to run in silent mode (no dialogs)
 * @returns {Promise<boolean>} - True if all dependencies are installed
 */
function checkDependencies(mainWindow, showSuccessMessage = true, requireTesseract = false, silentMode = false) {
    return new Promise((resolve) => {
        console.log('Checking dependencies...');

        // Set a timeout to prevent hanging
        const timeout = setTimeout(() => {
            console.log('Dependency check timed out, continuing anyway');
            resolve(true);
        }, 10000); // 10 seconds timeout

        // Check if Python is installed
        const pythonPaths = [
            'C:\\Python39',
            'C:\\Python38',
            'C:\\Python37',
            'C:\\Python310',
            'C:\\Python311',
            'C:\\Program Files\\Python39',
            'C:\\Program Files\\Python38',
            'C:\\Program Files\\Python37',
            'C:\\Program Files\\Python310',
            'C:\\Program Files\\Python311',
            'C:\\Program Files (x86)\\Python39',
            'C:\\Program Files (x86)\\Python38',
            'C:\\Program Files (x86)\\Python37',
            'C:\\Program Files (x86)\\Python310',
            'C:\\Program Files (x86)\\Python311'
        ];

        const pythonPath = findProgramPath('python', pythonPaths);

        if (!pythonPath) {
            console.error('Python is not installed or not in PATH. Please install Python 3.8 or higher.');
            // Don't show error dialog during startup - just log the error and continue
            clearTimeout(timeout);
            resolve(false);
            return;
        }

        // Add Python to PATH if not already there
        const pythonDir = path.dirname(pythonPath);
        addToPath(pythonDir);

        // Check if Tesseract OCR is installed
        // We only check standard installation locations to ensure consistency
        const tesseractPaths = [
            // Standard installation locations
            'C:\\Program Files\\Tesseract-OCR',
            'C:\\Program Files\\Tesseract-OCR\\bin',
            'C:\\Program Files (x86)\\Tesseract-OCR',
            'C:\\Program Files (x86)\\Tesseract-OCR\\bin'
        ];

        // Check for Tesseract in the system PATH
        console.log('Checking for Tesseract in the system PATH variable...');
        try {
            // Use execSync to get the system PATH
            const systemPath = execSync('echo %PATH%', { encoding: 'utf8' }).trim();
            console.log('System PATH:', systemPath);

            // Check if Tesseract-OCR is in the PATH
            if (systemPath.includes('Tesseract-OCR')) {
                console.log('Found Tesseract-OCR in system PATH');

                // Extract the Tesseract path from the PATH
                const pathParts = systemPath.split(';');
                for (const part of pathParts) {
                    if (part.includes('Tesseract-OCR')) {
                        console.log('Found Tesseract path in system PATH:', part);
                        // Only add if not already in the list
                        if (!tesseractPaths.includes(part)) {
                            tesseractPaths.push(part);
                        }
                    }
                }
            }
        } catch (error) {
            console.log('Error checking system PATH:', error.message);
        }

        const tesseractPath = findProgramPath('tesseract', tesseractPaths);

        // Special case: if we found the Tesseract directory but not the executable
        if (!tesseractPath && fs.existsSync('C:\\Program Files\\Tesseract-OCR')) {
            console.log('Found Tesseract directory but not the executable');
            tesseractPath = 'C:\\Program Files\\Tesseract-OCR'; // Use the directory path
            console.log('Using Tesseract directory path:', tesseractPath);
        }

        if (!tesseractPath) {
            console.log('Tesseract OCR not found');

            // Only show dialog and require installation if requireTesseract is true AND mainWindow is provided AND not in silent mode
            // This prevents dialogs from showing during startup
            if (requireTesseract && mainWindow && !silentMode) {
                // Only show the dialog if we're not in the startup process
                // We determine this by checking if mainWindow is visible
                if (mainWindow.isVisible()) {
                    const installChoice = dialog.showMessageBoxSync(mainWindow, {
                        type: 'question',
                        buttons: ['Install Now', 'Cancel'],
                        defaultId: 0,
                        title: 'Tesseract OCR Not Found',
                        message: 'Tesseract OCR is required for accurate Employee No. extraction but is not installed.',
                        detail: 'Tesseract OCR is essential for achieving 99.7% accuracy in Employee No. extraction. Would you like to install it now?'
                    });

                    if (installChoice === 0) {
                        // Run the installer script silently
                        const installerPath = path.join(__dirname, 'install_dependencies.bat');
                        exec(`"${installerPath}"`, { windowsHide: true }, (error) => {
                            if (error) {
                                clearTimeout(timeout);
                                console.error('Failed to run installer:', error);
                                showDependencyError(mainWindow, 'Failed to run the installer. Please run install_dependencies.bat manually.', silentMode);
                                resolve(false);
                                return;
                            }

                            dialog.showMessageBoxSync(mainWindow, {
                                type: 'info',
                                title: 'Installation Started',
                                message: 'Tesseract OCR installation has been started.',
                                detail: 'Please follow the instructions in the installer window. You may need to restart the application after installation.'
                            });

                            // Continue without Tesseract for now
                            resolve(true);
                        });
                    } else {
                        // User chose not to install
                        dialog.showMessageBoxSync(mainWindow, {
                            type: 'warning',
                            title: 'Tesseract OCR Not Installed',
                            message: 'The application will continue without Tesseract OCR.',
                            detail: 'WARNING: Employee No. extraction will be significantly less accurate (below 99.7% required accuracy). You can install Tesseract later by running install_dependencies.bat.'
                        });
                        resolve(true);
                    }
                    return;
                } else {
                    // We're in the startup process, just log the issue and continue
                    console.log('Tesseract OCR not found, but we are in startup process. Will show message later.');
                    resolve(false);
                    return;
                }
            }
            // If not requiring Tesseract or we're in startup, just continue
        } else {
            // Add Tesseract to PATH if not already there
            let tesseractDir;

            // Check if tesseractPath is a directory or a file
            if (fs.existsSync(tesseractPath) && fs.statSync(tesseractPath).isDirectory()) {
                // It's a directory path
                tesseractDir = tesseractPath;
                console.log('Tesseract OCR directory found at:', tesseractDir);
            } else {
                // It's a file path
                tesseractDir = path.dirname(tesseractPath);
                console.log('Tesseract OCR executable found at:', tesseractPath);
            }

            addToPath(tesseractDir);
            console.log('Tesseract OCR is installed and added to PATH if needed');
        }

        // Check Python packages
        console.log('Checking Python packages...');

        // Use a more reliable way to check for installed packages
        let missingPackages = [];
        try {
            const stdout = execSync('pip list', { encoding: 'utf8' });
            console.log('Successfully retrieved pip list');

            const requiredPackages = ['pymupdf', 'opencv-python', 'pytesseract', 'pillow', 'numpy', 'pandas', 'pdfplumber', 'PyPDF2', 'matplotlib', 'openpyxl', 'python-docx'];

            // Convert pip list output to lowercase for case-insensitive comparison
            const pipListLower = stdout.toLowerCase();

            for (const pkg of requiredPackages) {
                // Check if package name appears in pip list (case-insensitive)
                if (!pipListLower.includes(pkg.toLowerCase())) {
                    console.log(`Package missing: ${pkg}`);
                    missingPackages.push(pkg);
                } else {
                    console.log(`Package found: ${pkg}`);
                }
            }
        } catch (error) {
            console.error('Error checking Python packages:', error);
            clearTimeout(timeout);
            showDependencyError(mainWindow, 'Failed to check Python packages. Please run install_dependencies.bat manually.', silentMode);
            resolve(false);
            return;
        }

        if (missingPackages.length > 0) {
            console.log(`Missing Python packages: ${missingPackages.join(', ')}`);

            // Only show dialog if mainWindow is provided and visible (not during startup) and not in silent mode
            if (mainWindow && mainWindow.isVisible() && !silentMode) {
                const installChoice = dialog.showMessageBoxSync(mainWindow, {
                    type: 'question',
                    buttons: ['Install Now', 'Cancel'],
                    defaultId: 0,
                    title: 'Missing Python Packages',
                    message: `The following Python packages are missing: ${missingPackages.join(', ')}`,
                    detail: 'Would you like to install them now?'
                });

                if (installChoice === 0) {
                    // Run the installer script silently
                    const installerPath = path.join(__dirname, 'install_dependencies.bat');
                    exec(`"${installerPath}"`, { windowsHide: true }, (error) => {
                        if (error) {
                            clearTimeout(timeout);
                            console.error('Failed to run installer:', error);
                            showDependencyError(mainWindow, 'Failed to run the installer. Please run install_dependencies.bat manually.', silentMode);
                            resolve(false);
                            return;
                        }

                        dialog.showMessageBoxSync(mainWindow, {
                            type: 'info',
                            title: 'Installation Started',
                            message: 'Python packages installation has been started.',
                            detail: 'Please wait for the installation to complete. You may need to restart the application after installation.'
                        });

                        // Continue without the packages for now
                        clearTimeout(timeout);
                        resolve(true);
                    });
                } else {
                    // User chose not to install
                    dialog.showMessageBoxSync(mainWindow, {
                        type: 'warning',
                        title: 'Missing Python Packages',
                        message: 'The application will continue without the required Python packages.',
                        detail: 'Some features may not work correctly. You can install them later by running install_dependencies.bat.'
                    });
                    clearTimeout(timeout);
                    resolve(true);
                }
                return;
            } else {
                // We're in the startup process, just log the issue and continue
                console.log('Missing Python packages, but we are in startup process. Will show message later.');
                clearTimeout(timeout);
                resolve(false);
                return;
            }
        }

        // All dependencies are installed
        console.log('All dependencies are installed.');

        // Show success message if requested and not in silent mode
        if (showSuccessMessage && !silentMode) {
            dialog.showMessageBoxSync(mainWindow, {
                type: 'info',
                title: 'Dependencies Check',
                message: 'All dependencies are installed',
                detail: 'The application is ready to use with all required dependencies.'
            });
        }

        // Clear the timeout
        clearTimeout(timeout);

        resolve(true);
    });
}

/**
 * Show a dependency error message
 * @param {Electron.BrowserWindow|null} mainWindow - The main application window (can be null for silent mode)
 * @param {string} message - The error message to display
 * @param {boolean} silentMode - Whether to run in silent mode (no dialogs)
 */
function showDependencyError(mainWindow, message, silentMode = false) {
    console.error(`Dependency Error: ${message}`);

    // Only show dialog if mainWindow is provided and not in silent mode
    if (mainWindow && !silentMode) {
        dialog.showMessageBoxSync(mainWindow, {
            type: 'error',
            title: 'Dependency Error',
            message: 'Missing Dependencies',
            detail: message
        });
    }
}

module.exports = { checkDependencies };
