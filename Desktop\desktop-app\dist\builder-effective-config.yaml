directories:
  output: dist
  buildResources: build
appId: com.templar.payrollauditor
productName: TEMPLAR PAYROLL AUDITOR
win:
  target: nsis
  icon: build/icon.ico
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: false
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: TEMPLAR PAYROLL AUDITOR
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  installerSidebar: build/installerSidebar.bmp
  uninstallerSidebar: build/uninstallerSidebar.bmp
artifactName: TEMPLAR PAYROLL AUDITOR Setup ${version}.${ext}
extraResources:
  - from: backend-dist
    to: backend
  - from: vendor
    to: vendor
extraFiles:
  - from: python-embedded
    to: python-embedded
asar: true
asarUnpack:
  - node_modules/adm-zip/**/*
files: []
electronVersion: 28.3.3
