#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Templar Payroll Auditor - Backend Processor
This script processes payroll PDF files and generates comparison reports.
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
import PyPDF2
import pdfplumber
from openpyxl import Workbook
import pandas as pd

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file using multiple methods for better results."""
    print(f"Extracting text from: {pdf_path}")

    all_text = ""

    # Method 1: PyPDF2
    try:
        print("Trying extraction with PyPDF2...")
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                page_text = page.extract_text() or ""
                all_text += page_text + "\n"
                print(f"  Page {page_num+1}: {len(page_text)} characters extracted")
    except Exception as e:
        print(f"PyPDF2 extraction error: {e}")

    # Method 2: pdfplumber (often better for complex layouts)
    try:
        print("Trying extraction with pdfplumber...")
        plumber_text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for i, page in enumerate(pdf.pages):
                page_text = page.extract_text() or ""
                plumber_text += page_text + "\n"
                print(f"  Page {i+1}: {len(page_text)} characters extracted")

        # If pdfplumber got more text, use that instead
        if len(plumber_text.strip()) > len(all_text.strip()):
            print("Using pdfplumber results (more text extracted)")
            all_text = plumber_text
    except Exception as e:
        print(f"pdfplumber extraction error: {e}")

    # Print a sample of the extracted text for debugging
    text_sample = all_text[:500] + "..." if len(all_text) > 500 else all_text
    print(f"Extracted text sample:\n{text_sample}")

    return all_text

def parse_payroll_data(text):
    """Parse the extracted text to identify payroll data."""
    print("Parsing payroll data...")

    # More robust parser for real payslips
    lines = text.split('\n')
    employees = []
    current_employee = None

    # Common patterns in payslips
    employee_patterns = [
        "Employee ID:", "ID:", "Employee:", "Staff ID:", "Personnel No.:",
        "Employee Number:", "Emp. No.:", "Payroll No.:"
    ]

    salary_patterns = [
        "Salary", "Gross Pay", "Gross Salary", "Basic Salary", "Total Earnings",
        "Gross Income", "Total Gross"
    ]

    deduction_patterns = [
        "Deduction", "Tax", "PAYE", "Income Tax", "National Insurance",
        "Pension", "Insurance", "Health", "Loan", "Contribution"
    ]

    net_pay_patterns = [
        "Net Pay", "Net Salary", "Take Home", "Net Income", "Amount Payable",
        "Total Net", "Net Amount"
    ]

    print("Scanning for employee information...")

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue

        # Debug: Print every 20th line to see what we're parsing
        if i % 20 == 0:
            print(f"Line {i}: {line[:50]}...")

        # Look for employee ID patterns
        if any(pattern in line for pattern in employee_patterns):
            print(f"Found potential employee identifier: {line}")
            if current_employee:
                print(f"Adding employee: {current_employee['id']}")
                employees.append(current_employee)

            # Extract employee ID - try to get just the ID number if possible
            id_parts = line.split(":")
            if len(id_parts) > 1:
                emp_id = id_parts[1].strip()
            else:
                emp_id = line

            current_employee = {
                "id": emp_id,
                "salary": None,
                "deductions": [],
                "net_pay": None
            }

            # Try to find employee name in nearby lines
            name_search_range = 3  # Look 3 lines before and after
            for j in range(max(0, i-name_search_range), min(len(lines), i+name_search_range+1)):
                if j != i and "Name" in lines[j]:
                    current_employee["name"] = lines[j].replace("Name:", "").replace("Name", "").strip()
                    print(f"Found employee name: {current_employee['name']}")
                    break

        # Look for salary information
        elif any(pattern in line for pattern in salary_patterns):
            if current_employee:
                print(f"Found potential salary info: {line}")
                # Extract numeric value
                amount = extract_amount_from_line(line)
                if amount:
                    current_employee["salary"] = amount
                    print(f"Extracted salary: {amount}")

        # Look for deductions
        elif any(pattern in line for pattern in deduction_patterns):
            if current_employee:
                print(f"Found potential deduction: {line}")
                current_employee["deductions"].append(line)

                # Try to extract the deduction amount
                amount = extract_amount_from_line(line)
                if amount:
                    print(f"Extracted deduction amount: {amount}")

        # Look for net pay
        elif any(pattern in line for pattern in net_pay_patterns):
            if current_employee:
                print(f"Found potential net pay: {line}")
                # Extract numeric value
                amount = extract_amount_from_line(line)
                if amount:
                    current_employee["net_pay"] = amount
                    print(f"Extracted net pay: {amount}")

    # Add the last employee if there is one
    if current_employee:
        print(f"Adding final employee: {current_employee['id']}")
        employees.append(current_employee)

    print(f"Found {len(employees)} employees in the payroll data")

    # If no employees were found, try a more aggressive approach
    if not employees:
        print("No employees found with standard patterns, trying alternative approach...")
        employees = extract_employees_alternative(text)

    return employees

def extract_amount_from_line(line):
    """Extract a monetary amount from a line of text."""
    # Remove currency symbols
    for symbol in ['$', '£', '€', '¥', 'GHS', 'GH¢', 'GHC']:
        line = line.replace(symbol, '')

    # Look for patterns like numbers with commas and decimal points
    import re
    amount_patterns = [
        r'\b\d{1,3}(?:,\d{3})*\.\d{2}\b',  # 1,234.56
        r'\b\d+\.\d{2}\b',                 # 1234.56
        r'\b\d{1,3}(?:,\d{3})*\b',         # 1,234
        r'\b\d+\b'                         # 1234
    ]

    for pattern in amount_patterns:
        matches = re.findall(pattern, line)
        if matches:
            # Return the last match, which is often the amount
            return matches[-1]

    return None

def extract_employees_alternative(text):
    """Alternative method to extract employee data when standard patterns fail."""
    print("Using alternative extraction method...")

    # Look for any lines with monetary amounts
    lines = text.split('\n')
    employees = []

    # Try to identify sections that might be for different employees
    current_section = []
    sections = []

    for line in lines:
        if not line.strip():
            if current_section:
                sections.append('\n'.join(current_section))
                current_section = []
        else:
            current_section.append(line)

    # Add the last section if there is one
    if current_section:
        sections.append('\n'.join(current_section))

    print(f"Identified {len(sections)} potential employee sections")

    # Process each section
    for i, section in enumerate(sections):
        # Skip very short sections
        if len(section) < 50:
            continue

        print(f"Processing section {i+1}...")

        # Create a generic employee entry
        employee = {
            "id": f"Employee_{i+1}",
            "salary": None,
            "deductions": [],
            "net_pay": None
        }

        # Look for amounts that might be salary or net pay
        amounts = []
        for line in section.split('\n'):
            amount = extract_amount_from_line(line)
            if amount:
                amounts.append((line, amount))

        # If we found amounts, try to identify salary and net pay
        if amounts:
            # Assume the largest amount is the salary
            amounts.sort(key=lambda x: float(x[1].replace(',', '')), reverse=True)
            if amounts:
                employee["salary"] = amounts[0][1]
                print(f"Assigned salary: {amounts[0][1]} from line: {amounts[0][0]}")

            # Assume the last amount might be net pay
            if len(amounts) > 1:
                employee["net_pay"] = amounts[-1][1]
                print(f"Assigned net pay: {amounts[-1][1]} from line: {amounts[-1][0]}")

        employees.append(employee)

    return employees

def compare_payrolls(prev_data, curr_data):
    """Compare previous and current payroll data."""
    print("Comparing payroll data...")

    comparison = []

    # Create a dictionary of previous employees by ID for easy lookup
    prev_dict = {emp["id"]: emp for emp in prev_data}

    print(f"Previous payroll has {len(prev_data)} employees")
    print(f"Current payroll has {len(curr_data)} employees")

    # Function to safely convert amount strings to float for comparison
    def parse_amount(amount_str):
        if not amount_str:
            return 0.0
        try:
            # Remove any non-numeric characters except decimal point
            clean_str = ''.join(c for c in amount_str if c.isdigit() or c == '.')
            return float(clean_str)
        except (ValueError, TypeError):
            print(f"Warning: Could not parse amount '{amount_str}'")
            return 0.0

    # Function to format amount for display
    def format_amount(amount_str):
        if not amount_str:
            return "N/A"
        return amount_str

    # Process current employees
    for curr_emp in curr_data:
        emp_id = curr_emp["id"]
        print(f"Processing current employee: {emp_id}")

        # Get employee name if available
        emp_name = curr_emp.get("name", "Unknown")

        result = {
            "id": emp_id,
            "name": emp_name,
            "current_salary": format_amount(curr_emp["salary"]),
            "current_net": format_amount(curr_emp["net_pay"]),
            "changes": []
        }

        # Check if employee exists in previous payroll
        if emp_id in prev_dict:
            prev_emp = prev_dict[emp_id]
            print(f"Found matching employee in previous payroll")

            result["previous_salary"] = format_amount(prev_emp["salary"])
            result["previous_net"] = format_amount(prev_emp["net_pay"])

            # Check for salary changes
            prev_salary = parse_amount(prev_emp["salary"])
            curr_salary = parse_amount(curr_emp["salary"])

            if prev_salary != curr_salary:
                diff = curr_salary - prev_salary
                percent = (diff / prev_salary * 100) if prev_salary else 0

                change_msg = f"Salary changed from {format_amount(prev_emp['salary'])} to {format_amount(curr_emp['salary'])}"
                if diff > 0:
                    change_msg += f" (increase of {diff:.2f}, {percent:.2f}%)"
                else:
                    change_msg += f" (decrease of {abs(diff):.2f}, {abs(percent):.2f}%)"

                result["changes"].append(change_msg)
                print(change_msg)

            # Check for net pay changes
            prev_net = parse_amount(prev_emp["net_pay"])
            curr_net = parse_amount(curr_emp["net_pay"])

            if prev_net != curr_net:
                diff = curr_net - prev_net
                percent = (diff / prev_net * 100) if prev_net else 0

                change_msg = f"Net pay changed from {format_amount(prev_emp['net_pay'])} to {format_amount(curr_emp['net_pay'])}"
                if diff > 0:
                    change_msg += f" (increase of {diff:.2f}, {percent:.2f}%)"
                else:
                    change_msg += f" (decrease of {abs(diff):.2f}, {abs(percent):.2f}%)"

                result["changes"].append(change_msg)
                print(change_msg)

            # Check for deduction changes
            prev_deductions = set(prev_emp["deductions"])
            curr_deductions = set(curr_emp["deductions"])

            for deduction in curr_deductions - prev_deductions:
                change_msg = f"New deduction: {deduction}"
                result["changes"].append(change_msg)
                print(change_msg)

            for deduction in prev_deductions - curr_deductions:
                change_msg = f"Removed deduction: {deduction}"
                result["changes"].append(change_msg)
                print(change_msg)

            # If no changes were detected
            if not result["changes"]:
                result["changes"].append("No significant changes detected")
                print("No significant changes detected")

        else:
            change_msg = "New employee (not in previous payroll)"
            result["changes"].append(change_msg)
            print(change_msg)

        comparison.append(result)

    # Check for employees who were removed
    for prev_emp_id in prev_dict:
        if not any(emp["id"] == prev_emp_id for emp in curr_data):
            prev_emp = prev_dict[prev_emp_id]
            emp_name = prev_emp.get("name", "Unknown")

            print(f"Employee removed: {prev_emp_id} ({emp_name})")

            comparison.append({
                "id": prev_emp_id,
                "name": emp_name,
                "previous_salary": format_amount(prev_emp["salary"]),
                "previous_net": format_amount(prev_emp["net_pay"]),
                "current_salary": "N/A",
                "current_net": "N/A",
                "changes": ["Employee removed (not in current payroll)"]
            })

    print(f"Comparison complete. Found {len(comparison)} entries.")
    return comparison

def generate_reports(comparison_data, output_dir):
    """Generate reports in various formats."""
    print("Generating reports...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create Excel report
    excel_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.xlsx")
    wb = Workbook()

    # Summary worksheet
    ws_summary = wb.active
    ws_summary.title = "Summary"

    # Add title and timestamp
    ws_summary.cell(row=1, column=1, value="Payroll Comparison Report")
    ws_summary.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add statistics
    ws_summary.cell(row=4, column=1, value="Statistics:")
    ws_summary.cell(row=5, column=1, value="Total employees analyzed:")
    ws_summary.cell(row=5, column=2, value=len(comparison_data))

    # Count employees with changes
    employees_with_changes = sum(1 for emp in comparison_data if any(change != "No significant changes detected" for change in emp["changes"]))
    ws_summary.cell(row=6, column=1, value="Employees with changes:")
    ws_summary.cell(row=6, column=2, value=employees_with_changes)

    # Count new employees
    new_employees = sum(1 for emp in comparison_data if "New employee" in emp["changes"][0])
    ws_summary.cell(row=7, column=1, value="New employees:")
    ws_summary.cell(row=7, column=2, value=new_employees)

    # Count removed employees
    removed_employees = sum(1 for emp in comparison_data if "Employee removed" in emp["changes"][0])
    ws_summary.cell(row=8, column=1, value="Removed employees:")
    ws_summary.cell(row=8, column=2, value=removed_employees)

    # Detailed comparison worksheet
    ws_detail = wb.create_sheet("Detailed Comparison")

    # Add headers
    headers = ["Employee ID", "Employee Name", "Previous Salary", "Current Salary", "Change (%)",
               "Previous Net Pay", "Current Net Pay", "Change (%)", "Changes"]
    for col, header in enumerate(headers, 1):
        ws_detail.cell(row=1, column=col, value=header)

    # Add data
    for row, emp in enumerate(comparison_data, 2):
        ws_detail.cell(row=row, column=1, value=emp["id"])
        ws_detail.cell(row=row, column=2, value=emp.get("name", "Unknown"))

        # Salary data
        prev_salary = emp.get("previous_salary", "N/A")
        curr_salary = emp.get("current_salary", "N/A")
        ws_detail.cell(row=row, column=3, value=prev_salary)
        ws_detail.cell(row=row, column=4, value=curr_salary)

        # Calculate salary change percentage
        if prev_salary != "N/A" and curr_salary != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_salary if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_salary if c.isdigit() or c == '.'))
                if prev_val > 0:
                    change_pct = (curr_val - prev_val) / prev_val * 100
                    ws_detail.cell(row=row, column=5, value=f"{change_pct:.2f}%")
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=5, value="N/A")
        else:
            ws_detail.cell(row=row, column=5, value="N/A")

        # Net pay data
        prev_net = emp.get("previous_net", "N/A")
        curr_net = emp.get("current_net", "N/A")
        ws_detail.cell(row=row, column=6, value=prev_net)
        ws_detail.cell(row=row, column=7, value=curr_net)

        # Calculate net pay change percentage
        if prev_net != "N/A" and curr_net != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_net if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_net if c.isdigit() or c == '.'))
                if prev_val > 0:
                    change_pct = (curr_val - prev_val) / prev_val * 100
                    ws_detail.cell(row=row, column=8, value=f"{change_pct:.2f}%")
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=8, value="N/A")
        else:
            ws_detail.cell(row=row, column=8, value="N/A")

        # Changes
        ws_detail.cell(row=row, column=9, value="\n".join(emp["changes"]))

    # Format the worksheets
    for ws in [ws_summary, ws_detail]:
        # Adjust column widths
        for col in range(1, 10):
            max_length = 0
            for row in range(1, ws.max_row + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max_length + 2
            ws.column_dimensions[chr(64 + col)].width = adjusted_width

    # Save Excel file
    wb.save(excel_path)
    print(f"Excel: {excel_path}")

    # Create CSV report
    csv_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.csv")

    # Prepare data for CSV (flatten the changes array)
    csv_data = []
    for emp in comparison_data:
        emp_copy = emp.copy()
        emp_copy["changes"] = "; ".join(emp["changes"])
        csv_data.append(emp_copy)

    df = pd.DataFrame(csv_data)
    df.to_csv(csv_path, index=False)
    print(f"CSV: {csv_path}")

    # Create JSON report
    json_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.json")
    with open(json_path, 'w') as f:
        json.dump(comparison_data, f, indent=2)
    print(f"JSON: {json_path}")

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path
    }

    print("Report generation complete.")
    return reports

def main():
    """Main function to process payroll PDFs."""
    parser = argparse.ArgumentParser(description="Templar Payroll Auditor")
    parser.add_argument("prev_file", help="Path to the previous payroll PDF")
    parser.add_argument("curr_file", help="Path to the current payroll PDF")
    parser.add_argument("output_dir", help="Directory to save output reports")

    args = parser.parse_args()

    # Validate input files
    if not os.path.exists(args.prev_file):
        print(f"Error: Previous payroll file not found: {args.prev_file}")
        return 1

    if not os.path.exists(args.curr_file):
        print(f"Error: Current payroll file not found: {args.curr_file}")
        return 1

    # Create output directory if it doesn't exist
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    try:
        # Extract text from PDFs
        prev_text = extract_text_from_pdf(args.prev_file)
        curr_text = extract_text_from_pdf(args.curr_file)

        # Parse payroll data
        prev_data = parse_payroll_data(prev_text)
        curr_data = parse_payroll_data(curr_text)

        # Compare payrolls
        comparison = compare_payrolls(prev_data, curr_data)

        # Generate reports
        reports = generate_reports(comparison, args.output_dir)

        print("Payroll audit completed successfully.")
        return 0

    except Exception as e:
        print(f"Error processing payroll files: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
