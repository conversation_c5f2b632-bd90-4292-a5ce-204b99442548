#!/usr/bin/env python3
"""
Script to add month and year parameters to improved_payroll_parser.py
"""

import re
import os

# Path to the improved_payroll_parser.py file
parser_path = os.path.join('..', 'backend', 'improved_payroll_parser.py')

# Read the original file
with open(parser_path, 'r') as f:
    content = f.read()

# Find the position to add the new parameters
pattern = r"(compare_parser\.add_argument\('--id-field')"
replacement = r"compare_parser.add_argument('--current-month', help='Current month name')\n    compare_parser.add_argument('--current-year', help='Current year')\n    compare_parser.add_argument('--previous-month', help='Previous month name')\n    compare_parser.add_argument('--previous-year', help='Previous year')\n    \1"

# Replace the pattern
modified_content = re.sub(pattern, replacement, content)

# Write the modified content back to the file
with open(parser_path, 'w') as f:
    f.write(modified_content)

print(f"Successfully added month and year parameters to {parser_path}")
