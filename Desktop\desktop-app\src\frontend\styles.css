/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

body {
  background-color: #f0f0f0;
}

/* Splash Screen Styles removed */

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header styles */
.app-header {
  background-color: #1a237e;
  color: white;
  padding: 20px;
  text-align: center;
}

.title-container h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.title-container h2 {
  font-size: 20px;
  font-weight: normal;
}

/* Navigation styles */
.app-nav {
  background-color: #283593;
  display: flex;
  padding: 0 10px;
}

.nav-button {
  background-color: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 15px 20px;
  transition: background-color 0.3s;
}

.nav-button:hover {
  background-color: #3949ab;
}

.nav-button.active {
  background-color: #1976d2;
}

/* Main content styles */
.app-content {
  flex: 1;
  padding: 20px;
}

.content-container {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.hidden {
  display: none;
}

/* Payroll section styles */
.payroll-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.payroll-section {
  flex: 1;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.payroll-section h3 {
  background-color: #1976d2;
  color: white;
  padding: 10px;
  margin: -20px -20px 20px -20px;
  border-radius: 5px 5px 0 0;
}

.file-selector p {
  margin-bottom: 10px;
}

.file-input-container {
  display: flex;
  margin-bottom: 10px;
}

.file-input-container input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
}

.file-input-container button {
  background-color: #e0e0e0;
  border: 1px solid #ccc;
  border-left: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  padding: 8px 15px;
}

.file-input-container button:hover {
  background-color: #d0d0d0;
}

.error-message {
  color: #f44336;
  font-size: 14px;
  min-height: 20px;
}

.helper-text {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
  font-style: italic;
}

/* ID field selector styles */
.id-field-container {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 20px auto;
  max-width: 600px;
  padding: 15px;
  text-align: left;
}

.id-field-container label {
  display: block;
  font-weight: bold;
  margin-bottom: 8px;
}

.id-field-container select {
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
  padding: 8px;
  width: 100%;
}

.field-description {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

/* Month selector styles */
.month-selector {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.month-selector label {
  margin-right: 8px;
  font-weight: bold;
  color: #333;
}

.month-selector select {
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 15px;
  background-color: white;
  font-size: 14px;
}

/* Signature container styles */
.signature-container {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Compact signature container */
.signature-container.compact {
  padding: 8px;
}

.signature-row {
  display: flex;
  gap: 15px;
}

.signature-field {
  margin-bottom: 10px;
  flex: 1;
}

.signature-field label {
  display: block;
  margin-bottom: 3px;
  font-weight: bold;
  color: #333;
  font-size: 13px;
}

.signature-field input {
  width: 100%;
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 13px;
}

/* Action button styles */
.action-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

#start-audit-btn, #start-sort-btn, #start-build-btn {
  background-color: #4caf50;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 12px 30px;
  transition: background-color 0.3s;
}

#start-audit-btn:hover, #start-sort-btn:hover, #start-build-btn:hover {
  background-color: #45a049;
}

.stop-button {
  background-color: #f44336;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  margin-top: 10px;
  transition: background-color 0.3s;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.stop-button:hover {
  background-color: #d32f2f;
}

/* Comparison results styles */
#comparison-results {
  margin-top: 20px;
}

.results-container {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.results-container h4 {
  border-bottom: 1px solid #eee;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.comparison-summary {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 20px;
  padding: 15px;
}

.comparison-summary p {
  margin: 8px 0;
}

.changes-list {
  list-style-type: none;
  padding-left: 0;
}

.changes-list > li {
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.changes-list > li:last-child {
  border-bottom: none;
}

.changes-list ul {
  list-style-type: disc;
  padding-left: 20px;
}

.report-links {
  list-style-type: none;
  padding-left: 0;
}

.report-links li {
  margin-bottom: 10px;
}

.report-buttons-container {
  display: flex;
  gap: 15px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.report-button {
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 25px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.report-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.report-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.report-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.excel-button {
  background-color: #217346; /* Excel green */
}

.excel-button:hover:not(.disabled) {
  background-color: #1e6b3e;
}

.word-button {
  background-color: #2b579a; /* Word blue */
}

.word-button:hover:not(.disabled) {
  background-color: #254a87;
}

.pdf-button {
  background-color: #b7472a; /* PDF red */
}

.pdf-button:hover:not(.disabled) {
  background-color: #a33f25;
}

.note {
  color: #666;
  font-style: italic;
  margin-top: 15px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.comparison-table th, .comparison-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.comparison-table th {
  background-color: #f2f2f2;
}

.comparison-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.positive-change {
  color: #4caf50;
}

.negative-change {
  color: #f44336;
}

.no-change {
  color: #757575;
}

/* Bank Adviser styles */
.bank-adviser-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.bank-adviser-section {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.bank-adviser-section h3 {
  background-color: #1976d2;
  color: white;
  padding: 10px;
  margin: -20px -20px 20px -20px;
  border-radius: 5px 5px 0 0;
}

.column-mapping-container {
  margin-top: 20px;
  overflow-x: auto;
}

.column-mapping-table {
  width: 100%;
  border-collapse: collapse;
}

.column-mapping-table th, .column-mapping-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
}

.column-mapping-table th {
  background-color: #f2f2f2;
}

.column-mapping-table select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

#start-bank-adviser-btn {
  background-color: #4caf50;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 12px 30px;
  transition: background-color 0.3s;
}

#start-bank-adviser-btn:hover {
  background-color: #45a049;
}

.bank-adviser-results-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

.bank-adviser-results-table {
  width: 100%;
  border-collapse: collapse;
}

.bank-adviser-results-table th, .bank-adviser-results-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
}

.bank-adviser-results-table th {
  background-color: #f2f2f2;
  position: sticky;
  top: 0;
  z-index: 1;
}

.bank-adviser-results-table tr.pre-audited {
  background-color: #e8f5e9;
}

.bank-adviser-results-table tr.not-pre-audited {
  background-color: #ffebee;
}

.bank-adviser-results-table tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.bank-group-header {
  background-color: #e3f2fd;
  font-weight: bold;
}

.bank-group-total {
  background-color: #e8eaf6;
  font-weight: bold;
}

.grand-total {
  background-color: #c5cae9;
  font-weight: bold;
}

.employee-details-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.employee-details-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 80%;
  max-width: 800px;
}

.employee-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.employee-details-header h3 {
  margin: 0;
}

.close-details-modal {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-details-modal:hover {
  color: #333;
}

.employee-details-section {
  margin-bottom: 20px;
}

.employee-details-section h4 {
  margin-bottom: 10px;
  color: #1976d2;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
}

.details-table th, .details-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.details-table th {
  background-color: #f2f2f2;
  width: 30%;
}

.verification-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.verification-status.verified {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.verification-status.not-verified {
  background-color: #ffebee;
  color: #c62828;
}

.processing-details-container {
  margin-top: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.processing-details-box {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 200px;
  overflow-y: auto;
  padding: 10px;
}

.processing-detail-entry {
  margin-bottom: 5px;
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.processing-detail-entry:last-child {
  border-bottom: none;
}

.success-container {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.error-container {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.error-details {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.error-help {
  margin-top: 10px;
  font-style: italic;
  color: #666;
}

/* Footer styles */
.app-footer {
  background-color: #1a237e;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
}

.footer-right {
  text-align: right;
}

.footer-right p, .footer-left p {
  font-size: 14px;
  margin: 2px 0;
}

/* Sort selector styles */
.sort-selector {
  margin-top: 10px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sort-selector select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
  font-family: inherit;
}

/* Fields container styles */
.fields-container {
  margin-top: 20px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

/* Fields Tabs Styles */
.fields-tabs {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #ddd;
  margin-bottom: 15px;
}

.fields-tab-button {
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-bottom: none;
  padding: 8px 16px;
  cursor: pointer;
  margin-right: 2px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  transition: background-color 0.3s;
}

.fields-tab-button:hover {
  background-color: #ddd;
}

.fields-tab-button.active {
  background-color: #fff;
  border-bottom: 1px solid #fff;
  margin-bottom: -1px;
  font-weight: bold;
}

.fields-tab-content {
  display: none;
  padding: 15px;
  border: 1px solid #ddd;
  border-top: none;
  margin-top: -1px;
}

.fields-tab-content.active {
  display: block;
}

.fields-table-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  margin-top: 10px;
}

.fields-table {
  width: 100%;
  border-collapse: collapse;
}

.fields-table th,
.fields-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.fields-table th {
  background-color: #f2f2f2;
  position: sticky;
  top: 0;
  z-index: 10;
}

.fields-table tr:hover {
  background-color: #f5f5f5;
}

.select-all-button,
.deselect-all-button {
  padding: 5px 10px;
  margin-left: 5px;
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.select-all-button:hover,
.deselect-all-button:hover {
  background-color: #ddd;
}

.field-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch .toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
  background-color: #2196F3;
}

.toggle-input:focus + .toggle-slider {
  box-shadow: 0 0 1px #2196F3;
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Enhanced Dictionary Manager Styles */
.dictionary-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.dictionary-items-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.dictionary-items-table {
  width: 100%;
  border-collapse: collapse;
}

.dictionary-items-table th,
.dictionary-items-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.dictionary-items-table th {
  background-color: #f5f5f5;
  font-weight: bold;
}

.dictionary-items-table tr:hover {
  background-color: #f9f9f9;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.add-item-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
}

.edit-button {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 3px 8px;
  cursor: pointer;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 3px 8px;
  cursor: pointer;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 5px;
  width: 60%;
  max-width: 600px;
}

.close-modal {
  float: right;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
}

.format-helper {
  margin-top: 5px;
}

.format-helper-content {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-top: 5px;
  font-size: 12px;
}

.helper-text {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
  font-style: italic;
}

.small-button {
  font-size: 12px;
  padding: 2px 5px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
}

.warning {
  background-color: #ff9800;
  color: white;
}

.add-section-form {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  max-width: 500px;
}

.primary-button {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
}

.secondary-button {
  background-color: #e0e0e0;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
}

.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 20px;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  z-index: 1000;
  animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

.notification.success {
  background-color: #4caf50;
}

.notification.error {
  background-color: #f44336;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Dictionary Manager Styles */
.dictionary-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.dictionary-tab-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  color: #333;
  cursor: pointer;
  font-size: 16px;
  margin-right: 5px;
  padding: 10px 20px;
}

.dictionary-tab-button:hover {
  background-color: #e0e0e0;
}

.dictionary-tab-button.active {
  background-color: white;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
}

.dictionary-tab-content {
  display: none;
}

.dictionary-tab-content.active {
  display: block;
}

.dictionary-container {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.dictionary-add-form {
  flex: 1;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
}

.dictionary-list-container {
  flex: 2;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.action-button {
  background-color: #1976d2;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  padding: 8px 15px;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #1565c0;
}

.primary-button {
  background-color: #4caf50;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 10px 20px;
  transition: background-color 0.3s;
  margin-right: 10px;
}

.primary-button:hover {
  background-color: #45a049;
}

.secondary-button {
  background-color: #f44336;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 10px 20px;
  transition: background-color 0.3s;
}

.secondary-button:hover {
  background-color: #d32f2f;
}

.dictionary-item {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 10px;
}

.dictionary-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.dictionary-item-name {
  font-weight: bold;
  font-size: 16px;
}

.dictionary-item-actions {
  display: flex;
  gap: 5px;
}

.dictionary-item-action {
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 2px 5px;
}

.dictionary-item-action.edit {
  color: #1976d2;
}

.dictionary-item-action.delete {
  color: #f44336;
}

.dictionary-item-variations {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.field-checkbox input[type="checkbox"] {
  margin-right: 8px;
}

/* Progress Stage Styles */
.progress-stages {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  width: 100%;
}

.progress-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 25%;
}

.progress-stage:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 25px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: #ccc;
  z-index: 1;
}

.stage-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: 2px solid #ccc;
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.stage-name {
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

/* Active stage */
.progress-stage.active .stage-icon {
  background-color: #1976d2;
  border-color: #1976d2;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(25, 118, 210, 0.5);
}

.progress-stage.active .stage-name {
  color: #1976d2;
  font-weight: bold;
}

/* Completed stage */
.progress-stage.completed .stage-icon {
  background-color: #4caf50;
  border-color: #4caf50;
  color: white;
}

.progress-stage.completed .stage-name {
  color: #4caf50;
}

.progress-stage.completed:not(:last-child)::after {
  background-color: #4caf50;
}

/* Reports styles */
.reports-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.report-tab-button {
  background-color: #f1f1f1;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.report-tab-button:hover {
  background-color: #ddd;
}

.report-tab-button.active {
  background-color: #4caf50;
  color: white;
}

.report-tab-content {
  display: none;
}

.report-tab-content.active {
  display: block;
}

.reports-list {
  margin-top: 15px;
}

.report-item, .report-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.report-info h5 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.timestamp, .report-date {
  color: #666;
  font-size: 12px;
  margin: 0 0 5px 0;
}

.file-path, .report-name {
  color: #333;
  font-size: 14px;
  margin: 5px 0;
  word-break: break-all;
}

.report-actions {
  display: flex;
  gap: 10px;
}

.report-action-btn, .report-open-btn, .report-download-btn, .report-delete-btn {
  padding: 5px 10px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.open-btn, .report-open-btn {
  background-color: #4caf50;
  color: white;
}

.report-download-btn {
  background-color: #1976d2;
  color: white;
}

.delete-btn, .report-delete-btn {
  background-color: #f44336;
  color: white;
}

.report-type {
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  margin-bottom: 5px;
  display: inline-block;
}

.excel-type {
  background-color: #217346; /* Excel green */
}

.word-type {
  background-color: #2b579a; /* Word blue */
}

.pdf-type {
  background-color: #b7472a; /* PDF red */
}

#reports-container {
  max-height: 500px;
  overflow-y: auto;
  margin-top: 20px;
  padding-right: 10px;
}

.export-options {
  margin-top: 30px;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.export-buttons {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.export-btn {
  padding: 8px 15px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.export-btn:hover {
  background-color: #1565c0;
}

/* Success and error containers */
.success-container {
  padding: 15px;
  background-color: #e8f5e9;
  border: 1px solid #4caf50;
  border-radius: 5px;
  margin-top: 20px;
}

.error-container {
  padding: 15px;
  background-color: #ffebee;
  border: 1px solid #f44336;
  border-radius: 5px;
  margin-top: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.action-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn:first-child {
  background-color: #1976d2;
  color: white;
}

.action-btn:last-child {
  background-color: #4caf50;
  color: white;
}

/* Progress container */
.progress-container {
  padding: 20px;
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 5px;
  margin-top: 20px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  max-height: 80vh;
  overflow-y: auto;
}

/* Hide detailed process messages in the blue background */
.progress-container > p,
.progress-container > div > p,
.progress-container .progress-visualization > p {
  display: none !important;
}

/* Ensure processing details are always visible */
.progress-container .processing-details-container,
.progress-container .processing-details-box,
.progress-container .processing-detail-entry {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure the progress visualization is always visible */
.progress-container .progress-visualization,
.progress-container .progress-stages,
.progress-container .progress-stage,
.progress-container .stage-icon,
.progress-container .stage-name,
.progress-container .progress-bar-container,
.progress-container .progress-bar,
.progress-container .extraction-progress,
.progress-container .progress-row,
.progress-container .progress-bar-wrapper,
.progress-container .progress-bar-small {
  display: flex !important;
}

/* Progress visualization */
.progress-visualization {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  max-width: 100%;
  position: relative;
  z-index: 5;
  visibility: visible !important;
  opacity: 1 !important;
}

.action-btn {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: #1565c0;
}

/* Stop button */
.stop-button-container {
  margin-top: 20px;
}

.stop-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.stop-btn:hover {
  background-color: #d32f2f;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.progress-stages {
  display: flex !important;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 30px auto;
  position: relative;
  flex-wrap: wrap;
  visibility: visible !important;
  z-index: 6;
  opacity: 1 !important;
}

.progress-stage {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  width: 120px;
  z-index: 7;
  visibility: visible !important;
  opacity: 1 !important;
}

.stage-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex !important;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  font-size: 24px;
  color: #757575;
  border: 2px solid #ddd;
  transition: all 0.3s ease;
  visibility: visible !important;
  z-index: 8;
  opacity: 1 !important;
}

.stage-icon.active {
  background-color: #bbdefb;
  color: #1976d2;
  border-color: #1976d2;
}

.stage-icon.completed {
  background-color: #c8e6c9;
  color: #388e3c;
  border-color: #388e3c;
}

.stage-name {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #757575;
}

.stage-name.active {
  color: #1976d2;
}

.stage-name.completed {
  color: #388e3c;
}

.progress-bar-container {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  width: 0;
  transition: width 0.3s ease-out;
  will-change: width;
  transform: translateZ(0);
  display: block !important;
  visibility: visible !important;
}

.progress-details {
  margin-top: 20px;
  width: 100%;
  max-width: 800px;
}

.progress-file {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.progress-file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-file-name {
  font-weight: bold;
  font-size: 16px;
}

.progress-file-percentage {
  font-weight: bold;
  color: #1976d2;
}

.progress-file-bar-container {
  height: 8px;
  background-color: #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.progress-file-bar {
  height: 100%;
  background-color: #4caf50;
  width: 0;
  transition: width 0.2s linear;
}

/* Removed progress-status as requested
.progress-status {
  margin-top: 10px;
  font-size: 14px;
  color: #1976d2;
  font-weight: bold;
  display: block !important;
  visibility: visible !important;
  z-index: 9;
}
*/

.progress-log-container {
  width: 100%;
  margin-top: 20px;
  border: 1px solid #2196f3;
  border-radius: 4px;
  background-color: #ffffff;
  max-height: 200px; /* Fixed height container */
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.progress-log {
  height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #ffffff;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
  word-wrap: break-word;
  white-space: pre-wrap;
  width: 100%;
  box-sizing: border-box;
  color: #0d47a1;
}

/* Processing details container styles */
/* Processing details container styles - ONLY place where processing messages should appear */
.processing-details-container {
  width: 100%;
  margin-top: 20px;
  border: 1px solid #2196f3; /* Blue border to match the app theme */
  border-radius: 4px;
  background-color: #ffffff;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  box-sizing: border-box;
  position: relative; /* Ensure proper stacking context */
  z-index: 100; /* Reasonable z-index */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.processing-details-container h5 {
  margin: 0;
  padding: 10px;
  background-color: #2196f3; /* Blue background to match the app theme */
  border-bottom: 1px solid #1976d2;
  color: white;
  font-size: 14px;
  text-align: center;
  font-weight: bold;
  display: block;
  visibility: visible;
  opacity: 1;
}

.processing-details-box {
  height: 200px; /* Standard height */
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  background-color: #ffffff;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  text-align: left;
  word-wrap: break-word;
  width: 100%;
  box-sizing: border-box;
  color: #0d47a1; /* Dark blue text */
  border: none;
  position: relative;
  z-index: 101;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.processing-detail-entry {
  margin: 4px 0;
  padding: 6px 8px;
  border-bottom: 1px solid #e0e0e0;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #0d47a1;
  background-color: #f5f5f5;
  border-radius: 3px;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Extraction progress styles */
.extraction-progress {
  width: 100%;
  margin-top: 15px;
  padding: 10px;
  background-color: #e3f2fd !important; /* Light blue background to match the screenshot */
  border: 1px solid #bbdefb !important;
  border-radius: 4px;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 5;
  position: relative;
}

.progress-row {
  display: flex !important;
  align-items: center;
  margin-bottom: 10px;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 6;
  position: relative;
}

.progress-row:last-child {
  margin-bottom: 0;
}

.progress-row span {
  flex: 0 0 120px !important; /* Reduced width to match screenshot */
  font-size: 14px;
  font-weight: normal !important; /* Normal font weight for labels */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.progress-row span:last-child {
  flex: 0 0 100px !important; /* Reduced width to match screenshot */
  text-align: right !important;
  font-weight: normal !important; /* Normal font weight for percentage */
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.progress-bar-wrapper {
  flex: 1 !important;
  height: 12px !important;
  background-color: #e0e0e0 !important; /* Light gray background for progress bar */
  border-radius: 6px !important;
  overflow: hidden !important;
  margin: 0 10px !important;
  display: block !important;
  visibility: visible !important;
  z-index: 7;
  opacity: 1 !important;
}

.progress-bar-small {
  height: 100% !important;
  background-color: #4caf50 !important; /* Green color as requested */
  width: 0%;
  transition: width 0.3s ease-out;
  will-change: width;
  transform: translateZ(0);
  display: block !important;
  visibility: visible !important;
  z-index: 8;
  opacity: 1 !important;
  position: relative;
}

/* Ensure progress bars stay at 100% when completed */
.progress-bar-small[style*="width: 100%"] {
  width: 100% !important;
  background-color: #4caf50 !important;
}

/* New progress bar styles */
.progress-container {
  width: 100%;
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  padding: 10px;
  margin-top: 15px;
}

.progress-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  width: 120px;
  text-align: left;
  font-size: 14px;
}

.progress-bar-container {
  flex-grow: 1;
  margin: 0 10px;
  background-color: #e0e0e0;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  background-color: #4caf50;
  height: 100%;
  width: 0%;
  position: absolute;
  left: 0;
  top: 0;
  transition: width 0.3s ease-out;
}

/* Ensure progress bars stay at 100% when completed */
.progress-bar[style*="width: 100%"] {
  width: 100% !important;
  background-color: #4caf50 !important;
}

.progress-text {
  width: 100px;
  text-align: right;
  font-size: 14px;
}

.progress-log p {
  margin: 2px 0;
  padding: 0;
}

.spinner {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Document manager styles */
.document-manager-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.search-container {
  display: flex;
  flex: 1;
  margin-right: 15px;
}

.search-container input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
}

.search-container button {
  padding: 8px 15px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.filter-container {
  width: 200px;
}

.filter-container select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.document-list {
  margin-top: 15px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.document-info {
  flex: 1;
}

.document-info h5 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.document-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 10px;
}

.document-type.pdf {
  background-color: #ffebee;
  color: #d32f2f;
}

.document-type.excel {
  background-color: #e8f5e9;
  color: #388e3c;
}

.document-actions {
  display: flex;
  gap: 10px;
}

/* Bank Adviser Styles */
.column-mapping-container {
  margin-top: 20px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
}

.column-mapping-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.column-mapping-table th,
.column-mapping-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.column-mapping-table th {
  background-color: #f2f2f2;
}

.column-mapping-table tr:hover {
  background-color: #f5f5f5;
}

.column-mapping-table select {
  width: 100%;
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.bank-adviser-results-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.bank-adviser-results-table th,
.bank-adviser-results-table td {
  padding: 8px;
  text-align: left;
  border: 1px solid #ddd;
}

.bank-adviser-results-table th {
  background-color: #f2f2f2;
  position: sticky;
  top: 0;
  z-index: 10;
}

.bank-adviser-results-table tr:hover {
  background-color: #f5f5f5;
}

.bank-adviser-results-table .pre-audited {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.bank-adviser-results-table .not-pre-audited {
  background-color: #ffebee;
  color: #c62828;
}

.bank-adviser-results-table .bank-group-header {
  background-color: #e3f2fd;
  font-weight: bold;
}

.bank-adviser-results-table .bank-group-total {
  background-color: #e8eaf6;
  font-weight: bold;
}

.bank-adviser-results-table .grand-total {
  background-color: #3f51b5;
  color: white;
  font-weight: bold;
}

.employee-details-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.employee-details-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 5px;
  width: 80%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.employee-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.employee-details-header h3 {
  margin: 0;
}

.close-details-modal {
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.employee-details-section {
  margin-bottom: 20px;
}

.employee-details-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
}

.details-table th,
.details-table td {
  padding: 6px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.details-table th {
  background-color: #f9f9f9;
  font-weight: bold;
}

.verification-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.verification-status.verified {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.verification-status.not-verified {
  background-color: #ffebee;
  color: #c62828;
}
