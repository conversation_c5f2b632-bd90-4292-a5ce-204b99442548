/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

body {
  background-color: #f0f0f0;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header styles */
.app-header {
  background-color: #1a237e;
  color: white;
  padding: 20px;
  text-align: center;
}

.title-container h1 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.title-container h2 {
  font-size: 20px;
  font-weight: normal;
}

/* Navigation styles */
.app-nav {
  background-color: #283593;
  display: flex;
  padding: 0 10px;
}

.nav-button {
  background-color: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 15px 20px;
  transition: background-color 0.3s;
}

.nav-button:hover {
  background-color: #3949ab;
}

.nav-button.active {
  background-color: #1976d2;
}

/* Main content styles */
.app-content {
  flex: 1;
  padding: 20px;
}

.content-container {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.hidden {
  display: none;
}

/* Payroll section styles */
.payroll-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.payroll-section {
  flex: 1;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.payroll-section h3 {
  background-color: #1976d2;
  color: white;
  padding: 10px;
  margin: -20px -20px 20px -20px;
  border-radius: 5px 5px 0 0;
}

.file-selector p {
  margin-bottom: 10px;
}

.file-input-container {
  display: flex;
  margin-bottom: 10px;
}

.file-input-container input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
}

.file-input-container button {
  background-color: #e0e0e0;
  border: 1px solid #ccc;
  border-left: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  padding: 8px 15px;
}

.file-input-container button:hover {
  background-color: #d0d0d0;
}

.error-message {
  color: #f44336;
  font-size: 14px;
  min-height: 20px;
}

/* ID field selector styles */
.id-field-container {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 20px auto;
  max-width: 600px;
  padding: 15px;
  text-align: left;
}

.id-field-container label {
  display: block;
  font-weight: bold;
  margin-bottom: 8px;
}

.id-field-container select {
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
  padding: 8px;
  width: 100%;
}

.field-description {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

/* Month selector styles */
.month-selector {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.month-selector label {
  margin-right: 8px;
  font-weight: bold;
  color: #333;
}

.month-selector select {
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 15px;
  background-color: white;
  font-size: 14px;
}

/* Action button styles */
.action-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

#start-audit-btn {
  background-color: #4caf50;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  padding: 12px 30px;
  transition: background-color 0.3s;
}

#start-audit-btn:hover {
  background-color: #45a049;
}

.stop-button {
  background-color: #f44336;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  margin-top: 10px;
  transition: background-color 0.3s;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.stop-button:hover {
  background-color: #d32f2f;
}

/* Comparison results styles */
#comparison-results {
  margin-top: 20px;
}

.results-container {
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.results-container h4 {
  border-bottom: 1px solid #eee;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
}

.comparison-summary {
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 20px;
  padding: 15px;
}

.comparison-summary p {
  margin: 8px 0;
}

.changes-list {
  list-style-type: none;
  padding-left: 0;
}

.changes-list > li {
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.changes-list > li:last-child {
  border-bottom: none;
}

.changes-list ul {
  list-style-type: disc;
  padding-left: 20px;
}

.report-links {
  list-style-type: none;
  padding-left: 0;
}

.report-links li {
  margin-bottom: 10px;
}

.report-buttons-container {
  display: flex;
  gap: 15px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.report-button {
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 25px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.report-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.report-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
}

.report-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.excel-button {
  background-color: #217346; /* Excel green */
}

.excel-button:hover:not(.disabled) {
  background-color: #1e6b3e;
}

.word-button {
  background-color: #2b579a; /* Word blue */
}

.word-button:hover:not(.disabled) {
  background-color: #254a87;
}

.pdf-button {
  background-color: #b7472a; /* PDF red */
}

.pdf-button:hover:not(.disabled) {
  background-color: #a33f25;
}

.note {
  color: #666;
  font-style: italic;
  margin-top: 15px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.comparison-table th, .comparison-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.comparison-table th {
  background-color: #f2f2f2;
}

.comparison-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.positive-change {
  color: #4caf50;
}

.negative-change {
  color: #f44336;
}

.no-change {
  color: #757575;
}

/* Footer styles */
.app-footer {
  background-color: #1a237e;
  color: white;
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
}

.footer-right {
  text-align: right;
}

.footer-right p, .footer-left p {
  font-size: 14px;
  margin: 2px 0;
}

/* Sort selector styles */
.sort-selector {
  margin-top: 10px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sort-selector select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 8px;
  font-family: inherit;
}

/* Fields container styles */
.fields-container {
  margin-top: 20px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 15px;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.field-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.field-checkbox input[type="checkbox"] {
  margin-right: 8px;
}

/* Reports styles */
.reports-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.report-tab-button {
  background-color: #f1f1f1;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.report-tab-button:hover {
  background-color: #ddd;
}

.report-tab-button.active {
  background-color: #4caf50;
  color: white;
}

.report-tab-content {
  display: none;
}

.report-tab-content.active {
  display: block;
}

.reports-list {
  margin-top: 15px;
}

.report-item, .report-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.report-info h5 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.timestamp, .report-date {
  color: #666;
  font-size: 12px;
  margin: 0 0 5px 0;
}

.file-path, .report-name {
  color: #333;
  font-size: 14px;
  margin: 5px 0;
  word-break: break-all;
}

.report-actions {
  display: flex;
  gap: 10px;
}

.report-action-btn, .report-open-btn {
  padding: 5px 10px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.open-btn, .report-open-btn {
  background-color: #4caf50;
  color: white;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.report-type {
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  margin-bottom: 5px;
  display: inline-block;
}

.excel-type {
  background-color: #217346; /* Excel green */
}

.word-type {
  background-color: #2b579a; /* Word blue */
}

.pdf-type {
  background-color: #b7472a; /* PDF red */
}

#reports-container {
  max-height: 500px;
  overflow-y: auto;
  margin-top: 20px;
  padding-right: 10px;
}

.export-options {
  margin-top: 30px;
  padding: 15px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.export-buttons {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.export-btn {
  padding: 8px 15px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.export-btn:hover {
  background-color: #1565c0;
}

/* Success and error containers */
.success-container {
  padding: 15px;
  background-color: #e8f5e9;
  border: 1px solid #4caf50;
  border-radius: 5px;
  margin-top: 20px;
}

.error-container {
  padding: 15px;
  background-color: #ffebee;
  border: 1px solid #f44336;
  border-radius: 5px;
  margin-top: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.action-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.action-btn:first-child {
  background-color: #1976d2;
  color: white;
}

.action-btn:last-child {
  background-color: #4caf50;
  color: white;
}

/* Progress container */
.progress-container {
  padding: 20px;
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 5px;
  margin-top: 20px;
  text-align: center;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  max-height: 80vh;
  overflow-y: auto;
}

/* Hide detailed process messages in the blue background */
.progress-container > p,
.progress-container > div > p,
.progress-container .progress-visualization > p {
  display: none !important;
}

/* Progress visualization */
.progress-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  max-width: 100%;
}

.action-btn {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: #1565c0;
}

/* Stop button */
.stop-button-container {
  margin-top: 20px;
}

.stop-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.stop-btn:hover {
  background-color: #d32f2f;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.progress-stages {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 800px;
  margin-bottom: 30px;
  position: relative;
  flex-wrap: wrap;
}

.progress-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
  z-index: 2;
}

.stage-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  font-size: 24px;
  color: #757575;
  border: 2px solid #ddd;
  transition: all 0.3s ease;
}

.stage-icon.active {
  background-color: #bbdefb;
  color: #1976d2;
  border-color: #1976d2;
}

.stage-icon.completed {
  background-color: #c8e6c9;
  color: #388e3c;
  border-color: #388e3c;
}

.stage-name {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #757575;
}

.stage-name.active {
  color: #1976d2;
}

.stage-name.completed {
  color: #388e3c;
}

.progress-bar-container {
  position: absolute;
  top: 30px;
  left: 60px;
  right: 60px;
  height: 4px;
  background-color: #ddd;
  z-index: 1;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  width: 0;
  transition: width 0.2s linear;
}

.progress-details {
  margin-top: 20px;
  width: 100%;
  max-width: 800px;
}

.progress-file {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.progress-file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-file-name {
  font-weight: bold;
  font-size: 16px;
}

.progress-file-percentage {
  font-weight: bold;
  color: #1976d2;
}

.progress-file-bar-container {
  height: 8px;
  background-color: #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.progress-file-bar {
  height: 100%;
  background-color: #4caf50;
  width: 0;
  transition: width 0.2s linear;
}

.progress-status {
  margin-top: 10px;
  font-size: 14px;
  color: #1976d2;
  font-weight: bold;
}

.progress-log-container {
  width: 100%;
  margin-top: 20px;
  border: 1px solid #2196f3;
  border-radius: 4px;
  background-color: #f5f5f5;
  max-height: 200px; /* Fixed height container */
  overflow: hidden;
}

.progress-log {
  height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f5f5;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  text-align: left;
  word-wrap: break-word;
  white-space: pre-wrap;
  width: 100%;
  box-sizing: border-box;
}

/* Extraction progress styles */
.extraction-progress {
  width: 100%;
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.progress-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-row:last-child {
  margin-bottom: 0;
}

.progress-row span {
  flex: 0 0 150px;
  font-size: 14px;
}

.progress-row span:last-child {
  flex: 0 0 120px;
  text-align: right;
}

.progress-bar-wrapper {
  flex: 1;
  height: 12px;
  background-color: #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  margin: 0 10px;
}

.progress-bar-small {
  height: 100%;
  background-color: #4caf50;
  width: 0%;
  transition: width 0.2s linear;
}

.progress-log p {
  margin: 2px 0;
  padding: 0;
}

.spinner {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Document manager styles */
.document-manager-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.search-container {
  display: flex;
  flex: 1;
  margin-right: 15px;
}

.search-container input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
}

.search-container button {
  padding: 8px 15px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.filter-container {
  width: 200px;
}

.filter-container select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.document-list {
  margin-top: 15px;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 10px;
  background-color: white;
}

.document-info {
  flex: 1;
}

.document-info h5 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.document-type {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 10px;
}

.document-type.pdf {
  background-color: #ffebee;
  color: #d32f2f;
}

.document-type.excel {
  background-color: #e8f5e9;
  color: #388e3c;
}

.document-actions {
  display: flex;
  gap: 10px;
}
