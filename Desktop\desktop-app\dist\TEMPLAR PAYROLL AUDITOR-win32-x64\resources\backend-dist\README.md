# Templar Payroll Auditor - Backend

This directory contains the Python backend for the Templar Payroll Auditor application.

## Files

- `main.py`: The main script that processes payroll PDFs and generates comparison reports
- `sample_payroll.py`: A utility script to generate sample payroll PDFs for testing

## Usage

The main script is designed to be called from the Electron frontend, but can also be run directly:

```bash
python main.py previous_payroll.pdf current_payroll.pdf output_directory
```

## Dependencies

The backend requires the following Python packages:

- PyPDF2
- pdfplumber
- openpyxl
- pandas
- reportlab (for sample generation only)

You can install these dependencies with:

```bash
pip install PyPDF2 pdfplumber openpyxl pandas reportlab
```

## Generating Sample Data

To generate sample payroll PDFs for testing:

```bash
python sample_payroll.py
```

This will create two PDF files in the current directory:
- `previous_payroll_sample.pdf`
- `current_payroll_sample.pdf`

These files can be used to test the application without needing real payroll data.
