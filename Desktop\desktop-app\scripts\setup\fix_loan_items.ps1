$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false

# Open the existing workbook
$workbook = $excel.Workbooks.Open("C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx")

# Get the Dictionary worksheet
$dictSheet = $workbook.Worksheets.Item("Payslip Dictionary")

# Find the last row
$lastRow = $dictSheet.UsedRange.Rows.Count

# First, find and remove existing loan-related rows
$rowsToDelete = New-Object System.Collections.ArrayList
for ($row = $lastRow; $row -ge 2; $row--) {
    $section = $dictSheet.Cells.Item($row, 1).Text
    $item = $dictSheet.Cells.Item($row, 2).Text
    
    if ($section -eq "LOANS" -and ($item -eq "LOAN" -or $item -eq "BALANCE B/F" -or 
        $item -eq "CURRENT DEDUCTION" -or $item -eq "OUST. BALANCE" -or 
        $item -eq "SALARY ADVANCE-MINS")) {
        $rowsToDelete.Add($row) | Out-Null
    }
}

foreach ($row in $rowsToDelete) {
    $range = $dictSheet.Rows.Item($row)
    $range.Delete()
}

# Find the new last row after deletions
$lastRow = $dictSheet.UsedRange.Rows.Count

# Find where to insert new loan rows
$insertRow = 0
for ($row = 2; $row -le $lastRow; $row++) {
    $section = $dictSheet.Cells.Item($row, 1).Text
    $item = $dictSheet.Cells.Item($row, 2).Text
    
    if ($section -eq "LOANS" -and $item -eq "LOANS") {
        $insertRow = $row + 1
        break
    }
}

if ($insertRow -eq 0) {
    Write-Host "Could not find LOANS section header"
    exit
}

# Insert new rows for loan items
$range = $dictSheet.Rows.Item($insertRow)

# Add column headers
# LOAN header
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'LOAN'
$dictSheet.Cells.Item($insertRow, 3) = '[UC]'
$dictSheet.Cells.Item($insertRow, 4) = 'Column Header'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# BALANCE B/F header
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'BALANCE B/F'
$dictSheet.Cells.Item($insertRow, 3) = '[UC][/]'
$dictSheet.Cells.Item($insertRow, 4) = 'Column Header'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# CURRENT DEDUCTION header
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'CURRENT DEDUCTION'
$dictSheet.Cells.Item($insertRow, 3) = '[UC]'
$dictSheet.Cells.Item($insertRow, 4) = 'Column Header'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# OUST. BALANCE header
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'OUST. BALANCE'
$dictSheet.Cells.Item($insertRow, 3) = '[UC][.]'
$dictSheet.Cells.Item($insertRow, 4) = 'Column Header'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# Add loan type and values
# SALARY ADVANCE-MINS (loan type)
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'SALARY ADVANCE-MINS'
$dictSheet.Cells.Item($insertRow, 3) = '[UC][-]'
$dictSheet.Cells.Item($insertRow, 4) = 'Text (loan type)'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# BALANCE B/F value
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'BALANCE B/F Value'
$dictSheet.Cells.Item($insertRow, 3) = '[N][,][.]'
$dictSheet.Cells.Item($insertRow, 4) = 'Numeric with decimal places'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# CURRENT DEDUCTION value
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'CURRENT DEDUCTION Value'
$dictSheet.Cells.Item($insertRow, 3) = '[N][,][.]'
$dictSheet.Cells.Item($insertRow, 4) = 'Numeric with decimal places'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# OUST. BALANCE value
$dictSheet.Cells.Item($insertRow, 1) = 'LOANS'
$dictSheet.Cells.Item($insertRow, 2) = 'OUST. BALANCE Value'
$dictSheet.Cells.Item($insertRow, 3) = '[N][,][.]'
$dictSheet.Cells.Item($insertRow, 4) = 'Numeric with decimal places'
$dictSheet.Cells.Item($insertRow, 5) = 'YES'
$insertRow++

# Save the workbook
$workbook.Save()
$excel.Quit()

# Clean up COM objects
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($dictSheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Loan items updated correctly at C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx"
