"""
Dictionary Integration Module

This module provides integration between the enhanced payslip dictionary
and the extraction engine. It replaces the old dictionary standardization
with the new comprehensive dictionary functionality.
"""

import os
import json
import re
from enhanced_payroll_dictionaries import load_dictionary, find_item_by_variation, get_standardized_name

# HARDCODED EXCLUSION LIST - These items must NEVER appear in extraction or reports
# This is a strict requirement per user specifications
HARDCODED_EXCLUSIONS = [
    "NET PAY",
    "GROSS SALARY",
    "INCOME TAX",
    "BASIC SALARY"
]

def is_excluded_item(item_name):
    """
    Check if an item is in the hardcoded exclusion list.

    Args:
        item_name: The item name to check

    Returns:
        True if the item should be excluded, False otherwise
    """
    if not item_name:
        return False

    item_upper = item_name.upper().strip()

    # Check exact matches first
    for excluded_item in HARDCODED_EXCLUSIONS:
        if item_upper == excluded_item.upper():
            print(f"DICTIONARY EXCLUSION: Hardcoded exclusion applied to '{item_name}' - exact match with '{excluded_item}'")
            return True

    # Check partial matches for variations
    for excluded_item in HARDCODED_EXCLUSIONS:
        excluded_upper = excluded_item.upper()
        if excluded_upper in item_upper or item_upper in excluded_upper:
            print(f"DICTIONARY EXCLUSION: Hardcoded exclusion applied to '{item_name}' - partial match with '{excluded_item}'")
            return True

    return False

# Global dictionary cache
_dictionary_cache = None

def get_dictionary():
    """
    Get the dictionary, loading it from disk if necessary.

    Returns:
        The dictionary object
    """
    global _dictionary_cache
    if _dictionary_cache is None:
        _dictionary_cache = load_dictionary()
    return _dictionary_cache

def reload_dictionary():
    """
    Force a reload of the dictionary from disk.

    Returns:
        The reloaded dictionary object
    """
    global _dictionary_cache
    _dictionary_cache = load_dictionary()
    return _dictionary_cache

def standardize_item(section_name, item_text):
    """
    Standardize an item name using the dictionary.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_text: The item text to standardize

    Returns:
        The standardized item name or the original if no match is found
    """
    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return item_text

    # Get the standardized name
    return get_standardized_name(section_name, item_text)

def standardize_earnings(earnings_dict):
    """
    Standardize earnings dictionary keys using the dictionary and filter out excluded items.

    Args:
        earnings_dict: Dictionary of earnings items

    Returns:
        Dictionary with standardized keys and excluded items removed
    """
    standardized = {}

    for key, value in earnings_dict.items():
        std_key = standardize_item("EARNINGS", key)

        # Check if this item should be excluded
        if is_excluded_item(std_key):
            print(f"DICTIONARY EXCLUSION: Filtered out excluded earnings item '{std_key}' (original: '{key}')")
            continue

        standardized[std_key] = value

    return standardized

def standardize_deductions(deductions_dict):
    """
    Standardize deductions dictionary keys using the dictionary and filter out excluded items.

    Args:
        deductions_dict: Dictionary of deduction items

    Returns:
        Dictionary with standardized keys and excluded items removed
    """
    standardized = {}

    for key, value in deductions_dict.items():
        std_key = standardize_item("DEDUCTIONS", key)

        # Check if this item should be excluded
        if is_excluded_item(std_key):
            print(f"DICTIONARY EXCLUSION: Filtered out excluded deductions item '{std_key}' (original: '{key}')")
            continue

        standardized[std_key] = value

    return standardized

def standardize_loans(loans_dict):
    """
    Standardize loans dictionary keys using the dictionary.

    Args:
        loans_dict: Dictionary of loan items

    Returns:
        Dictionary with standardized keys
    """
    standardized = {}

    for key, value in loans_dict.items():
        std_key = standardize_item("LOANS", key)
        standardized[std_key] = value

    return standardized

def should_include_in_report(section_name, item_name):
    """
    Check if an item should be included in reports.
    First checks hardcoded exclusions, then dictionary settings.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_name: The item name to check

    Returns:
        True if the item should be included, False otherwise
    """
    # First check hardcoded exclusions - these override everything
    if is_excluded_item(item_name):
        return False

    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return True  # Default to including if section not found

    # Find the item
    item_name, item_data = find_item_by_variation(section_name, item_name)

    # If item not found, default to including
    if not item_data:
        return True

    # Return the include_in_report flag
    return item_data.get("include_in_report", True)

def get_value_format(section_name, item_name):
    """
    Get the value format for an item.

    Args:
        section_name: The section to look in (e.g., "EARNINGS", "DEDUCTIONS")
        item_name: The item name to check

    Returns:
        The value format string or None if not found
    """
    dictionary = get_dictionary()

    # Check if the section exists in the dictionary
    if section_name not in dictionary:
        return None

    # Find the item
    item_name, item_data = find_item_by_variation(section_name, item_name)

    # If item not found, return None
    if not item_data:
        return None

    # Return the value format
    return item_data.get("value_format")
