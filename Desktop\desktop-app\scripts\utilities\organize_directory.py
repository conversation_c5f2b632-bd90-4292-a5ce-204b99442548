#!/usr/bin/env python3
"""
Directory Organization Script for Templar Payroll Auditor
Cleans and organizes all files into proper directory structure
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def create_directory_structure():
    """Create the proper directory structure"""
    
    directories = [
        # Core application directories
        "src/frontend",
        "src/backend", 
        "src/assets",
        "src/templates",
        
        # Configuration and build
        "config",
        "build",
        "dist",
        
        # Data and processing
        "data/input",
        "data/output", 
        "data/samples",
        "data/templates",
        "data/dictionaries",
        
        # Reports and outputs
        "reports/audit_reports",
        "reports/filtered_reports", 
        "reports/comparison_reports",
        "reports/archive",
        
        # Scripts and utilities
        "scripts/processing",
        "scripts/utilities",
        "scripts/testing",
        "scripts/setup",
        
        # Documentation
        "docs/user_guides",
        "docs/technical",
        "docs/project_tracking",
        
        # Testing
        "tests/unit",
        "tests/integration",
        "tests/data",
        
        # Temporary and cache
        "temp",
        "cache",
        
        # Vendor and dependencies
        "vendor",
        "node_modules"  # Keep existing
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")

def organize_files():
    """Organize files into appropriate directories"""
    
    # File organization mapping
    file_moves = {
        # Frontend files
        "src/frontend": [
            "index.html", "styles.css", "renderer.js", "preload.js",
            "splash.html", "splash.js", "progress-bar.html", "progress-updater.js",
            "enhanced_dictionary_manager.html", "enhanced_dictionary_manager.js",
            "dictionary_manager.js", "data_builder.js", "bank_adviser.js",
            "bank_adviser_content.html", "minimal-test.html", "minimal-test.js",
            "main.js", "robust-main.js", "simple-main.js"
        ],
        
        # Backend files
        "src/backend": [
            "backend/file_converter.py"
        ],
        
        # Assets
        "src/assets": [
            "assets/download.png", "assets/electron-icon.svg", "assets/icon.png"
        ],
        
        # Templates
        "src/templates": [
            "templates"  # Move entire directory
        ],
        
        # Configuration files
        "config": [
            "package.json", "package-lock.json"
        ],
        
        # Build files
        "build": [
            "build/icon.ico", "build/icon.png", "build_production.bat"
        ],
        
        # Data input files
        "data/input": [
            "Checklist1.xlsx"
        ],
        
        # Data templates
        "data/templates": [
            "Payslip_Dictionary_Template.xlsx", "test.xlsx"
        ],
        
        # Data dictionaries
        "data/dictionaries": [
            "backend-dist/dictionaries"  # Move entire directory
        ],
        
        # Sample data
        "data/samples": [
            "backend-dist/current_payroll_sample.pdf",
            "backend-dist/previous_payroll_sample.pdf"
        ],
        
        # Processing scripts
        "scripts/processing": [
            "backend-dist/improved_payroll_parser.py",
            "backend-dist/improved_payroll_parser_fixed.py", 
            "backend-dist/improved_payroll_parser_backup.py",
            "backend-dist/improved_payroll_parser.py.new",
            "backend-dist/improved_report_generator.py",
            "backend-dist/main.py",
            "backend-dist/payroll_report_generator.py",
            "backend-dist/sample_payroll.py",
            "backend-dist/file_converter.py",
            "backend-dist/dictionary_integration.py",
            "backend-dist/enhanced_payroll_dictionaries.py",
            "backend-dist/payroll_dictionaries.py",
            "filter_july_report.py"
        ],
        
        # Utility scripts
        "scripts/utilities": [
            "add_month_year_params.py", "analyze_allowances_pdf.py",
            "bank_adviser_fix.py", "clean_dictionaries.py",
            "comprehensive_awards_extraction.py", "direct_payslip_extraction.py",
            "excel_converter.py", "extract_allowances.py", "extract_to_excel.py",
            "improved_allowances_extraction.py", "improved_awards_extraction.py",
            "improved_payroll_parser_original.py", "improved_payslip_extraction.py",
            "update_main_function.py", "backend-dist/read_excel_columns.py",
            "backend-dist/test_exclusions.py", "backend-dist/bank_adviser.py"
        ],
        
        # Testing scripts
        "scripts/testing": [
            "test_allowance_extraction.py", "test_awards_extraction.py",
            "test_earnings_deductions.py", "test_extraction.py",
            "test_payslip_extraction.py", "test-backend.js", "test-dictionaries.js"
        ],
        
        # Setup scripts
        "scripts/setup": [
            "install.bat", "install_dependencies.bat", "prepare_python_env.bat",
            "move_tesseract.bat", "run-with-logging.bat", "run-with-samples.bat",
            "create_enhanced_template.ps1", "create_final_template.ps1",
            "create_payslip_template.ps1", "fix_loan_items.ps1", "update_template.ps1"
        ],
        
        # Build and installer scripts
        "scripts/setup": [
            "check-app.js", "check_dependencies.js", "create-installer.js",
            "installer.js"
        ],
        
        # Documentation
        "docs/user_guides": [
            "README.md", "README-UPDATED.md"
        ],
        
        # Project tracking
        "docs/project_tracking": [
            "PROJECT TRACKER",  # Move entire directory
            "Payslip_Dictionary_Implementation_Plan.txt",
            "change_request.md",
            "filtering_summary_report.md"
        ],
        
        # Debug and temp files
        "temp": [
            "allowance_debug.txt", "allowance_debug2.txt",
            "regex_after_department.txt", "temp.txt"
        ],
        
        # Vendor
        "vendor": [
            "vendor/tesseract"  # Move entire directory
        ]
    }
    
    # Move files to their designated locations
    for target_dir, files in file_moves.items():
        for file_path in files:
            if os.path.exists(file_path):
                try:
                    if os.path.isdir(file_path):
                        # Move entire directory
                        target_path = os.path.join(target_dir, os.path.basename(file_path))
                        if os.path.exists(target_path):
                            shutil.rmtree(target_path)
                        shutil.move(file_path, target_path)
                        print(f"Moved directory: {file_path} -> {target_path}")
                    else:
                        # Move individual file
                        target_path = os.path.join(target_dir, os.path.basename(file_path))
                        if os.path.exists(target_path):
                            os.remove(target_path)
                        shutil.move(file_path, target_path)
                        print(f"Moved file: {file_path} -> {target_path}")
                except Exception as e:
                    print(f"Error moving {file_path}: {e}")

def organize_reports():
    """Organize report files properly"""
    
    # Move all reports from backend-dist/reports to reports/
    if os.path.exists("backend-dist/reports"):
        try:
            # Move payroll audit reports
            if os.path.exists("backend-dist/reports/payroll_audit_reports"):
                target_dir = "reports/audit_reports"
                if os.path.exists(target_dir):
                    shutil.rmtree(target_dir)
                shutil.move("backend-dist/reports/payroll_audit_reports", target_dir)
                print(f"Moved payroll audit reports to: {target_dir}")
            
            # Move other report directories
            for item in os.listdir("backend-dist/reports"):
                item_path = os.path.join("backend-dist/reports", item)
                if os.path.isdir(item_path):
                    target_path = os.path.join("reports", item)
                    if os.path.exists(target_path):
                        shutil.rmtree(target_path)
                    shutil.move(item_path, target_path)
                    print(f"Moved report directory: {item_path} -> {target_path}")
            
            # Remove empty reports directory
            if os.path.exists("backend-dist/reports") and not os.listdir("backend-dist/reports"):
                os.rmdir("backend-dist/reports")
                
        except Exception as e:
            print(f"Error organizing reports: {e}")

def clean_old_comparison_files():
    """Clean up old comparison files from backend-dist"""
    
    comparison_files = [
        "backend-dist/payroll_comparison_20250502_170846.csv",
        "backend-dist/payroll_comparison_20250502_170846.json", 
        "backend-dist/payroll_comparison_20250502_170846.xlsx",
        "backend-dist/payroll_comparison_20250502_171155.csv",
        "backend-dist/payroll_comparison_20250502_171155.json",
        "backend-dist/payroll_comparison_20250502_171155.xlsx",
        "backend-dist/payroll_comparison_20250502_173812.csv",
        "backend-dist/payroll_comparison_20250502_173812.json",
        "backend-dist/payroll_comparison_20250502_173812.xlsx",
        "backend-dist/payroll_comparison_20250502_174656.csv",
        "backend-dist/payroll_comparison_20250502_174656.json",
        "backend-dist/payroll_comparison_20250502_174656.xlsx",
        "backend-dist/payroll_comparison_20250502_184312.csv",
        "backend-dist/payroll_comparison_20250502_184312.json",
        "backend-dist/payroll_comparison_20250502_184312.xlsx"
    ]
    
    # Move to archive
    archive_dir = "reports/archive/comparison_files_may_2025"
    Path(archive_dir).mkdir(parents=True, exist_ok=True)
    
    for file_path in comparison_files:
        if os.path.exists(file_path):
            try:
                target_path = os.path.join(archive_dir, os.path.basename(file_path))
                shutil.move(file_path, target_path)
                print(f"Archived: {file_path} -> {target_path}")
            except Exception as e:
                print(f"Error archiving {file_path}: {e}")

def clean_cache_and_temp():
    """Clean up cache and temporary files"""
    
    # Move __pycache__ to cache directory
    if os.path.exists("backend-dist/__pycache__"):
        try:
            target_path = "cache/backend_pycache"
            if os.path.exists(target_path):
                shutil.rmtree(target_path)
            shutil.move("backend-dist/__pycache__", target_path)
            print(f"Moved cache: backend-dist/__pycache__ -> {target_path}")
        except Exception as e:
            print(f"Error moving cache: {e}")

def cleanup_empty_directories():
    """Remove empty directories"""
    
    # Remove backend-dist if empty
    if os.path.exists("backend-dist"):
        try:
            # Move README if it exists
            if os.path.exists("backend-dist/README.md"):
                shutil.move("backend-dist/README.md", "docs/technical/backend_README.md")
            
            # Remove if empty
            if not os.listdir("backend-dist"):
                os.rmdir("backend-dist")
                print("Removed empty backend-dist directory")
        except Exception as e:
            print(f"Error cleaning backend-dist: {e}")

def create_organization_summary():
    """Create a summary of the organization"""
    
    summary = f"""# Directory Organization Summary
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## New Directory Structure

### Source Code
- `src/frontend/` - All HTML, CSS, JS frontend files
- `src/backend/` - Backend Python processing files  
- `src/assets/` - Images, icons, and static assets
- `src/templates/` - Template files

### Configuration & Build
- `config/` - Package.json and configuration files
- `build/` - Build artifacts and icons
- `dist/` - Distribution files (unchanged)

### Data Management
- `data/input/` - Input files like Checklist1.xlsx
- `data/output/` - Generated output files
- `data/samples/` - Sample PDF files
- `data/templates/` - Excel templates
- `data/dictionaries/` - Payroll dictionaries

### Reports
- `reports/audit_reports/` - All payroll audit reports
- `reports/filtered_reports/` - Filtered report outputs
- `reports/comparison_reports/` - Comparison analysis
- `reports/archive/` - Archived old reports

### Scripts
- `scripts/processing/` - Core processing scripts
- `scripts/utilities/` - Utility and helper scripts
- `scripts/testing/` - Test scripts
- `scripts/setup/` - Installation and setup scripts

### Documentation
- `docs/user_guides/` - User documentation
- `docs/technical/` - Technical documentation
- `docs/project_tracking/` - Project management files

### System
- `tests/` - Test files and data
- `temp/` - Temporary files
- `cache/` - Cache files
- `vendor/` - Third-party dependencies
- `node_modules/` - NPM dependencies (unchanged)

## Files Organized: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

All files have been moved to their appropriate locations based on their function and type.
Old comparison files have been archived to maintain history.
Empty directories have been cleaned up.

## Next Steps
1. Update any hardcoded paths in scripts
2. Update package.json scripts if needed
3. Test application functionality
4. Update documentation with new paths
"""
    
    with open("docs/technical/directory_organization_summary.md", "w") as f:
        f.write(summary)
    
    print("Created organization summary")

def main():
    """Main organization function"""
    
    print("=== Templar Payroll Auditor Directory Organization ===")
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create directory structure
    print("\n1. Creating directory structure...")
    create_directory_structure()
    
    # Organize files
    print("\n2. Organizing files...")
    organize_files()
    
    # Organize reports
    print("\n3. Organizing reports...")
    organize_reports()
    
    # Clean old comparison files
    print("\n4. Archiving old comparison files...")
    clean_old_comparison_files()
    
    # Clean cache and temp
    print("\n5. Cleaning cache and temporary files...")
    clean_cache_and_temp()
    
    # Cleanup empty directories
    print("\n6. Cleaning up empty directories...")
    cleanup_empty_directories()
    
    # Create summary
    print("\n7. Creating organization summary...")
    create_organization_summary()
    
    print(f"\n=== Organization Complete ===")
    print(f"Finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\nDirectory has been organized into a proper structure.")
    print("Check docs/technical/directory_organization_summary.md for details.")

if __name__ == "__main__":
    main()
