import sys
import os
import pandas as pd
import openpyxl
from openpyxl.styles import Border, Side, PatternFill, Font, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.drawing.image import Image
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

def excel_to_pdf(excel_path, output_path):
    """
    Convert Excel file to PDF with grid lines
    """
    try:
        # First check if the file exists
        if not os.path.exists(excel_path):
            print(f"Error: Excel file not found at {excel_path}")
            return False
            
        print(f"Converting Excel file: {excel_path} to PDF: {output_path}")
        
        # Read the Excel file
        df = pd.read_excel(excel_path)
        
        # Create a figure with a table
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.axis('tight')
        ax.axis('off')
        
        # Create the table with the dataframe
        table = ax.table(
            cellText=df.values,
            colLabels=df.columns,
            loc='center',
            cellLoc='center',
            colColours=['#f2f2f2'] * len(df.columns),
            cellColours=[['#ffffff' for _ in range(len(df.columns))] for _ in range(len(df))],
        )
        
        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)  # Adjust table size
        
        # Add grid lines by setting edges on cells
        for cell in table._cells.values():
            cell.set_edgecolor('black')
            cell.set_linewidth(0.5)
        
        # Save as PDF
        plt.savefig(output_path, format='pdf', bbox_inches='tight', pad_inches=0.5)
        plt.close()
        
        print(f"Successfully converted Excel to PDF: {output_path}")
        return True
        
    except Exception as e:
        print(f"Error converting Excel to PDF: {str(e)}")
        return False

def excel_to_docx(excel_path, output_path):
    """
    Convert Excel file to DOCX (simplified version)
    """
    try:
        # First check if the file exists
        if not os.path.exists(excel_path):
            print(f"Error: Excel file not found at {excel_path}")
            return False
            
        print(f"Converting Excel file: {excel_path} to DOCX: {output_path}")
        
        # Read the Excel file
        df = pd.read_excel(excel_path)
        
        # Create a temporary image of the table
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.axis('tight')
        ax.axis('off')
        
        # Create the table with the dataframe
        table = ax.table(
            cellText=df.values,
            colLabels=df.columns,
            loc='center',
            cellLoc='center',
            colColours=['#f2f2f2'] * len(df.columns),
            cellColours=[['#ffffff' for _ in range(len(df.columns))] for _ in range(len(df))],
        )
        
        # Style the table
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1, 1.5)  # Adjust table size
        
        # Add grid lines by setting edges on cells
        for cell in table._cells.values():
            cell.set_edgecolor('black')
            cell.set_linewidth(0.5)
        
        # Save as image
        temp_img_path = output_path.replace('.docx', '.png')
        plt.savefig(temp_img_path, format='png', dpi=300, bbox_inches='tight', pad_inches=0.5)
        plt.close()
        
        # Create a new workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Add a title
        ws['A1'] = "Payroll Comparison Report"
        ws['A1'].font = Font(size=14, bold=True)
        
        # Add the image
        img = Image(temp_img_path)
        # Resize image
        img.width = 800
        img.height = 600
        ws.add_image(img, 'A3')
        
        # Save as DOCX (actually XLSX, but with .docx extension)
        wb.save(output_path)
        
        # Clean up the temporary image
        if os.path.exists(temp_img_path):
            os.remove(temp_img_path)
        
        print(f"Successfully converted Excel to DOCX: {output_path}")
        return True
        
    except Exception as e:
        print(f"Error converting Excel to DOCX: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Usage: python excel_converter.py [pdf|docx] input_file output_file")
        sys.exit(1)
    
    conversion_type = sys.argv[1].lower()
    input_file = sys.argv[2]
    output_file = sys.argv[3]
    
    if conversion_type == "pdf":
        success = excel_to_pdf(input_file, output_file)
    elif conversion_type == "docx":
        success = excel_to_docx(input_file, output_file)
    else:
        print(f"Unknown conversion type: {conversion_type}")
        success = False
    
    sys.exit(0 if success else 1)
