<!-- Enhanced Dictionary Manager UI -->
<div class="content-container hidden" id="enhanced-dictionary-content">
  <h2>Enhanced Dictionary Manager</h2>
  <p class="description">Manage payslip dictionary for improved extraction accuracy.</p>

  <div class="dictionary-actions">
    <button id="import-dictionary-btn" class="action-button">
      <i class="fas fa-file-import"></i> Import from Excel
    </button>
    <button id="export-dictionary-btn" class="action-button">
      <i class="fas fa-file-export"></i> Export to Excel
    </button>
    <button id="reset-enhanced-dictionary-btn" class="action-button warning">
      <i class="fas fa-undo"></i> Reset to Defaults
    </button>
  </div>

  <div class="dictionary-tabs">
    <button class="dictionary-tab-button active" id="personal-details-tab">Personal Details</button>
    <button class="dictionary-tab-button" id="earnings-tab">Earnings</button>
    <button class="dictionary-tab-button" id="deductions-tab">Deductions</button>
    <button class="dictionary-tab-button" id="employers-contribution-tab">Employers Contribution</button>
    <button class="dictionary-tab-button" id="loans-tab">Loans</button>
    <button class="dictionary-tab-button" id="bank-details-tab">Bank Details</button>
    <button class="dictionary-tab-button" id="add-section-tab">+ Add Section</button>
  </div>

  <!-- Personal Details Section -->
  <div class="dictionary-tab-content active" id="personal-details-content">
    <div class="section-header">
      <h3>PERSONAL DETAILS SECTION</h3>
      <div class="section-actions">
        <button id="add-personal-details-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="personal-details-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Earnings Section -->
  <div class="dictionary-tab-content" id="earnings-content">
    <div class="section-header">
      <h3>EARNINGS SECTION</h3>
      <div class="section-actions">
        <button id="add-earnings-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="earnings-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Deductions Section -->
  <div class="dictionary-tab-content" id="deductions-content">
    <div class="section-header">
      <h3>DEDUCTIONS SECTION</h3>
      <div class="section-actions">
        <button id="add-deductions-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="deductions-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Employers Contribution Section -->
  <div class="dictionary-tab-content" id="employers-contribution-content">
    <div class="section-header">
      <h3>EMPLOYERS CONTRIBUTION SECTION</h3>
      <div class="section-actions">
        <button id="add-employers-contribution-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="employers-contribution-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Loans Section -->
  <div class="dictionary-tab-content" id="loans-content">
    <div class="section-header">
      <h3>LOANS SECTION</h3>
      <div class="section-actions">
        <button id="add-loans-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="loans-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Bank Details Section -->
  <div class="dictionary-tab-content" id="bank-details-content">
    <div class="section-header">
      <h3>EMPLOYEE BANK DETAILS SECTION</h3>
      <div class="section-actions">
        <button id="add-bank-details-item-btn" class="add-item-button">
          <i class="fas fa-plus"></i> Add Item
        </button>
      </div>
    </div>

    <div class="dictionary-items-container">
      <table class="dictionary-items-table">
        <thead>
          <tr>
            <th>LINE ITEM</th>
            <th>FORMAT</th>
            <th>VALUE FORMAT</th>
            <th>INCLUDE IN REPORT</th>
            <th>ACTIONS</th>
          </tr>
        </thead>
        <tbody id="bank-details-items">
          <!-- Items will be added here dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- Add Section Tab Content -->
  <div class="dictionary-tab-content" id="add-section-content">
    <div class="section-header">
      <h3>Add New Section</h3>
    </div>

    <div class="add-section-form">
      <div class="form-group">
        <label for="new-section-name">Section Name:</label>
        <input type="text" id="new-section-name" placeholder="e.g., ADDITIONAL INFORMATION">
      </div>
      <button id="create-section-btn" class="primary-button">Create Section</button>
    </div>
  </div>

  <!-- Item Edit Modal -->
  <div id="item-edit-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h3 id="edit-modal-title">Edit Item</h3>

      <div class="form-group">
        <label for="edit-item-name">Line Item Name:</label>
        <input type="text" id="edit-item-name">
      </div>

      <div class="form-group">
        <label for="edit-item-format">Format:</label>
        <input type="text" id="edit-item-format" placeholder="e.g., [UC], [TC][.]">
        <div class="format-helper">
          <button id="format-helper-btn" class="small-button">Format Help</button>
          <div id="format-helper-content" class="format-helper-content hidden">
            <p><strong>Capitalization:</strong> [UC] = ALL CAPS, [TC] = Title Case, [LC] = lowercase</p>
            <p><strong>Special Characters:</strong> [.] = period, [-] = hyphen, [_] = underscore, [/] = slash, [#] = numbers</p>
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="edit-item-value-format">Value Format:</label>
        <select id="edit-item-value-format">
          <option value="Section Header">Section Header</option>
          <option value="Column Header">Column Header</option>
          <option value="Text">Text</option>
          <option value="Numeric">Numeric</option>
          <option value="Numeric with decimal places">Numeric with decimal places</option>
          <option value="Alphanumeric">Alphanumeric</option>
          <option value="Alphanumeric with hyphens">Alphanumeric with hyphens</option>
          <option value="Date">Date</option>
        </select>
      </div>

      <div class="form-group checkbox-group">
        <input type="checkbox" id="edit-item-include-in-report">
        <label for="edit-item-include-in-report">Include in Report</label>
      </div>

      <div class="modal-actions">
        <button id="save-item-btn" class="primary-button">Save</button>
        <button id="cancel-edit-btn" class="secondary-button">Cancel</button>
      </div>
    </div>
  </div>

  <div class="action-container">
    <button id="save-enhanced-dictionary-btn" class="primary-button">Save All Changes</button>
  </div>
</div>

<!-- Add CSS for the enhanced dictionary manager -->
<style>
  .dictionary-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .dictionary-items-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
  }

  .dictionary-items-table {
    width: 100%;
    border-collapse: collapse;
  }

  .dictionary-items-table th,
  .dictionary-items-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  .dictionary-items-table th {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  .dictionary-items-table tr:hover {
    background-color: #f9f9f9;
  }

  .item-actions {
    display: flex;
    gap: 5px;
  }

  .add-item-button {
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
  }

  .edit-button {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 3px 8px;
    cursor: pointer;
  }

  .delete-button {
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 3px 8px;
    cursor: pointer;
  }

  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    border-radius: 5px;
    width: 60%;
    max-width: 600px;
  }

  .close-modal {
    float: right;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .checkbox-group input[type="checkbox"] {
    margin: 0;
  }

  .format-helper {
    margin-top: 5px;
  }

  .format-helper-content {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 5px;
    font-size: 12px;
  }

  .small-button {
    font-size: 12px;
    padding: 2px 5px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
  }

  .hidden {
    display: none;
  }

  .warning {
    background-color: #ff9800;
    color: white;
  }

  .add-section-form {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    max-width: 500px;
  }

  /* Toggle Switch Styles */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
  }

  .toggle-switch .toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 20px;
  }

  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  .toggle-input:checked + .toggle-slider {
    background-color: #2196F3;
  }

  .toggle-input:focus + .toggle-slider {
    box-shadow: 0 0 1px #2196F3;
  }

  .toggle-input:checked + .toggle-slider:before {
    transform: translateX(20px);
  }
</style>
