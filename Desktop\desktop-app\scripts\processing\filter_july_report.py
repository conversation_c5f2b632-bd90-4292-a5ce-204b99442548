#!/usr/bin/env python3
"""
Filter July 2025 Run 1 Payroll Audit Report based on Checklist1.xlsx
- Match expected changes against checklist definitions
- Remove changes not matching checklist criteria
- Remove all UTILITY SUBSIDY related changes
- Keep items not in checklist but present in report
"""

import json
import pandas as pd
import re
from datetime import datetime
import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import copy

def load_checklist(checklist_path):
    """Load and process the checklist Excel file"""
    print(f"Loading checklist from: {checklist_path}")
    
    try:
        df = pd.read_excel(checklist_path)
        print(f"Checklist loaded: {df.shape[0]} items")
        
        # Create a dictionary for quick lookup
        checklist_dict = {}
        
        for _, row in df.iterrows():
            section = str(row['Section']).upper().strip()
            item_name = str(row['Item Name']).upper().strip()
            
            # Create key for lookup
            key = f"{section}:{item_name}"
            
            checklist_dict[key] = {
                'section': section,
                'item_name': item_name,
                'new': str(row['NEW']).upper() == '✅ YES',
                'increase': str(row['INCREASE']).upper() == '✅ YES',
                'decrease': str(row['DECREASE']).upper() == '✅ YES',
                'removed': str(row['REMOVED']).upper() == '✅ YES',
                'keep_in_report': str(row['Keep in Report']).upper() == '✅ YES'
            }
        
        print(f"Processed {len(checklist_dict)} checklist items")
        return checklist_dict
        
    except Exception as e:
        print(f"Error loading checklist: {e}")
        return {}

def extract_change_info(change_text):
    """Extract information from a change description"""
    change_info = {
        'item_name': '',
        'change_type': 'other',
        'section': 'other',
        'is_utility_subsidy': False
    }
    
    # Check for UTILITY SUBSIDY
    if 'UTILITY SUBSIDY' in change_text.upper():
        change_info['is_utility_subsidy'] = True
        change_info['item_name'] = 'UTILITY SUBSIDY'
        change_info['section'] = 'DEDUCTIONS'
    
    # Extract change type
    if 'changed from' in change_text.lower():
        if 'increase of' in change_text.lower():
            change_info['change_type'] = 'increase'
        elif 'decrease of' in change_text.lower():
            change_info['change_type'] = 'decrease'
        else:
            change_info['change_type'] = 'change'
    elif 'New ' in change_text or 'new ' in change_text.lower():
        change_info['change_type'] = 'new'
    elif 'Removed ' in change_text or 'removed ' in change_text.lower():
        change_info['change_type'] = 'removed'
    
    # Try to extract item name from various patterns
    patterns = [
        r'New ([A-Z\s\-\.]+) \(',
        r'Removed ([A-Z\s\-\.]+) \(',
        r'([A-Z\s\-\.]+) changed from',
        r'; ([A-Z\s\-\.]+) changed from',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, change_text)
        if match:
            item_name = match.group(1).strip()
            if len(item_name) > 3:  # Avoid short matches
                change_info['item_name'] = item_name.upper()
                break
    
    # Determine section based on common patterns
    earnings_keywords = ['ALLOWANCE', 'SALARY', 'TRANSPORT', 'RENT', 'LEAVE', 'SUBSISTENCE', 'FUEL', 'RESPONSIBILITY']
    deductions_keywords = ['TAX', 'TITHES', 'PENSION', 'WELFARE', 'HEALTH', 'LOAN', 'ADVANCE', 'SUBSIDY']
    
    item_upper = change_info['item_name'].upper()
    if any(keyword in item_upper for keyword in earnings_keywords):
        change_info['section'] = 'EARNINGS'
    elif any(keyword in item_upper for keyword in deductions_keywords):
        change_info['section'] = 'DEDUCTIONS'
    
    return change_info

def should_keep_change(change_text, checklist_dict):
    """Determine if a change should be kept based on checklist"""
    
    # Always remove UTILITY SUBSIDY changes
    if 'UTILITY SUBSIDY' in change_text.upper():
        return False, "UTILITY SUBSIDY removal"
    
    # Extract change information
    change_info = extract_change_info(change_text)
    
    # If we couldn't extract meaningful info, keep it (items not in checklist)
    if not change_info['item_name'] or len(change_info['item_name']) < 3:
        return True, "Item not in checklist - keeping"
    
    # Look for matching checklist entry
    possible_keys = [
        f"{change_info['section']}:{change_info['item_name']}",
        f"DEDUCTIONS:{change_info['item_name']}",
        f"EARNINGS:{change_info['item_name']}",
        f"OTHER:{change_info['item_name']}"
    ]
    
    checklist_entry = None
    for key in possible_keys:
        if key in checklist_dict:
            checklist_entry = checklist_dict[key]
            break
    
    # If not in checklist, keep it
    if not checklist_entry:
        return True, f"Item '{change_info['item_name']}' not in checklist - keeping"
    
    # Check if this type of change is allowed for this item
    change_type = change_info['change_type']
    
    if change_type == 'new' and checklist_entry['new']:
        return True, f"NEW change allowed for {change_info['item_name']}"
    elif change_type == 'increase' and checklist_entry['increase']:
        return True, f"INCREASE change allowed for {change_info['item_name']}"
    elif change_type == 'decrease' and checklist_entry['decrease']:
        return True, f"DECREASE change allowed for {change_info['item_name']}"
    elif change_type == 'removed' and checklist_entry['removed']:
        return True, f"REMOVED change allowed for {change_info['item_name']}"
    elif checklist_entry['keep_in_report']:
        return True, f"Item {change_info['item_name']} marked to keep in report"
    else:
        return False, f"{change_type.upper()} change not allowed for {change_info['item_name']}"

def filter_report_data(report_data, checklist_dict):
    """Filter the report data based on checklist"""
    print("Filtering report data...")
    
    filtered_data = []
    total_changes_removed = 0
    total_employees_processed = 0
    
    for employee in report_data:
        total_employees_processed += 1
        
        if total_employees_processed % 1000 == 0:
            print(f"Processed {total_employees_processed} employees...")
        
        # Create a copy of the employee data
        filtered_employee = copy.deepcopy(employee)
        
        # Filter itemized changes
        original_changes = employee.get('ITEMIZED CHANGE', [])
        filtered_changes = []
        
        for change in original_changes:
            keep_change, reason = should_keep_change(change, checklist_dict)
            
            if keep_change:
                filtered_changes.append(change)
            else:
                total_changes_removed += 1
                if total_changes_removed <= 10:  # Log first 10 removals
                    print(f"  Removed: {change[:100]}... | Reason: {reason}")
        
        # Update the employee's changes
        filtered_employee['ITEMIZED CHANGE'] = filtered_changes
        
        # Recalculate change summary if no changes remain
        if not filtered_changes:
            filtered_employee['CHANGE (increase or decrease)'] = "No changes"
        
        filtered_data.append(filtered_employee)
    
    print(f"Filtering complete:")
    print(f"  - Total employees processed: {total_employees_processed}")
    print(f"  - Total changes removed: {total_changes_removed}")
    
    return filtered_data

def create_filtered_word_report(filtered_data, output_path):
    """Create a Word document from the filtered data"""
    print(f"Creating filtered Word report: {output_path}")

    try:
        # Create a new Word document
        doc = Document()

        # Set document properties
        title = doc.add_heading('PAYROLL PRE-AUDIT REPORT: July 2025 (FILTERED)', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Make sure the title is bold
        for run in title.runs:
            run.bold = True

        # Add timestamp and filtering info
        info_table = doc.add_table(rows=3, cols=2)
        info_table.style = 'Table Grid'
        info_table.autofit = True

        # Left column - Generated date and period
        info_table.cell(0, 0).text = f'Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        info_table.cell(1, 0).text = f'Period: July 2025'
        info_table.cell(2, 0).text = f'Filtered based on: Checklist1.xlsx'

        # Right column - Filtering information
        info_table.cell(0, 1).text = f'Total employees: {len(filtered_data)}'
        info_table.cell(1, 1).text = f'UTILITY SUBSIDY changes: REMOVED'
        info_table.cell(2, 1).text = f'Checklist validation: APPLIED'

        # Format the table
        for row in info_table.rows:
            for cell in row.cells:
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.size = Pt(10)

        # Add some space after the table
        doc.add_paragraph()

        # Count statistics
        employees_with_changes = 0
        total_changes = 0

        for emp in filtered_data:
            changes = emp.get('ITEMIZED CHANGE', [])
            if changes and any(change.strip() for change in changes):
                employees_with_changes += 1
                total_changes += len(changes)

        # Add statistics section
        stats_heading = doc.add_heading('Statistics', level=1)
        stats_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        stats_table = doc.add_table(rows=3, cols=2)
        stats_table.style = 'Table Grid'

        # Add statistics data
        stats_rows = [
            ("Total employees analyzed:", str(len(filtered_data))),
            ("Employees with changes:", str(employees_with_changes)),
            ("Total changes after filtering:", str(total_changes))
        ]

        for i, (label, value) in enumerate(stats_rows):
            stats_table.cell(i, 0).text = label
            stats_table.cell(i, 1).text = value

            # Apply formatting
            stats_table.cell(i, 0).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT
            stats_table.cell(i, 1).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Add itemized changes section
        itemized_heading = doc.add_heading('Itemized Changes (Filtered)', level=1)
        itemized_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Add a department heading
        dept_heading = doc.add_paragraph('Department: All Departments', style='Heading 2')
        dept_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        # Process employees with changes
        changes_added = 0
        for emp in filtered_data:
            changes = emp.get('ITEMIZED CHANGE', [])

            # Skip employees with no changes
            if not changes or not any(change.strip() for change in changes):
                continue

            # Add employee information
            emp_id = emp.get("EMPLOYEE NO.", "")
            dept = emp.get("DEPARTMENT", "ALL DEPARTMENTS")

            # Add employee heading
            emp_heading = doc.add_paragraph(f"{emp_id}: {dept}", style='Heading 3')
            emp_heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # Add changes as bullet points
            for change in changes:
                if change.strip():
                    change_para = doc.add_paragraph(change, style='List Bullet')
                    changes_added += 1

            # Add some space between employees
            doc.add_paragraph()

            # Limit to prevent overly large documents
            if changes_added > 1000:
                doc.add_paragraph("... (Additional changes truncated for document size)")
                break

        # Save the document
        doc.save(output_path)
        print(f"Word document saved successfully: {output_path}")
        print(f"  - Changes included: {changes_added}")

    except Exception as e:
        print(f"Error creating Word document: {e}")

def main():
    """Main function to filter the July 2025 report"""

    # File paths
    checklist_path = "Checklist1.xlsx"
    report_path = "backend-dist/reports/payroll_audit_reports/Payroll_Audit_Report_July_2025_Run_01.json"

    # Output paths
    output_json = "backend-dist/reports/payroll_audit_reports/Payroll_Audit_Report_July_2025_Run_01_Filtered.json"
    output_csv = "backend-dist/reports/payroll_audit_reports/Payroll_Audit_Report_July_2025_Run_01_Filtered.csv"
    output_docx = "backend-dist/reports/payroll_audit_reports/Payroll_Audit_Report_July_2025_Run_01_Filtered.docx"

    print("=== July 2025 Report Filtering Tool ===")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Load checklist
    checklist_dict = load_checklist(checklist_path)
    if not checklist_dict:
        print("Failed to load checklist. Exiting.")
        return

    # Load report data
    print(f"Loading report data from: {report_path}")
    try:
        with open(report_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        print(f"Report loaded: {len(report_data)} employees")
    except Exception as e:
        print(f"Error loading report: {e}")
        return

    # Filter the data
    filtered_data = filter_report_data(report_data, checklist_dict)

    # Save filtered JSON
    print(f"Saving filtered JSON to: {output_json}")
    try:
        with open(output_json, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, indent=2, ensure_ascii=False)
        print("Filtered JSON saved successfully")
    except Exception as e:
        print(f"Error saving filtered JSON: {e}")

    # Convert to CSV
    print(f"Converting to CSV: {output_csv}")
    try:
        # Flatten the data for CSV
        csv_data = []
        for emp in filtered_data:
            emp_copy = emp.copy()
            # Join itemized changes with semicolon
            if isinstance(emp_copy.get('ITEMIZED CHANGE'), list):
                emp_copy['ITEMIZED CHANGE'] = '; '.join(emp_copy['ITEMIZED CHANGE'])
            csv_data.append(emp_copy)

        df = pd.DataFrame(csv_data)
        df.to_csv(output_csv, index=False, encoding='utf-8')
        print("CSV saved successfully")
    except Exception as e:
        print(f"Error saving CSV: {e}")

    # Create Word document
    create_filtered_word_report(filtered_data, output_docx)

    print("\n=== Filtering Complete ===")
    print(f"Original employees: {len(report_data)}")
    print(f"Filtered employees: {len(filtered_data)}")
    print(f"Files created:")
    print(f"  - {output_json}")
    print(f"  - {output_csv}")
    print(f"  - {output_docx}")

if __name__ == "__main__":
    main()
