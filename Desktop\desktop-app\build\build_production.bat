@echo off
echo Building TEMPLAR PAYROLL AUDITOR production package...

REM Prepare Python environment
echo Preparing Python environment...
call prepare_python_env.bat

REM Create build directory for icons if it doesn't exist
if not exist build (
    mkdir build
)

REM Create a simple icon for the application if it doesn't exist
if not exist build\icon.ico (
    echo Creating icon for the application...
    powershell -Command "$iconPath = 'build\icon.ico'; if (-not (Test-Path $iconPath)) { [System.Drawing.Icon]::ExtractAssociatedIcon('C:\Windows\System32\notepad.exe').Save($iconPath) }"
)

REM Build the production package
echo Building production package...
npm run build

echo Production build complete!
echo The installer can be found in the 'dist' directory.
pause
