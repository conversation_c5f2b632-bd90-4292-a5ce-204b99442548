import sys
import os
import pandas as pd
import tempfile
import argparse
from docx import Document
from docx.shared import Pt, <PERSON><PERSON>, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import json

def create_pdf_report(data, output_path):
    """
    Create a PDF report directly from data using pandas and matplotlib
    """
    try:
        print(f"Creating PDF report: {output_path}")

        # If data is a string (JSON), parse it
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except:
                print("Error parsing JSON data")
                return False

        # Convert to DataFrame if it's a dict or list
        if isinstance(data, dict) or isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = data

        # Create a PDF with matplotlib and PdfPages
        with PdfPages(output_path) as pdf:
            # Create a figure for the title page
            fig, ax = plt.subplots(figsize=(11, 8.5))
            ax.axis('off')
            ax.text(0.5, 0.6, 'Payroll Audit Report',
                   fontsize=28, ha='center', va='center', weight='bold')
            ax.text(0.5, 0.5, 'Generated by Templar Systems Payroll Auditor',
                   fontsize=16, ha='center', va='center')
            ax.text(0.5, 0.4, f'Generated on: {pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")}',
                   fontsize=12, ha='center', va='center')
            pdf.savefig(fig)
            plt.close(fig)

            # Create a figure for the data
            # Calculate figure size based on data size
            rows, cols = df.shape
            fig_width = min(11, max(8.5, cols * 1.2))
            fig_height = min(20, max(8.5, rows * 0.4 + 2))

            fig, ax = plt.subplots(figsize=(fig_width, fig_height))
            ax.axis('off')

            # Create a table with the dataframe
            table = ax.table(
                cellText=df.values,
                colLabels=df.columns,
                loc='center',
                cellLoc='center'
            )

            # Style the table
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 1.5)

            # Style header row
            for i, key in enumerate(df.columns):
                cell = table[(0, i)]
                cell.set_text_props(weight='bold', color='white')
                cell.set_facecolor('#4CAF50')  # Green header

            # Add alternating row colors for better readability
            for i in range(len(df)):
                for j in range(len(df.columns)):
                    cell = table[(i + 1, j)]
                    if i % 2 == 0:
                        cell.set_facecolor('#f2f2f2')  # Light gray for even rows

            # Save the figure to the PDF with tight layout
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)

        print(f"Successfully created PDF report: {output_path}")
        return True

    except Exception as e:
        print(f"Error creating PDF report: {str(e)}")
        return False

def create_word_report(data, output_path):
    """
    Create a Word report directly from data with improved formatting
    """
    try:
        print(f"Creating Word report: {output_path}")

        # If data is a string (JSON), parse it
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except:
                print("Error parsing JSON data")
                return False

        # Convert to DataFrame if it's a dict or list
        if isinstance(data, dict) or isinstance(data, list):
            df = pd.DataFrame(data)
        else:
            df = data

        # Create a new Word document
        doc = Document()

        # Set margins for better layout
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(0.5)
            section.bottom_margin = Inches(0.5)
            section.left_margin = Inches(0.5)
            section.right_margin = Inches(0.5)

        # Add a title
        title = doc.add_heading('Payroll Audit Report', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add a subtitle
        subtitle = doc.add_paragraph('Generated by Templar Systems Payroll Auditor')
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add timestamp
        timestamp = doc.add_paragraph(f'Generated on: {pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")}')
        timestamp.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add some space
        doc.add_paragraph()

        # Add a table with better styling
        table = doc.add_table(rows=1, cols=len(df.columns))
        table.style = 'Table Grid'

        # Make the table take the full width of the page
        table.autofit = False
        table.allow_autofit = False

        # Style the header row
        header_cells = table.rows[0].cells
        for i, column in enumerate(df.columns):
            cell = header_cells[i]
            cell.text = str(column)
            # Make header bold with green background
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True
                    run.font.size = Pt(11)
            # Set cell background color (using shading)
            cell_shading = cell._element.get_or_add_tcPr()
            cell_shading.append(parse_xml(r'<w:shd {} w:fill="4CAF50"/>'.format(nsdecls('w'))))
            # Center text
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add data rows with alternating colors
        for row_idx, row_data in enumerate(df.itertuples(index=False)):
            row_cells = table.add_row().cells

            # Set alternating row background color
            if row_idx % 2 == 0:
                for cell in row_cells:
                    cell_shading = cell._element.get_or_add_tcPr()
                    cell_shading.append(parse_xml(r'<w:shd {} w:fill="F2F2F2"/>'.format(nsdecls('w'))))

            # Add cell data
            for i, value in enumerate(row_data):
                cell = row_cells[i]
                cell.text = str(value)
                # Center text
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Save the document
        doc.save(output_path)

        print(f"Successfully created Word report: {output_path}")
        return True

    except Exception as e:
        print(f"Error creating Word report: {str(e)}")
        return False

def excel_to_pdf(excel_path, pdf_path):
    """
    Convert Excel file to PDF
    """
    try:
        print(f"Converting Excel to PDF: {excel_path} -> {pdf_path}")

        # Read the Excel file
        df = pd.read_excel(excel_path)

        # Create the PDF report
        return create_pdf_report(df, pdf_path)

    except Exception as e:
        print(f"Error converting Excel to PDF: {str(e)}")
        return False

def excel_to_docx(excel_path, docx_path):
    """
    Convert Excel file to DOCX format
    """
    try:
        print(f"Converting Excel to DOCX: {excel_path} -> {docx_path}")

        # Read the Excel file
        df = pd.read_excel(excel_path)

        # Create the Word report
        return create_word_report(df, docx_path)

    except Exception as e:
        print(f"Error converting Excel to DOCX: {str(e)}")
        return False

def json_to_pdf(json_path, pdf_path):
    """
    Convert JSON file to PDF
    """
    try:
        print(f"Converting JSON to PDF: {json_path} -> {pdf_path}")

        # Read the JSON file
        with open(json_path, 'r') as f:
            data = json.load(f)

        # Create the PDF report
        return create_pdf_report(data, pdf_path)

    except Exception as e:
        print(f"Error converting JSON to PDF: {str(e)}")
        return False

def json_to_docx(json_path, docx_path):
    """
    Convert JSON file to DOCX format
    """
    try:
        print(f"Converting JSON to DOCX: {json_path} -> {docx_path}")

        # Read the JSON file
        with open(json_path, 'r') as f:
            data = json.load(f)

        # Create the Word report
        return create_word_report(data, docx_path)

    except Exception as e:
        print(f"Error converting JSON to DOCX: {str(e)}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Convert files to PDF or DOCX')
    parser.add_argument('format', choices=['pdf', 'docx'], help='Output format')
    parser.add_argument('input_file', help='Input file path')
    parser.add_argument('output_file', help='Output file path')
    parser.add_argument('--source', choices=['excel', 'json'], default='excel', help='Source file type')

    args = parser.parse_args()

    if args.source == 'excel':
        if args.format == 'pdf':
            success = excel_to_pdf(args.input_file, args.output_file)
        else:  # docx
            success = excel_to_docx(args.input_file, args.output_file)
    else:  # json
        if args.format == 'pdf':
            success = json_to_pdf(args.input_file, args.output_file)
        else:  # docx
            success = json_to_docx(args.input_file, args.output_file)

    sys.exit(0 if success else 1)
