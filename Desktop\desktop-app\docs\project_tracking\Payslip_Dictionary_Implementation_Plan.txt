# PAYSLIP DICTIONARY IMPLEMENTATION PLAN

## OVERVIEW
This document outlines the implementation plan for enhancing the payslip extraction and reporting system with a comprehensive dictionary-based approach. The plan addresses the current issues with extraction accuracy and reporting flexibility.

## GOALS
1. Improve extraction accuracy for payslip line items
2. Standardize naming of items across different payslip formats
3. Provide better section recognition (EARNINGS, DEDUCTIONS, etc.)
4. Enable flexible reporting with the ability to exclude specific items
5. Support bulk operations through Excel import/export

## PHASE 1: DICTIONARY DATA STRUCTURE DESIGN

### Dictionary Schema
```json
{
  "SECTIONS": {
    "EARNINGS": {
      "display_name": "Earnings",
      "description": "Income items paid to the employee",
      "items": {
        "BASIC SALARY": {
          "variations": ["BASIC", "BASE SALARY", "BASE PAY"],
          "required": true,
          "include_in_report": true,
          "regex_pattern": "BASIC\\s+SALARY\\s+([\d,.]+)",
          "categories": ["Core", "Statutory"]
        },
        // Additional items...
      }
    },
    "DEDUCTIONS": {
      "display_name": "Deductions",
      "description": "Items deducted from employee's pay",
      "items": {
        "INCOME TAX": {
          "variations": ["PAYE", "TAX", "INCOME TAX DEDUCTION"],
          "required": true,
          "include_in_report": true,
          "regex_pattern": "INCOME\\s+TAX\\s+([\d,.]+)",
          "categories": ["Tax", "Statutory"]
        },
        // Additional items...
      }
    },
    // Additional sections...
  }
}
```

### Item Properties
- **variations**: Alternative names for the same item
- **required**: Whether the item must be present in every payslip
- **include_in_report**: Whether to include changes to this item in reports
- **regex_pattern**: Pattern for extracting this specific item
- **categories**: Tags for grouping and filtering items

## PHASE 2: DICTIONARY MANAGER UI REDESIGN

### Main Dictionary Manager Screen
- Tab-based interface with one tab per section (EARNINGS, DEDUCTIONS, etc.)
- Global actions: Import/Export Excel, Reset to Defaults
- Search functionality across all sections

### Section Tab Components
- List view of all items in the section with columns:
  * Standard Name
  * Required (toggle)
  * Include in Report (toggle)
  * Categories
  * Actions (Edit, Delete)
- "Add New Item" button
- Filter options (show only required, show only excluded from reports, etc.)

### Item Edit Dialog
- Fields for all item properties:
  * Standard Name (text input)
  * Variations (multi-input field)
  * Required (toggle)
  * Include in Report (toggle)
  * Categories (multi-select)
  * Regex Pattern (text input with "Generate" button)
- "Test Pattern" feature with sample payslip text
- Save/Cancel buttons

### Excel Import/Export
- Template with columns for all item properties
- Preview screen showing changes before import
- Validation to ensure data integrity
- Option to import only specific sections

## PHASE 3: EXTRACTION ENGINE ENHANCEMENT

### Regex Pattern Generation
```python
def generate_regex_for_item(standard_name, variations):
    # Create base pattern from standard name
    base_name = standard_name.replace(' ', '\\s+')

    # Add variations as alternatives
    if variations:
        variations_pattern = '|'.join([v.replace(' ', '\\s+') for v in variations])
        name_pattern = f"(?:{base_name}|{variations_pattern})"
    else:
        name_pattern = base_name

    # Pattern to capture the value (currency amount)
    value_pattern = "([\d,.]+)"

    # Complete pattern with flexible whitespace
    complete_pattern = f"{name_pattern}\\s+{value_pattern}"

    return complete_pattern
```

### Two-Phase Extraction Process
1. **First pass**: Use specific patterns for each expected item
2. **Second pass**: Use generic patterns to catch any remaining items

```python
def extract_section_items(section_name, payslip_text, dictionary):
    extracted_items = {}
    section_dict = dictionary["SECTIONS"][section_name]

    # First pass: Use specific patterns for each expected item
    for item_name, item_config in section_dict["items"].items():
        pattern = item_config["regex_pattern"]
        match = re.search(pattern, payslip_text)

        if match:
            extracted_items[item_name] = match.group(1).strip()
        elif item_config["required"]:
            print(f"Warning: Required item '{item_name}' not found in {section_name}")

    # Second pass: Look for unexpected items with generic pattern
    generic_pattern = r"([A-Z][A-Z\s\-\.]+)\s+([\d,.]+)"
    for match in re.finditer(generic_pattern, payslip_text):
        item_name = match.group(1).strip()
        item_value = match.group(2).strip()

        # Skip if already extracted
        if any(item_name in std_name or item_name in variations
               for std_name, config in section_dict["items"].items()
               for variations in config.get("variations", [])):
            continue

        # Add as unknown item
        extracted_items[f"UNKNOWN: {item_name}"] = item_value

    return extracted_items
```

### Validation During Extraction
- Check for missing required items
- Validate extracted values against expected formats
- Flag payslips with issues for review

## PHASE 4: REPORT FILTERING IMPLEMENTATION

### Report Configuration Interface
- Global settings:
  * Default inclusion for new items
  * Minimum change threshold
  * Sections to include
- Item-specific settings table
- Save/load report templates

### Filtering Logic
```python
def should_include_in_report(item_name, section_name, prev_value, curr_value, dictionary, report_config):
    # Get the item configuration
    item_config = dictionary["SECTIONS"][section_name]["items"].get(item_name, {})

    # Check if item is explicitly excluded
    if not item_config.get("include_in_report", True):
        return False

    # Check if section is excluded in report configuration
    if section_name not in report_config.get("included_sections", []):
        return False

    # Check threshold conditions if values exist
    if prev_value is not None and curr_value is not None:
        try:
            # Convert to numeric values for comparison
            prev_num = float(prev_value.replace(',', ''))
            curr_num = float(curr_value.replace(',', ''))

            # Calculate change
            absolute_change = abs(curr_num - prev_num)
            percentage_change = (absolute_change / prev_num * 100) if prev_num != 0 else float('inf')

            # Check against threshold
            threshold = item_config.get("report_threshold", {})
            if threshold:
                if threshold.get("type") == "percentage":
                    if percentage_change < threshold.get("value", 0):
                        return False
                elif threshold.get("type") == "absolute":
                    if absolute_change < threshold.get("value", 0):
                        return False
        except (ValueError, TypeError):
            # Non-numeric values, can't apply threshold
            pass

    # Check category filters
    if report_config.get("included_categories"):
        item_categories = item_config.get("categories", [])
        if not any(cat in report_config["included_categories"] for cat in item_categories):
            return False

    # All checks passed, include in report
    return True
```

### Report Generation Process
- Apply filters during report generation
- Indicate when items have been filtered out
- Provide option to view all changes

## PHASE 5: IMPLEMENTATION TIMELINE

### Day 1 (4-8 hours): Data Structure and Backend
- Design and implement dictionary data structure (2-3 hours)
- Modify existing storage mechanism to support new properties (1-2 hours)
- Develop regex generation logic (1-2 hours)
- Update extraction engine to use dictionary (1-2 hours)

### Day 2 (4-8 hours): Dictionary Manager UI
- Redesign tab-based interface (2-3 hours)
- Implement item edit dialog with new toggles (1-2 hours)
- Add Excel import/export functionality (1-2 hours)
- Implement search and filter functionality (1-2 hours)

### Day 3 (4-6 hours): Report Filtering
- Implement report configuration interface (2-3 hours)
- Develop filtering logic (1-2 hours)
- Update report generation process (1-2 hours)

### Day 4 (2-4 hours): Testing and Deployment
- Testing with sample payslips (1-2 hours)
- Bug fixes and refinements (1-2 hours)
- Deployment to production environment (1 hour)

## PHASE 6: MIGRATION PLAN

### Data Migration
- Create initial dictionary from existing extraction patterns
- Import known variations from previous extractions
- Set sensible defaults for required and include_in_report flags

### User Training
- Develop training materials for Dictionary Manager
- Create guides for report configuration
- Conduct training sessions for key users

### Rollout Strategy
- Pilot with a small group of users
- Gradual rollout to all users
- Monitor and address issues

## CONCLUSION
This implementation plan provides a comprehensive approach to enhancing the payslip extraction and reporting system. By implementing a dictionary-based approach with flexible reporting options, the system will deliver more accurate extraction results and more useful reports tailored to specific needs.
