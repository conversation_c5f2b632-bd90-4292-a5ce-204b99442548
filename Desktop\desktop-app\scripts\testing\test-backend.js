const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Paths to files
const pythonScript = path.join(__dirname, '..', 'backend', 'main.py');
const prevFile = path.join(__dirname, '..', 'backend', 'previous_payroll_sample.pdf');
const currFile = path.join(__dirname, '..', 'backend', 'current_payroll_sample.pdf');
const outputDir = path.join(__dirname, '..', 'backend');

// Check if files exist
console.log(`Python script exists: ${fs.existsSync(pythonScript)}`);
console.log(`Previous file exists: ${fs.existsSync(prevFile)}`);
console.log(`Current file exists: ${fs.existsSync(currFile)}`);
console.log(`Output directory exists: ${fs.existsSync(outputDir)}`);

// Run the Python script
console.log(`Running Python script: ${pythonScript}`);
console.log(`Parameters: ${prevFile}, ${currFile}, ${outputDir}`);

const backendProcess = spawn('python', [pythonScript, prevFile, currFile, outputDir]);

backendProcess.stdout.on('data', (data) => {
  const message = data.toString();
  console.log(`Python stdout: ${message}`);
});

backendProcess.stderr.on('data', (data) => {
  const errorMsg = data.toString();
  console.error(`Python stderr: ${errorMsg}`);
});

backendProcess.on('close', (code) => {
  console.log(`Python process exited with code ${code}`);
  
  // Check if reports were generated
  const files = fs.readdirSync(outputDir);
  const reports = files.filter(file => 
    file.startsWith('payroll_comparison_') && 
    (file.endsWith('.xlsx') || file.endsWith('.csv') || file.endsWith('.json'))
  );
  
  console.log('Generated reports:');
  reports.forEach(report => console.log(`- ${report}`));
});

backendProcess.on('error', (err) => {
  console.error(`Failed to start Python process: ${err.message}`);
});
