const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'api', {
    // File selection
    selectFile: (options) => ipcRenderer.invoke('select-file', options),
    selectDirectory: () => ipcRenderer.invoke('select-directory'),

    // Process PDFs
    processPdfs: (data) => ipcRenderer.invoke('process-pdfs', data),

    // PDF Sorter
    sortPdf: (data) => ipcRenderer.invoke('sort-pdf', data),

    // Data Builder
    buildDataTable: (data) => ipcRenderer.invoke('build-data-table', data),

    // Bank Adviser
    processBankAdvice: (data) => ipcRenderer.invoke('process-bank-advice', data),
    exportBankAdviser: (data) => ipcRenderer.invoke('export-bank-adviser', data),
    getDesktopPath: () => ipc<PERSON>enderer.invoke('get-desktop-path'),
    checkPathExists: (path) => ipcRenderer.invoke('check-path-exists', path),
    getExcelColumns: (filePath, headerRow, firstColumn) => ipcRenderer.invoke('get-excel-columns', filePath, headerRow, firstColumn),
    analyzeExcelStructure: (filePath) => ipcRenderer.invoke('analyze-excel-structure', filePath),

    // Process control
    stopProcess: (processType) => ipcRenderer.invoke('stop-process', processType),

    // Utility methods
    isApiAvailable: () => true,

    // Open file in default application
    openFile: (filePath) => ipcRenderer.invoke('open-file', filePath),

    // Check if a file exists
    checkFileExists: (filePath) => ipcRenderer.invoke('check-file-exists', filePath),

    // List files in a directory
    listFiles: (dirPath) => ipcRenderer.invoke('list-files', dirPath),

    // Get the application directory path
    getAppDir: () => ipcRenderer.invoke('get-app-dir'),

    // Save file dialog
    saveFileDialog: (options) => ipcRenderer.invoke('save-file-dialog', options),

    // Copy file
    copyFile: (source, destination) => ipcRenderer.invoke('copy-file', source, destination),

    // Delete file
    deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),

    // Check if file exists
    checkFileExists: (filePath) => ipcRenderer.invoke('check-file-exists', filePath),

    // List files in directory
    listFiles: (dirPath) => ipcRenderer.invoke('list-files', dirPath),

    // Dictionary management
    getDictionaries: () => ipcRenderer.invoke('get-dictionaries'),
    saveDictionaries: (dictionaries) => ipcRenderer.invoke('save-dictionaries', dictionaries),
    resetDictionaries: () => ipcRenderer.invoke('reset-dictionaries'),

    // Enhanced Dictionary management
    getEnhancedDictionary: () => ipcRenderer.invoke('get-enhanced-dictionary'),
    saveEnhancedDictionary: (dictionary) => ipcRenderer.invoke('save-enhanced-dictionary', dictionary),
    resetEnhancedDictionary: () => ipcRenderer.invoke('reset-enhanced-dictionary'),
    importDictionary: () => ipcRenderer.invoke('import-dictionary'),
    exportDictionary: () => ipcRenderer.invoke('export-dictionary'),

    // Listen for backend progress updates
    onBackendProgress: (callback) => {
      ipcRenderer.on('backend-progress', (event, message) => {
        callback(message);
      });
    },

    // Listen for direct progress updates
    onDirectProgressUpdate: (callback) => {
      ipcRenderer.on('direct-progress-update', (event, data) => {
        callback(data);
      });
    },

    // Splash screen API
    onSplashMessage: (callback) => {
      ipcRenderer.on('splash-message', (event, message) => {
        callback(message);
      });
    }
  }
);

// Log when preload script is executed
console.log('Preload script has been loaded');
