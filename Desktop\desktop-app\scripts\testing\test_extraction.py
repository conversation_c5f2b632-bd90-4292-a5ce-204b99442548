"""
Test script to extract data from MAIN1.pdf and print what was extracted
"""

import sys
import os
import json
import re
import PyPDF2

def extract_employee_data(payslip_text):
    """Extract employee data from a single payslip."""
    employee = {
        "employee_id": None,      # Primary identifier (default)
        "ssf_no": None,           # Alternative identifier
        "ghana_card_id": None,    # Alternative identifier
        "name": None,             # Alternative identifier
        "job_title": None,
        "department": None,       # Added for sorting/comparison
        "section": None,          # Added for sorting/comparison
        "basic_salary": None,
        "gross_salary": None,
        "net_pay": None,
        "deductions": {},         # Dictionary for easier comparison
        "earnings": {},           # Dictionary for easier comparison
        "month": None,
        "year": None,
        "loan": None,             # Added for loan information
        "bank_details": {},       # Dictionary for bank details
        "employer_contributions": {},  # Dictionary for employer contributions
    }

    # Extract employee ID
    employee_id_match = re.search(r"([A-Z0-9]+)\s+Employee No", payslip_text)
    if employee_id_match:
        employee["employee_id"] = employee_id_match.group(1).strip()
        print(f"Successfully extracted Employee ID: {employee['employee_id']}")

    # Extract SSF number
    ssf_match = re.search(r"([A-Z0-9]+)\s+SSF No", payslip_text)
    if ssf_match:
        employee["ssf_no"] = ssf_match.group(1).strip()
        print(f"Successfully extracted SSF No: {employee['ssf_no']}")

    # Extract Ghana Card ID
    ghana_card_match = re.search(r"(GHA-\d+-\d+)\s+Ghana Card ID", payslip_text)
    if ghana_card_match:
        employee["ghana_card_id"] = ghana_card_match.group(1).strip()
        print(f"Successfully extracted Ghana Card ID: {employee['ghana_card_id']}")

    # Extract employee name
    name_match = re.search(r"([A-Z\s\-\.]+)\s+Employee Name", payslip_text)
    if name_match:
        employee["name"] = name_match.group(1).strip()
        print(f"Successfully extracted Name: {employee['name']}")

    # Extract job title
    job_title_match = re.search(r"([A-Z_]+)\s+Job Title", payslip_text)
    if job_title_match:
        employee["job_title"] = job_title_match.group(1).strip()
        print(f"Successfully extracted Job Title: {employee['job_title']}")

    # Extract department
    department_match = re.search(r"([A-Z\s\-]+)\s+Department", payslip_text)
    if department_match:
        department = department_match.group(1).strip()
        employee["department"] = department
        print(f"Successfully extracted Department: {department}")

    # Extract section
    section_match = re.search(r"([A-Z\s\-]+)\s+Section", payslip_text)
    if section_match:
        section = section_match.group(1).strip()
        employee["section"] = section
        print(f"Successfully extracted Section: {section}")

    # Extract basic salary
    basic_salary_match = re.search(r"BASIC SALARY\s+([\d,.]+)", payslip_text)
    if basic_salary_match:
        employee["basic_salary"] = basic_salary_match.group(1).strip()
        print(f"Successfully extracted Basic Salary: {employee['basic_salary']}")

    # Extract gross salary
    gross_salary_match = re.search(r"GROSS SALARY\s+([\d,.]+)", payslip_text)
    if gross_salary_match:
        employee["gross_salary"] = gross_salary_match.group(1).strip()
        print(f"Successfully extracted Gross Salary: {employee['gross_salary']}")

    # Extract net pay
    net_pay_match = re.search(r"NET PAY\s+([\d,.]+)", payslip_text)
    if net_pay_match:
        employee["net_pay"] = net_pay_match.group(1).strip()
        print(f"Successfully extracted Net Pay: {employee['net_pay']}")

    # Extract month and year
    period_match = re.search(r"([A-Za-z]+)\s+(\d{4})\s+Period", payslip_text)
    if period_match:
        employee["month"] = period_match.group(1).strip()
        employee["year"] = period_match.group(2).strip()
        print(f"Successfully extracted Period: {employee['month']} {employee['year']}")

    # Extract all earnings dynamically
    # First, try to find the EARNINGS section
    earnings_section = re.search(r"(?:EARNINGS)(?:.*?)(?:DEDUCTIONS|GROSS SALARY|NET PAY|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if earnings_section:
        earnings_text = earnings_section.group(0)
        print(f"Found EARNINGS section: {earnings_text}")
        
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        earnings_items = re.finditer(r"([A-Z][A-Z\s\-\.]+)\s+([\d,.]+)", earnings_text)
        for item in earnings_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()
            
            # Skip section headers
            if item_name == "EARNINGS":
                continue
                
            # Add to earnings dictionary
            employee["earnings"][item_name] = item_value
            print(f"Extracted earning: {item_name} = {item_value}")
    else:
        print("EARNINGS section not found")

    # Extract all deductions dynamically
    # First, try to find the DEDUCTIONS section
    deductions_section = re.search(r"(?:DEDUCTIONS)(?:.*?)(?:NET PAY|EMPLOYER'S CONTRIBUTION|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if deductions_section:
        deductions_text = deductions_section.group(0)
        print(f"Found DEDUCTIONS section: {deductions_text}")
        
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        deduction_items = re.finditer(r"([A-Z][A-Z\s\-\.]+)\s+([\d,.]+)", deductions_text)
        for item in deduction_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()
            
            # Handle NET PAY specially
            if item_name == "NET PAY":
                continue
                
            # Skip section headers
            if item_name == "DEDUCTIONS":
                continue
                
            # Add to deductions dictionary
            employee["deductions"][item_name] = item_value
            print(f"Extracted deduction: {item_name} = {item_value}")
    else:
        print("DEDUCTIONS section not found")

    # Extract all employer contributions dynamically
    # First, try to find the EMPLOYER'S CONTRIBUTION section
    employer_section = re.search(r"(?:EMPLOYER'S CONTRIBUTION|EMPLOYER CONTRIBUTION)(?:.*?)(?:LOAN DEDUCTIONS|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if employer_section:
        employer_text = employer_section.group(0)
        print(f"Found EMPLOYER'S CONTRIBUTION section: {employer_text}")
        
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        employer_items = re.finditer(r"([A-Z][A-Z\s\-\.0-9]+)\s+([\d,.]+)", employer_text)
        for item in employer_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()
            
            # Only skip section headers
            if item_name in ["EMPLOYER'S CONTRIBUTION", "EMPLOYER CONTRIBUTION"]:
                continue
                
            # Add to employer contributions dictionary
            employee["employer_contributions"][item_name] = item_value
            print(f"Extracted employer contribution: {item_name} = {item_value}")
    else:
        print("EMPLOYER'S CONTRIBUTION section not found")

    return employee

def main():
    pdf_path = r'C:\Users\<USER>\Desktop\MAIN1.pdf'
    
    # Extract text from PDF
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        page = reader.pages[0]
        text = page.extract_text()
        
    # Extract employee data
    employee_data = extract_employee_data(text)
    
    # Print the extracted data in a formatted way
    print("\n\n--- EXTRACTED DATA SUMMARY ---")
    print(f"Employee ID: {employee_data['employee_id']}")
    print(f"Name: {employee_data['name']}")
    print(f"Job Title: {employee_data['job_title']}")
    print(f"Department: {employee_data['department']}")
    print(f"Section: {employee_data['section']}")
    print(f"Basic Salary: {employee_data['basic_salary']}")
    print(f"Gross Salary: {employee_data['gross_salary']}")
    print(f"Net Pay: {employee_data['net_pay']}")
    print(f"Period: {employee_data['month']} {employee_data['year']}")
    
    print("\nEarnings:")
    for name, value in employee_data['earnings'].items():
        print(f"  {name}: {value}")
        
    print("\nDeductions:")
    for name, value in employee_data['deductions'].items():
        print(f"  {name}: {value}")
        
    print("\nEmployer Contributions:")
    for name, value in employee_data['employer_contributions'].items():
        print(f"  {name}: {value}")

if __name__ == "__main__":
    main()
