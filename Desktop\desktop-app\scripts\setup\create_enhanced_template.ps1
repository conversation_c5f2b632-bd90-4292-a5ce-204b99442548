$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$workbook = $excel.Workbooks.Add()

# Create Legend worksheet
$legendSheet = $workbook.Worksheets.Item(1)
$legendSheet.Name = 'Format Legend'

# Add legend headers
$legendSheet.Cells.Item(1, 1) = 'SHORTHAND'
$legendSheet.Cells.Item(1, 2) = 'DESCRIPTION'
$legendSheet.Cells.Item(1, 3) = 'EXAMPLE'

# Format legend headers
$range = $legendSheet.Range('A1:C1')
$range.Font.Bold = $true
$range.Interior.ColorIndex = 15

# Set legend column widths
$legendSheet.Columns.Item(1).ColumnWidth = 15
$legendSheet.Columns.Item(2).ColumnWidth = 40
$legendSheet.Columns.Item(3).ColumnWidth = 25

# Add legend data
$row = 2

# Capitalization Patterns
$legendSheet.Cells.Item($row, 1) = '[UC]'
$legendSheet.Cells.Item($row, 2) = 'ALL UPPERCASE'
$legendSheet.Cells.Item($row, 3) = 'EARNINGS'
$row++

$legendSheet.Cells.Item($row, 1) = '[TC]'
$legendSheet.Cells.Item($row, 2) = 'Title Case'
$legendSheet.Cells.Item($row, 3) = 'Employee Name'
$row++

$legendSheet.Cells.Item($row, 1) = '[LC]'
$legendSheet.Cells.Item($row, 2) = 'lowercase'
$legendSheet.Cells.Item($row, 3) = 'employee name'
$row++

$legendSheet.Cells.Item($row, 1) = '[MC]'
$legendSheet.Cells.Item($row, 2) = 'Mixed Case'
$legendSheet.Cells.Item($row, 3) = 'ABUAKWA AREA - Ministers'
$row++

# Special Characters
$legendSheet.Cells.Item($row, 1) = '[.]'
$legendSheet.Cells.Item($row, 2) = 'Contains period'
$legendSheet.Cells.Item($row, 3) = 'Employee No.'
$row++

$legendSheet.Cells.Item($row, 1) = '[-]'
$legendSheet.Cells.Item($row, 2) = 'Contains hyphen'
$legendSheet.Cells.Item($row, 3) = 'APPIAH-AIDOO'
$row++

$legendSheet.Cells.Item($row, 1) = '[_]'
$legendSheet.Cells.Item($row, 2) = 'Contains underscore'
$legendSheet.Cells.Item($row, 3) = 'APOSTLE_'
$row++

$legendSheet.Cells.Item($row, 1) = '[/]'
$legendSheet.Cells.Item($row, 2) = 'Contains slash'
$legendSheet.Cells.Item($row, 3) = 'BALANCE B/F'
$row++

$legendSheet.Cells.Item($row, 1) = '[#]'
$legendSheet.Cells.Item($row, 2) = 'Contains numbers'
$legendSheet.Cells.Item($row, 3) = '2ND FUEL ELEMENT'
$row++

$legendSheet.Cells.Item($row, 1) = "[']"
$legendSheet.Cells.Item($row, 2) = "Contains apostrophe"
$legendSheet.Cells.Item($row, 3) = "Employer's Contributions"
$row++

$legendSheet.Cells.Item($row, 1) = '[()]'
$legendSheet.Cells.Item($row, 2) = 'Contains parentheses'
$legendSheet.Cells.Item($row, 3) = 'AMT (GHS)'
$row++

# Value Format
$legendSheet.Cells.Item($row, 1) = '[N]'
$legendSheet.Cells.Item($row, 2) = 'Numeric only'
$legendSheet.Cells.Item($row, 3) = '123456'
$row++

$legendSheet.Cells.Item($row, 1) = '[C]'
$legendSheet.Cells.Item($row, 2) = 'Currency with symbol'
$legendSheet.Cells.Item($row, 3) = 'GHS 1,234.56'
$row++

$legendSheet.Cells.Item($row, 1) = '[D]'
$legendSheet.Cells.Item($row, 2) = 'Date format'
$legendSheet.Cells.Item($row, 3) = '02-05-25'
$row++

$legendSheet.Cells.Item($row, 1) = '[A]'
$legendSheet.Cells.Item($row, 2) = 'Alphanumeric'
$legendSheet.Cells.Item($row, 3) = 'COP0209'
$row++

$legendSheet.Cells.Item($row, 1) = '[T]'
$legendSheet.Cells.Item($row, 2) = 'Text only'
$legendSheet.Cells.Item($row, 3) = 'ABUAKWA'
$row++

$legendSheet.Cells.Item($row, 1) = '[,]'
$legendSheet.Cells.Item($row, 2) = 'Contains comma'
$legendSheet.Cells.Item($row, 3) = '1,234.56'
$row++

# Create Dictionary worksheet
$dictSheet = $workbook.Worksheets.Add()
$dictSheet.Name = 'Payslip Dictionary'

# Add dictionary headers
$dictSheet.Cells.Item(1, 1) = 'SECTION'
$dictSheet.Cells.Item(1, 2) = 'ITEM'
$dictSheet.Cells.Item(1, 3) = 'FORMAT'
$dictSheet.Cells.Item(1, 4) = 'DESCRIPTION'
$dictSheet.Cells.Item(1, 5) = 'INCLUDE_IN_REPORT'

# Format dictionary headers
$range = $dictSheet.Range('A1:E1')
$range.Font.Bold = $true
$range.Interior.ColorIndex = 15

# Set dictionary column widths
$dictSheet.Columns.Item(1).ColumnWidth = 25
$dictSheet.Columns.Item(2).ColumnWidth = 30
$dictSheet.Columns.Item(3).ColumnWidth = 25
$dictSheet.Columns.Item(4).ColumnWidth = 40
$dictSheet.Columns.Item(5).ColumnWidth = 20

# Add dictionary data
$row = 2

# PERSONAL DETAILS SECTION
$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Employee No.'
$dictSheet.Cells.Item($row, 3) = '[TC][.]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Employee Name'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'SSF No.'
$dictSheet.Cells.Item($row, 3) = '[TC][.]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Ghana Card ID'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Section'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Department'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'PERSONAL DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Job Title'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# EARNINGS SECTION
$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'EARNINGS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'BASIC SALARY'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'EDUCATIONAL SUBSIDY'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'LEAVE ALLOWANCE'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = '2ND FUEL ELEMENT'
$dictSheet.Cells.Item($row, 3) = '[UC][#]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'RENT ELEMENT'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'RESPONSIBILITY HEADS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = '1ST FUEL ELEMENT'
$dictSheet.Cells.Item($row, 3) = '[UC][#]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'GROSS SALARY'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EARNINGS'
$dictSheet.Cells.Item($row, 2) = 'NET PAY'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# DEDUCTIONS SECTION
$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'SSF EEMPLOYEE'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'INCOME TAX'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'LEAVE ALLOWANCE'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'PENT. MIN WELFARE FUND'
$dictSheet.Cells.Item($row, 3) = '[UC][.]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'RENT ELEMENT'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'SCHOLARSHIP FUND (MINISTE'
$dictSheet.Cells.Item($row, 3) = '[UC][()]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'TITHES'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'Loan Deductions'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = "MINISTERS' PENSION - CAT 2"
$dictSheet.Cells.Item($row, 3) = "[UC]['][#][-]"
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'TAXABLE SALARY'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'DEDUCTIONS'
$dictSheet.Cells.Item($row, 2) = 'TOTAL DEDUCTIONS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# EMPLOYERS CONTRIBUTION SECTION
$dictSheet.Cells.Item($row, 1) = 'EMPLOYERS CONTRIBUTION'
$dictSheet.Cells.Item($row, 2) = "Employer's Contributions"
$dictSheet.Cells.Item($row, 3) = "[TC][']"
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EMPLOYERS CONTRIBUTION'
$dictSheet.Cells.Item($row, 2) = 'SSF EMPLOYER'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EMPLOYERS CONTRIBUTION'
$dictSheet.Cells.Item($row, 2) = 'SAVING SCHEME (EMPLOYER)'
$dictSheet.Cells.Item($row, 3) = '[UC][()]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# LOANS SECTION
$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'LOANS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'LOAN'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'BALANCE B/F'
$dictSheet.Cells.Item($row, 3) = '[UC][/]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'CURRENT DEDUCTION'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'OUST. BALANCE'
$dictSheet.Cells.Item($row, 3) = '[UC][.]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'LOANS'
$dictSheet.Cells.Item($row, 2) = 'SALARY ADVANCE-MINS'
$dictSheet.Cells.Item($row, 3) = '[UC][-]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# EMPLOYEE BANK DETAILS SECTION
$dictSheet.Cells.Item($row, 1) = 'EMPLOYEE BANK DETAILS'
$dictSheet.Cells.Item($row, 2) = 'EMPLOYEE BANK DETAILS'
$dictSheet.Cells.Item($row, 3) = '[UC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EMPLOYEE BANK DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Bank'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EMPLOYEE BANK DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Account No.'
$dictSheet.Cells.Item($row, 3) = '[TC][.]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

$dictSheet.Cells.Item($row, 1) = 'EMPLOYEE BANK DETAILS'
$dictSheet.Cells.Item($row, 2) = 'Branch'
$dictSheet.Cells.Item($row, 3) = '[TC]'
$dictSheet.Cells.Item($row, 5) = 'YES'
$row++

# Save the workbook
$workbook.SaveAs("C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx")
$excel.Quit()

# Clean up COM objects
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($legendSheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($dictSheet) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($workbook) | Out-Null
[System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
[System.GC]::Collect()
[System.GC]::WaitForPendingFinalizers()

Write-Host "Enhanced Excel template created successfully at C:\Users\<USER>\Desktop\Payslip_Dictionary_Template.xlsx"
