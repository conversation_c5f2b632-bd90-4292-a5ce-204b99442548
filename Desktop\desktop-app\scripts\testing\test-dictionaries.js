// Test script for dictionaries
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the script path
const scriptPath = path.join(__dirname, 'backend-dist', 'payroll_dictionaries.py');

// Check if the script exists
if (!fs.existsSync(scriptPath)) {
  console.error(`Dictionary script not found: ${scriptPath}`);
  process.exit(1);
}

// Run the script to get the dictionaries
const process = spawn('python', ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import payroll_dictionaries
import json

# Get the dictionaries
earnings = payroll_dictionaries.get_earnings_dictionary()
deductions = payroll_dictionaries.get_deductions_dictionary()
loans = payroll_dictionaries.get_loans_dictionary()

# Print the dictionaries as JSON
print(json.dumps({
    'earnings': earnings,
    'deductions': deductions,
    'loans': loans
}))
`]);

let output = '';

process.stdout.on('data', (data) => {
  output += data.toString();
});

process.stderr.on('data', (data) => {
  console.error(`Dictionary error: ${data.toString()}`);
});

process.on('close', (code) => {
  if (code === 0) {
    try {
      const dictionaries = JSON.parse(output);
      console.log('Dictionaries loaded successfully:');
      console.log('Earnings:', Object.keys(dictionaries.earnings));
      console.log('Deductions:', Object.keys(dictionaries.deductions));
      console.log('Loans:', Object.keys(dictionaries.loans));
    } catch (error) {
      console.error('Error parsing dictionary output:', error);
    }
  } else {
    console.error(`Dictionary process exited with code ${code}`);
  }
});
