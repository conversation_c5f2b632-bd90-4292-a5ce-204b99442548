const { app, BrowserWindow, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// Enable detailed logging
process.env.ELECTRON_ENABLE_LOGGING = true;
process.env.ELECTRON_ENABLE_STACK_DUMPING = true;

// Keep a global reference of the window object to prevent garbage collection
let mainWindow;

function createWindow() {
  console.log('Creating main window...');
  
  try {
    // Create the browser window
    mainWindow = new BrowserWindow({
      width: 1024,
      height: 768,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      show: false // Don't show the window until it's ready
    });

    console.log('Main window created successfully');
    
    // Check if index.html exists
    const indexPath = path.join(__dirname, 'index.html');
    console.log(`Checking if index.html exists at: ${indexPath}`);
    
    if (fs.existsSync(indexPath)) {
      console.log('index.html found, loading file...');
      
      // Load the index.html file
      mainWindow.loadFile(indexPath);
      
      // Open DevTools
      mainWindow.webContents.openDevTools();
      
      // Show window when ready
      mainWindow.once('ready-to-show', () => {
        console.log('Window ready to show');
        mainWindow.show();
      });
      
      // Log any load errors
      mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error(`Failed to load: ${errorDescription} (${errorCode})`);
        dialog.showErrorBox('Load Error', `Failed to load: ${errorDescription}`);
      });
    } else {
      console.error('index.html not found!');
      dialog.showErrorBox('File Not Found', `Could not find index.html at ${indexPath}`);
      app.quit();
    }

    // Emitted when the window is closed
    mainWindow.on('closed', function () {
      console.log('Main window closed');
      mainWindow = null;
    });
  } catch (error) {
    console.error('Error creating window:', error);
    dialog.showErrorBox('Error', `Failed to create window: ${error.message}`);
    app.quit();
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  console.log('Electron app is ready');
  createWindow();

  app.on('activate', function () {
    // On macOS it's common to re-create a window when the dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
}).catch(error => {
  console.error('Failed to initialize app:', error);
  dialog.showErrorBox('Initialization Error', `Failed to initialize app: ${error.message}`);
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', function () {
  console.log('All windows closed');
  if (process.platform !== 'darwin') app.quit();
});

// Log any unhandled exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  dialog.showErrorBox('Uncaught Exception', `An error occurred: ${error.message}`);
});
