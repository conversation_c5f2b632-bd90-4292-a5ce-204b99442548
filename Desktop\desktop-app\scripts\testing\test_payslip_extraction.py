import os
import sys
import json
import re
import logging
import pandas as pd

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('PayslipExtractor')

# Print immediate feedback
print("Script started. Testing payslip data extraction for Bank Adviser...")

class PayslipExtractor:
    """
    Class for testing extraction of data from payslip PDFs for Bank Adviser.
    """

    def __init__(self):
        """Initialize the PayslipExtractor class."""
        self.payslip_data = {}
        self.sections_found = set()
        self.section_data_counts = {}

    def extract_payslip_data(self, payslip_files):
        """
        Extract data from payslip PDFs using the improved parser.

        Args:
            payslip_files (list): List of paths to payslip PDF files
        """
        logger.info(f"Extracting data from {len(payslip_files)} payslip files")
        print(f"Extracting data from {len(payslip_files)} payslip files")

        # Initialize payslip data dictionary
        self.payslip_data = {}
        self.sections_found = set()
        self.section_data_counts = {}

        # If no payslip files, return early
        if not payslip_files:
            logger.warning("No payslip files provided")
            print("No payslip files provided")
            return

        try:
            # Try to import the improved parser
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            try:
                # First try to import from the current directory
                print("Trying to import improved_payroll_parser from current directory...")
                from improved_payroll_parser import extract_payslip_data, load_dictionaries
                print("Successfully imported improved_payroll_parser")
            except ImportError as e:
                print(f"Failed to import improved_payroll_parser from current directory: {str(e)}")
                
                # Try to import from backend-dist
                try:
                    print("Trying to import improved_payroll_parser from backend-dist...")
                    sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend-dist'))
                    from improved_payroll_parser import extract_payslip_data, load_dictionaries
                    print("Successfully imported improved_payroll_parser from backend-dist")
                except ImportError as e:
                    print(f"Failed to import improved_payroll_parser from backend-dist: {str(e)}")
                    print("Falling back to direct PDF extraction")
                    extract_payslip_data = None
                    load_dictionaries = None

            # If we have the parser, use it
            if extract_payslip_data and load_dictionaries:
                # Load dictionaries for standardization
                try:
                    dictionaries = load_dictionaries()
                    print("Successfully loaded dictionaries")
                except Exception as e:
                    print(f"Failed to load dictionaries: {str(e)}")
                    dictionaries = {}

                # Process each payslip file
                for payslip_file in payslip_files:
                    try:
                        print(f"Processing payslip file: {payslip_file}")

                        # Use the existing extraction engine to extract data
                        payslip_data = extract_payslip_data(payslip_file, dictionaries)
                        
                        # Print the extracted sections
                        print(f"Extracted sections: {list(payslip_data.keys())}")
                        
                        # Update sections found
                        for section in payslip_data.keys():
                            self.sections_found.add(section)
                            
                            # Count items in each section
                            if section not in self.section_data_counts:
                                self.section_data_counts[section] = 0
                            self.section_data_counts[section] += len(payslip_data[section])

                        # Extract the employee number from personal details
                        employee_no = None
                        personal_details = payslip_data.get('PERSONAL DETAILS', {})

                        # Try different possible field names for employee number
                        for field in ['EMPLOYEE NO.', 'EMPLOYEE NUMBER', 'STAFF ID', 'ID', 'EMPLOYEE ID', 'STAFF NUMBER']:
                            if field in personal_details:
                                employee_no = personal_details[field]
                                # Clean up the employee number (remove spaces, etc.)
                                if employee_no:
                                    employee_no = re.sub(r'\s+', '', str(employee_no))
                                break

                        # If not found in personal details, try to extract from any section
                        if not employee_no:
                            for section_name, section_data in payslip_data.items():
                                for field_name, field_value in section_data.items():
                                    if any(keyword in field_name.upper() for keyword in ['EMPLOYEE', 'STAFF', 'ID', 'NO']):
                                        # This might be an employee number field
                                        potential_id = str(field_value)
                                        # Check if it matches the pattern of an employee ID
                                        if re.match(r'^[A-Z]*\d+$', potential_id.strip()):
                                            employee_no = potential_id.strip()
                                            break
                                if employee_no:
                                    break

                        # If still not found, try to extract from filename
                        if not employee_no:
                            filename = os.path.basename(payslip_file)
                            # Look for patterns like COP1234 or just numbers
                            match = re.search(r'([A-Z]+\d+|\d+)', filename)
                            if match:
                                employee_no = match.group(1)

                        # Log the extracted employee number
                        if employee_no:
                            print(f"Extracted employee number: {employee_no} from {payslip_file}")
                        else:
                            print(f"Could not extract employee number from {payslip_file}")

                        # Extract net pay from earnings section
                        net_pay = 0.0

                        # First try to find net pay in the earnings section
                        earnings = payslip_data.get('EARNINGS', {})

                        # Try different possible field names for net pay
                        net_pay_fields = ['NET PAY', 'NETPAY', 'NET SALARY', 'TAKE HOME', 'NETT PAY', 'TOTAL NET PAY']
                        for field in net_pay_fields:
                            if field in earnings:
                                try:
                                    net_pay_str = str(earnings[field])
                                    # Remove currency symbols, commas, and other non-numeric characters
                                    net_pay_str = re.sub(r'[^\d.]', '', net_pay_str)
                                    if net_pay_str:
                                        net_pay = float(net_pay_str)
                                        print(f"Found net pay in earnings section: {field} = {net_pay}")
                                        break
                                except (ValueError, TypeError):
                                    print(f"Could not convert net pay value to float: {earnings[field]}")

                        # If not found in earnings, try other sections
                        if net_pay == 0.0:
                            print(f"Net pay not found in earnings section for employee {employee_no}, trying other sections")
                            for section_name, section_data in payslip_data.items():
                                print(f"Checking section {section_name} for net pay")
                                for field_name, field_value in section_data.items():
                                    if any(keyword in field_name.upper() for keyword in ['NET PAY', 'NETPAY', 'NET SALARY', 'TAKE HOME']):
                                        print(f"Found potential net pay field: {field_name} = {field_value}")
                                        try:
                                            net_pay_str = str(field_value)
                                            # Remove currency symbols, commas, and other non-numeric characters
                                            net_pay_str = re.sub(r'[^\d.]', '', net_pay_str)
                                            if net_pay_str:
                                                net_pay = float(net_pay_str)
                                                print(f"Found net pay in {section_name} section: {field_name} = {net_pay}")
                                                break
                                        except (ValueError, TypeError):
                                            print(f"Could not convert net pay value to float: {field_value}")
                                if net_pay > 0:
                                    break

                        # Log the extracted net pay
                        if net_pay > 0:
                            print(f"Extracted net pay: {net_pay} for employee {employee_no}")
                        else:
                            print(f"Could not extract net pay for employee {employee_no}")

                        # Store the data if employee number is found
                        if employee_no:
                            self.payslip_data[employee_no] = {
                                'NET PAY': net_pay,
                                'EMPLOYEE NO.': employee_no,
                                'SECTIONS': list(payslip_data.keys())
                            }
                            print(f"Extracted data for employee {employee_no}: NET PAY = {net_pay}")
                        else:
                            print(f"Could not extract employee number from {payslip_file}")

                    except Exception as e:
                        print(f"Error extracting data from {payslip_file}: {str(e)}")
                        import traceback
                        print(traceback.format_exc())
                        continue
            else:
                # Direct PDF extraction fallback
                print("Using direct PDF extraction fallback")
                self._extract_with_direct_method(payslip_files)

        except Exception as e:
            print(f"Error initializing payslip extraction: {str(e)}")
            import traceback
            print(traceback.format_exc())

        print(f"Extracted data for {len(self.payslip_data)} employees from payslip files")
        
        # Print section statistics
        print("\nSections found in payslips:")
        for section in self.sections_found:
            count = self.section_data_counts.get(section, 0)
            print(f"  {section}: {count} data items")
        
        # Print the extracted data for debugging
        if self.payslip_data:
            print("\nSample of extracted data:")
            for i, (employee_no, data) in enumerate(list(self.payslip_data.items())[:5]):
                print(f"  Employee {employee_no}: {data}")
            
            if len(self.payslip_data) > 5:
                print(f"  ... and {len(self.payslip_data) - 5} more entries")
        else:
            print("No data was extracted!")

    def _extract_with_direct_method(self, payslip_files):
        """
        Extract data from payslip PDFs using direct PDF extraction.
        
        Args:
            payslip_files (list): List of paths to payslip PDF files
        """
        print("Direct PDF extraction not implemented in this test script")
        print("This would be a fallback method if the improved parser is not available")
        
    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.
        
        Args:
            output_file (str): Path to the output Excel file
        """
        try:
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.payslip_data.items():
                data_list.append(data)
                
            df = pd.DataFrame(data_list)
            
            # Save to Excel
            df.to_excel(output_file, index=False)
            
            print(f"Successfully saved data to Excel file: {output_file}")
            print(f"Total records saved: {len(df)}")
            
        except Exception as e:
            print(f"Error saving data to Excel file: {str(e)}")
            import traceback
            print(traceback.format_exc())

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_payslip_extraction.py <payslip_pdf_file1> [payslip_pdf_file2 ...] [output_excel_file]")
        sys.exit(1)
    
    # Get payslip files and optional output file
    payslip_files = sys.argv[1:-1] if len(sys.argv) > 2 else [sys.argv[1]]
    
    # Default output file is payslip_extract.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "payslip_extract.xlsx")
    
    # If last argument ends with .xlsx, use it as output file
    if sys.argv[-1].endswith('.xlsx'):
        output_file = sys.argv[-1]
        payslip_files = sys.argv[1:-1]
    
    print(f"Payslip files: {payslip_files}")
    print(f"Output file: {output_file}")
    
    extractor = PayslipExtractor()
    extractor.extract_payslip_data(payslip_files)
    
    # Save to Excel if data was extracted
    if extractor.payslip_data:
        extractor.save_to_excel(output_file)
    else:
        print("No data to save to Excel.")

if __name__ == "__main__":
    main()
