#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate sample payroll PDF files for testing the Templar Payroll Auditor.
"""

import os
import sys
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors

def generate_sample_payroll(output_path, is_current=False):
    """Generate a sample payroll PDF file."""
    doc = SimpleDocTemplate(output_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # Create content
    content = []
    
    # Title
    title = Paragraph(f"TEMPLAR SYSTEMS PAYROLL - {'CURRENT' if is_current else 'PREVIOUS'}", styles['Title'])
    content.append(title)
    content.append(Spacer(1, 12))
    
    # Date
    date = Paragraph(f"Pay Period: {'May 1-15, 2025' if is_current else 'April 16-30, 2025'}", styles['Normal'])
    content.append(date)
    content.append(Spacer(1, 12))
    
    # Employee 1
    content.append(Paragraph("Employee: ID: EMP001 - <PERSON>", styles['Heading2']))
    content.append(Spacer(1, 6))
    
    # Employee 1 details
    if is_current:
        emp1_data = [
            ["Gross Pay", "$5,200.00"],
            ["Federal Tax", "$780.00"],
            ["State Tax", "$260.00"],
            ["Social Security", "$322.40"],
            ["Medicare", "$75.40"],
            ["Health Insurance", "$150.00"],
            ["401(k)", "$260.00"],
            ["Net Pay", "$3,352.20"]
        ]
    else:
        emp1_data = [
            ["Gross Pay", "$5,000.00"],
            ["Federal Tax", "$750.00"],
            ["State Tax", "$250.00"],
            ["Social Security", "$310.00"],
            ["Medicare", "$72.50"],
            ["Health Insurance", "$150.00"],
            ["401(k)", "$250.00"],
            ["Net Pay", "$3,217.50"]
        ]
    
    emp1_table = Table(emp1_data, colWidths=[200, 100])
    emp1_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
    ]))
    content.append(emp1_table)
    content.append(Spacer(1, 20))
    
    # Employee 2
    content.append(Paragraph("Employee: ID: EMP002 - Jane Doe", styles['Heading2']))
    content.append(Spacer(1, 6))
    
    # Employee 2 details
    if is_current:
        emp2_data = [
            ["Gross Pay", "$6,500.00"],
            ["Federal Tax", "$975.00"],
            ["State Tax", "$325.00"],
            ["Social Security", "$403.00"],
            ["Medicare", "$94.25"],
            ["Health Insurance", "$200.00"],
            ["401(k)", "$325.00"],
            ["Dental Insurance", "$50.00"],  # New deduction in current payroll
            ["Net Pay", "$4,127.75"]
        ]
    else:
        emp2_data = [
            ["Gross Pay", "$6,500.00"],
            ["Federal Tax", "$975.00"],
            ["State Tax", "$325.00"],
            ["Social Security", "$403.00"],
            ["Medicare", "$94.25"],
            ["Health Insurance", "$200.00"],
            ["401(k)", "$325.00"],
            ["Net Pay", "$4,177.75"]
        ]
    
    emp2_table = Table(emp2_data, colWidths=[200, 100])
    emp2_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
    ]))
    content.append(emp2_table)
    content.append(Spacer(1, 20))
    
    # Employee 3 (only in previous payroll)
    if not is_current:
        content.append(Paragraph("Employee: ID: EMP003 - Robert Johnson", styles['Heading2']))
        content.append(Spacer(1, 6))
        
        emp3_data = [
            ["Gross Pay", "$4,800.00"],
            ["Federal Tax", "$720.00"],
            ["State Tax", "$240.00"],
            ["Social Security", "$297.60"],
            ["Medicare", "$69.60"],
            ["Health Insurance", "$150.00"],
            ["401(k)", "$240.00"],
            ["Net Pay", "$3,082.80"]
        ]
        
        emp3_table = Table(emp3_data, colWidths=[200, 100])
        emp3_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ]))
        content.append(emp3_table)
        content.append(Spacer(1, 20))
    
    # Employee 4 (only in current payroll)
    if is_current:
        content.append(Paragraph("Employee: ID: EMP004 - Sarah Williams", styles['Heading2']))
        content.append(Spacer(1, 6))
        
        emp4_data = [
            ["Gross Pay", "$5,800.00"],
            ["Federal Tax", "$870.00"],
            ["State Tax", "$290.00"],
            ["Social Security", "$359.60"],
            ["Medicare", "$84.10"],
            ["Health Insurance", "$180.00"],
            ["401(k)", "$290.00"],
            ["Net Pay", "$3,726.30"]
        ]
        
        emp4_table = Table(emp4_data, colWidths=[200, 100])
        emp4_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ]))
        content.append(emp4_table)
        content.append(Spacer(1, 20))
    
    # Footer
    footer = Paragraph("TEMPLAR SYSTEMS CONFIDENTIAL", styles['Normal'])
    content.append(footer)
    
    # Build the PDF
    doc.build(content)
    
    return output_path

def main():
    """Main function to generate sample payroll PDFs."""
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Generate previous payroll PDF
    prev_path = os.path.join(output_dir, "previous_payroll_sample.pdf")
    generate_sample_payroll(prev_path, is_current=False)
    print(f"Generated previous payroll sample: {prev_path}")
    
    # Generate current payroll PDF
    curr_path = os.path.join(output_dir, "current_payroll_sample.pdf")
    generate_sample_payroll(curr_path, is_current=True)
    print(f"Generated current payroll sample: {curr_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
