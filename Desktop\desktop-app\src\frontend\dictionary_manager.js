// Dictionary Manager JavaScript

// Global dictionary object
let dictionary = {};
let currentSection = "PERSONAL DETAILS";
let currentItem = null;

// DOM Elements
const dictionaryContent = document.getElementById('dictionary-content');
const importDictionaryBtn = document.getElementById('import-dictionary-btn');
const exportDictionaryBtn = document.getElementById('export-dictionary-btn');
const resetDictionaryBtn = document.getElementById('reset-dictionary-btn');
const saveDictionaryBtn = document.getElementById('save-dictionary-btn');

// Tab elements
const personalDetailsTab = document.getElementById('personal-details-tab');
const earningsTab = document.getElementById('earnings-tab');
const deductionsTab = document.getElementById('deductions-tab');
const employersContributionTab = document.getElementById('employers-contribution-tab');
const loansTab = document.getElementById('loans-tab');
const bankDetailsTab = document.getElementById('bank-details-tab');
const addSectionTab = document.getElementById('add-section-tab');

// Content elements
const personalDetailsContent = document.getElementById('personal-details-content');
const earningsContent = document.getElementById('earnings-content');
const deductionsContent = document.getElementById('deductions-content');
const employersContributionContent = document.getElementById('employers-contribution-content');
const loansContent = document.getElementById('loans-content');
const bankDetailsContent = document.getElementById('bank-details-content');
const addSectionContent = document.getElementById('add-section-content');

// Item lists
const personalDetailsItems = document.getElementById('personal-details-items');
const earningsItems = document.getElementById('earnings-items');
const deductionsItems = document.getElementById('deductions-items');
const employersContributionItems = document.getElementById('employers-contribution-items');
const loansItems = document.getElementById('loans-items');
const bankDetailsItems = document.getElementById('bank-details-items');

// Add item buttons
const addPersonalDetailsItemBtn = document.getElementById('add-personal-details-item-btn');
const addEarningsItemBtn = document.getElementById('add-earnings-item-btn');
const addDeductionsItemBtn = document.getElementById('add-deductions-item-btn');
const addEmployersContributionItemBtn = document.getElementById('add-employers-contribution-item-btn');
const addLoansItemBtn = document.getElementById('add-loans-item-btn');
const addBankDetailsItemBtn = document.getElementById('add-bank-details-item-btn');
const createSectionBtn = document.getElementById('create-section-btn');

// Modal elements
const itemEditModal = document.getElementById('item-edit-modal');
const editModalTitle = document.getElementById('edit-modal-title');
const editItemName = document.getElementById('edit-item-name');
const editItemFormat = document.getElementById('edit-item-format');
const editItemValueFormat = document.getElementById('edit-item-value-format');
const editItemStandardizedName = document.getElementById('edit-item-standardized-name');
const editItemVariations = document.getElementById('edit-item-variations');
const editItemIncludeInReport = document.getElementById('edit-item-include-in-report');
const saveItemBtn = document.getElementById('save-item-btn');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeModal = document.querySelector('.close-modal');
const formatHelperBtn = document.getElementById('format-helper-btn');
const formatHelperContent = document.getElementById('format-helper-content');

// Initialize the dictionary manager
function initDictionaryManager() {
  // Load the dictionary
  loadDictionary();

  // Set up event listeners
  setupEventListeners();
}

// Load the dictionary from the backend
async function loadDictionary() {
  try {
    // Call the backend to get the dictionary
    const dictionaryData = await window.api.getEnhancedDictionary();

    if (dictionaryData) {
      dictionary = dictionaryData;
    } else {
      console.error('Failed to load dictionary');
      dictionary = {};
    }

    // Render the dictionary
    renderDictionary();
  } catch (error) {
    console.error('Error loading dictionary:', error);
  }
}

// Save the dictionary to the backend
async function saveDictionary() {
  try {
    // Call the backend to save the dictionary
    const success = await window.api.saveEnhancedDictionary(dictionary);

    if (success) {
      showNotification('Dictionary saved successfully', 'success');

      // Refresh the dictionary to ensure all changes are reflected
      loadDictionary();

      // Emit an event to notify other parts of the application that the dictionary has been updated
      if (window.appEvents) {
        window.appEvents.emit('dictionaryUpdated', dictionary);
      }
    } else {
      showNotification('Failed to save dictionary', 'error');
    }
  } catch (error) {
    console.error('Error saving dictionary:', error);
    showNotification('Error saving dictionary: ' + error.message, 'error');
  }
}

// Reset the dictionary to defaults
async function resetDictionary() {
  if (confirm('Are you sure you want to reset the dictionary to defaults? This will remove all custom items.')) {
    try {
      // Call the backend to reset the dictionary
      const success = await window.api.resetEnhancedDictionary();

      if (success) {
        showNotification('Dictionary reset to defaults', 'success');
        await loadDictionary(); // Reload the dictionary

        // Emit an event to notify other parts of the application that the dictionary has been updated
        if (window.appEvents) {
          window.appEvents.emit('dictionaryUpdated', dictionary);
        }
      } else {
        showNotification('Failed to reset dictionary', 'error');
      }
    } catch (error) {
      console.error('Error resetting dictionary:', error);
      showNotification('Error resetting dictionary: ' + error.message, 'error');
    }
  }
}

// Import dictionary from Excel
async function importDictionary() {
  try {
    // Call the backend to import the dictionary
    const result = await window.api.importDictionary();

    if (result.success) {
      showNotification(`Dictionary imported successfully. ${result.itemCount} items imported.`, 'success');
      await loadDictionary(); // Reload the dictionary

      // Emit an event to notify other parts of the application that the dictionary has been updated
      if (window.appEvents) {
        window.appEvents.emit('dictionaryUpdated', dictionary);
      }
    } else {
      showNotification('Failed to import dictionary: ' + result.error, 'error');
    }
  } catch (error) {
    console.error('Error importing dictionary:', error);
    showNotification('Error importing dictionary: ' + error.message, 'error');
  }
}

// Export dictionary to Excel
async function exportDictionary() {
  try {
    // Call the backend to export the dictionary
    const success = await window.api.exportDictionary();

    if (success) {
      showNotification('Dictionary exported successfully', 'success');
    } else {
      showNotification('Failed to export dictionary', 'error');
    }
  } catch (error) {
    console.error('Error exporting dictionary:', error);
    showNotification('Error exporting dictionary: ' + error.message, 'error');
  }
}

// Render the dictionary
function renderDictionary() {
  // Clear all item lists
  personalDetailsItems.innerHTML = '';
  earningsItems.innerHTML = '';
  deductionsItems.innerHTML = '';
  employersContributionItems.innerHTML = '';
  loansItems.innerHTML = '';
  bankDetailsItems.innerHTML = '';

  // Render each section
  renderSection('PERSONAL DETAILS', personalDetailsItems);
  renderSection('EARNINGS', earningsItems);
  renderSection('DEDUCTIONS', deductionsItems);
  renderSection('EMPLOYERS CONTRIBUTION', employersContributionItems);
  renderSection('LOANS', loansItems);
  renderSection('EMPLOYEE BANK DETAILS', bankDetailsItems);
}

// Render a specific section
function renderSection(sectionName, container) {
  if (!dictionary[sectionName] || !dictionary[sectionName].items) {
    return;
  }

  const items = dictionary[sectionName].items;

  for (const [itemName, itemData] of Object.entries(items)) {
    const row = document.createElement('tr');

    // Item name cell
    const nameCell = document.createElement('td');
    nameCell.textContent = itemName;

    // Add standardized name as a tooltip if different from item name
    const standardizedName = itemData.standardized_name || itemName;
    if (standardizedName !== itemName) {
      nameCell.title = `Standardized as: ${standardizedName}`;
      nameCell.style.textDecoration = 'underline dotted';
      nameCell.style.cursor = 'help';
    }

    // Add variations as a tooltip if any exist
    const variations = itemData.variations || [];
    if (variations.length > 0) {
      const variationsText = variations.join(', ');
      if (nameCell.title) {
        nameCell.title += `\nVariations: ${variationsText}`;
      } else {
        nameCell.title = `Variations: ${variationsText}`;
        nameCell.style.textDecoration = 'underline dotted';
        nameCell.style.cursor = 'help';
      }
    }

    row.appendChild(nameCell);

    // Format cell
    const formatCell = document.createElement('td');
    formatCell.textContent = itemData.format || '';
    row.appendChild(formatCell);

    // Value format cell
    const valueFormatCell = document.createElement('td');
    valueFormatCell.textContent = itemData.value_format || '';
    row.appendChild(valueFormatCell);

    // Include in report cell
    const includeCell = document.createElement('td');

    // Create a toggle switch instead of a checkbox
    const toggleContainer = document.createElement('label');
    toggleContainer.className = 'toggle-switch';

    const includeCheckbox = document.createElement('input');
    includeCheckbox.type = 'checkbox';
    includeCheckbox.checked = itemData.include_in_report || false;
    includeCheckbox.className = 'toggle-input';

    // Add event listener to update the dictionary when toggled
    includeCheckbox.addEventListener('change', (event) => {
      // Update the dictionary
      dictionary[sectionName].items[itemName].include_in_report = event.target.checked;

      // Show notification
      const status = event.target.checked ? 'included in' : 'excluded from';
      showNotification(`"${itemName}" will be ${status} reports`, 'success');
    });

    // Create the slider element
    const slider = document.createElement('span');
    slider.className = 'toggle-slider';

    // Assemble the toggle
    toggleContainer.appendChild(includeCheckbox);
    toggleContainer.appendChild(slider);
    includeCell.appendChild(toggleContainer);
    row.appendChild(includeCell);

    // Actions cell
    const actionsCell = document.createElement('td');
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'item-actions';

    // Edit button
    const editButton = document.createElement('button');
    editButton.className = 'edit-button';
    editButton.innerHTML = '<i class="fas fa-edit"></i>';
    editButton.addEventListener('click', () => openEditModal(sectionName, itemName));
    actionsDiv.appendChild(editButton);

    // Delete button
    const deleteButton = document.createElement('button');
    deleteButton.className = 'delete-button';
    deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
    deleteButton.addEventListener('click', () => deleteItem(sectionName, itemName));
    actionsDiv.appendChild(deleteButton);

    actionsCell.appendChild(actionsDiv);
    row.appendChild(actionsCell);

    container.appendChild(row);
  }
}

// Open the edit modal for an item
function openEditModal(sectionName, itemName) {
  currentSection = sectionName;
  currentItem = itemName;

  // Set the modal title
  editModalTitle.textContent = itemName ? 'Edit Item' : 'Add New Item';

  // Populate the form if editing an existing item
  if (itemName && dictionary[sectionName] && dictionary[sectionName].items[itemName]) {
    const itemData = dictionary[sectionName].items[itemName];
    editItemName.value = itemName;
    editItemFormat.value = itemData.format || '';
    editItemValueFormat.value = itemData.value_format || '';
    editItemIncludeInReport.checked = itemData.include_in_report || false;
    editItemStandardizedName.value = itemData.standardized_name || itemName;
    editItemVariations.value = (itemData.variations || []).join(', ');
  } else {
    // Clear the form if adding a new item
    editItemName.value = '';
    editItemFormat.value = '';
    editItemValueFormat.value = '';
    editItemIncludeInReport.checked = true;
    editItemStandardizedName.value = '';
    editItemVariations.value = '';
  }

  // Show the modal
  itemEditModal.style.display = 'block';
}

// Save the item from the edit modal
function saveItem() {
  const itemName = editItemName.value.trim();
  const format = editItemFormat.value.trim();
  const valueFormat = editItemValueFormat.value;
  const includeInReport = editItemIncludeInReport.checked;
  const standardizedName = editItemStandardizedName.value.trim() || itemName;
  const variationsInput = editItemVariations.value.trim();
  const variations = variationsInput ? variationsInput.split(',').map(v => v.trim()) : [];

  // Validate inputs
  if (!itemName) {
    showNotification('Item name is required', 'error');
    return;
  }

  if (!format) {
    showNotification('Format is required', 'error');
    return;
  }

  // Ensure the section exists in the dictionary
  if (!dictionary[currentSection]) {
    dictionary[currentSection] = { items: {} };
  }

  // Add or update the item
  dictionary[currentSection].items[itemName] = {
    format: format,
    value_format: valueFormat,
    include_in_report: includeInReport,
    standardized_name: standardizedName,
    variations: variations
  };

  // If the item was renamed, remove the old item
  if (currentItem && currentItem !== itemName) {
    delete dictionary[currentSection].items[currentItem];
  }

  // Close the modal
  itemEditModal.style.display = 'none';

  // Re-render the dictionary
  renderDictionary();

  // Show success notification
  showNotification(`Item "${itemName}" saved successfully`, 'success');
}

// Delete an item
function deleteItem(sectionName, itemName) {
  if (confirm(`Are you sure you want to delete "${itemName}" from the ${sectionName} section?`)) {
    if (dictionary[sectionName] && dictionary[sectionName].items[itemName]) {
      delete dictionary[sectionName].items[itemName];
      renderDictionary();
      showNotification(`Item "${itemName}" deleted successfully`, 'success');
    }
  }
}

// Create a new section
function createSection() {
  const sectionName = document.getElementById('new-section-name').value.trim();

  if (!sectionName) {
    showNotification('Section name is required', 'error');
    return;
  }

  if (dictionary[sectionName]) {
    showNotification(`Section "${sectionName}" already exists`, 'error');
    return;
  }

  // Add the new section
  dictionary[sectionName] = { items: {} };

  // Clear the input
  document.getElementById('new-section-name').value = '';

  // Show success notification
  showNotification(`Section "${sectionName}" created successfully`, 'success');

  // TODO: Add a new tab for the section
  // This would require dynamically creating tab elements
}

// Show a notification
function showNotification(message, type) {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;

  // Add to the document
  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.remove();
  }, 3000);
}

// Set up event listeners
function setupEventListeners() {
  // Tab buttons
  personalDetailsTab.addEventListener('click', () => showTab(personalDetailsContent));
  earningsTab.addEventListener('click', () => showTab(earningsContent));
  deductionsTab.addEventListener('click', () => showTab(deductionsContent));
  employersContributionTab.addEventListener('click', () => showTab(employersContributionContent));
  loansTab.addEventListener('click', () => showTab(loansContent));
  bankDetailsTab.addEventListener('click', () => showTab(bankDetailsContent));
  addSectionTab.addEventListener('click', () => showTab(addSectionContent));

  // Add item buttons
  addPersonalDetailsItemBtn.addEventListener('click', () => openEditModal('PERSONAL DETAILS'));
  addEarningsItemBtn.addEventListener('click', () => openEditModal('EARNINGS'));
  addDeductionsItemBtn.addEventListener('click', () => openEditModal('DEDUCTIONS'));
  addEmployersContributionItemBtn.addEventListener('click', () => openEditModal('EMPLOYERS CONTRIBUTION'));
  addLoansItemBtn.addEventListener('click', () => openEditModal('LOANS'));
  addBankDetailsItemBtn.addEventListener('click', () => openEditModal('EMPLOYEE BANK DETAILS'));

  // Create section button
  createSectionBtn.addEventListener('click', createSection);

  // Modal buttons
  saveItemBtn.addEventListener('click', saveItem);
  cancelEditBtn.addEventListener('click', () => itemEditModal.style.display = 'none');
  closeModal.addEventListener('click', () => itemEditModal.style.display = 'none');

  // Format helper
  formatHelperBtn.addEventListener('click', () => {
    formatHelperContent.classList.toggle('hidden');
  });

  // Dictionary action buttons
  importDictionaryBtn.addEventListener('click', importDictionary);
  exportDictionaryBtn.addEventListener('click', exportDictionary);
  resetDictionaryBtn.addEventListener('click', resetDictionary);
  saveDictionaryBtn.addEventListener('click', saveDictionary);

  // Close modal when clicking outside
  window.addEventListener('click', (event) => {
    if (event.target === itemEditModal) {
      itemEditModal.style.display = 'none';
    }
  });
}

// Show a specific tab
function showTab(tabContent) {
  // Hide all tab contents
  document.querySelectorAll('.dictionary-tab-content').forEach(element => {
    element.classList.remove('active');
  });

  // Show the selected tab content
  tabContent.classList.add('active');

  // Update active tab button
  document.querySelectorAll('.dictionary-tab-button').forEach(button => {
    button.classList.remove('active');
  });

  // Find the button that corresponds to this tab content
  const tabId = tabContent.id;
  const buttonId = tabId.replace('-content', '-tab');
  const button = document.getElementById(buttonId);
  if (button) {
    button.classList.add('active');
  }
}

// Add notification styles
const style = document.createElement('style');
style.textContent = `
  .notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    z-index: 1000;
    animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
  }

  .notification.success {
    background-color: #4caf50;
  }

  .notification.error {
    background-color: #f44336;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
`;
document.head.appendChild(style);

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initDictionaryManager);
