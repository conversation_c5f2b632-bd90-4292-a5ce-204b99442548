const installer = require('electron-installer-windows');
const path = require('path');

const options = {
  src: path.join(__dirname, 'dist/TEMPLAR PAYROLL AUDITOR-win32-x64'),
  dest: path.join(__dirname, 'dist/installer'),
  name: 'TEMPLAR PAYROLL AUDITOR',
  productName: 'TEMPLAR PAYROLL AUDITOR',
  exe: 'TEMPLAR PAYROLL AUDITOR.exe',
  description: 'A powerful desktop application for auditing and comparing payroll data',
  authors: ['Templar'],
  noMsi: true,
  iconUrl: path.join(__dirname, 'build/icon.ico'),
  setupIcon: path.join(__dirname, 'build/icon.ico'),
  setupExe: 'TEMPLAR PAYROLL AUDITOR Setup.exe',
  setupMsi: 'TEMPLAR PAYROLL AUDITOR Setup.msi',
  defaultInstallPath: 'C:\\TEMPLAR PAYROLL AUDITOR'
};

console.log('Creating Windows installer...');

installer(options)
  .then(() => console.log('Installer created successfully!'))
  .catch(err => {
    console.error('Error creating installer:', err);
    process.exit(1);
  });
