// Vanilla JavaScript implementation for the renderer

// Simple event system for application-wide events
window.appEvents = {
  listeners: {},

  // Add event listener
  on: function(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  },

  // Remove event listener
  off: function(event, callback) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  },

  // Trigger event
  emit: function(event, data) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }
};

document.addEventListener('DOMContentLoaded', () => {
  // Splash screen code removed

  // Check if the API is available
  if (!window.api || !window.api.isApiAvailable()) {
    console.error('Electron API not available');
    return;
  }

  // Initialize global variables for tracking extraction progress
  window.totalPreviousPages = 0;
  window.currentPreviousPage = 0;
  window.totalCurrentPages = 0;
  window.currentCurrentPage = 0;
  window.extractingPrevious = false;
  window.extractingCurrent = false;
  window.parsingStarted = false;
  window.comparisonStarted = false;
  window.reportingStarted = false;

  // Directly initialize progress bars after a short delay to ensure DOM is ready
  setTimeout(() => {
    console.log('Directly initializing progress bars after DOM load');

    // Get the progress elements
    const previousProgressBar = document.getElementById('previous-progress-bar');
    const previousProgressPercent = document.getElementById('previous-progress-percent');
    const currentProgressBar = document.getElementById('current-progress-bar');
    const currentProgressPercent = document.getElementById('current-progress-percent');

    // Log the elements to verify they exist
    console.log('Previous progress bar element:', previousProgressBar);
    console.log('Previous progress percent element:', previousProgressPercent);
    console.log('Current progress bar element:', currentProgressBar);
    console.log('Current progress percent element:', currentProgressPercent);

    // Directly set initial values
    if (previousProgressBar && previousProgressPercent) {
      previousProgressBar.style.width = '0%';
      previousProgressPercent.textContent = '000/2946 (0%)';
    }

    if (currentProgressBar && currentProgressPercent) {
      currentProgressBar.style.width = '0%';
      currentProgressPercent.textContent = '000/2946 (0%)';
    }
  }, 500);

  // DOM elements
  const currentBrowseBtn = document.getElementById('current-browse-btn');
  const previousBrowseBtn = document.getElementById('previous-browse-btn');
  const startAuditBtn = document.getElementById('start-audit-btn');
  const currentFilePath = document.getElementById('current-file-path');
  const previousFilePath = document.getElementById('previous-file-path');
  const currentFileError = document.getElementById('current-file-error');
  const previousFileError = document.getElementById('previous-file-error');
  const comparisonResults = document.getElementById('comparison-results');

  // Navigation buttons
  const homeBtn = document.getElementById('home-btn');
  const uploadBtn = document.getElementById('upload-btn');
  const comparisonBtn = document.getElementById('comparison-btn');
  const reportsBtn = document.getElementById('reports-btn');
  const sorterBtn = document.getElementById('sorter-btn');
  const builderBtn = document.getElementById('builder-btn');
  const dictionaryBtn = document.getElementById('dictionary-btn');
  const bankAdviserBtn = document.getElementById('bank-adviser-btn');

  // Content containers
  const homeContent = document.getElementById('home-content');
  const uploadContent = document.getElementById('upload-content');
  const comparisonContent = document.getElementById('comparison-content');
  const reportsContent = document.getElementById('reports-content');
  const sorterContent = document.getElementById('sorter-content');
  const builderContent = document.getElementById('builder-content');
  const dictionaryContent = document.getElementById('dictionary-content');
  const bankAdviserContent = document.getElementById('bank-adviser-content');

  // Variables to store file paths
  let currentFile = '';
  let previousFile = '';
  let outputDir = '';

  // Variables to track active processes
  let activeComparisonProcess = null;
  let activeSortProcess = null;
  let activeBuildProcess = null;

  // Function to show a specific content section
  function showContent(contentElement) {
    // Hide all content sections
    [homeContent, uploadContent, comparisonContent, reportsContent, sorterContent, builderContent, dictionaryContent, bankAdviserContent]
      .forEach(element => {
        if (element) element.classList.add('hidden');
      });

    // Show the selected content section
    if (contentElement) contentElement.classList.remove('hidden');

    // Update active button
    [homeBtn, uploadBtn, comparisonBtn, reportsBtn, sorterBtn, builderBtn, dictionaryBtn, bankAdviserBtn]
      .forEach(button => {
        if (button) button.classList.remove('active');
      });

    // Set active button based on content
    if (contentElement === homeContent) homeBtn.classList.add('active');
    else if (contentElement === uploadContent) uploadBtn.classList.add('active');
    else if (contentElement === comparisonContent) comparisonBtn.classList.add('active');
    else if (contentElement === reportsContent) reportsBtn.classList.add('active');
    else if (contentElement === sorterContent) sorterBtn.classList.add('active');
    else if (contentElement === builderContent) builderBtn.classList.add('active');
    else if (contentElement === dictionaryContent) dictionaryBtn.classList.add('active');
    else if (contentElement === bankAdviserContent) bankAdviserBtn.classList.add('active');
  }

  // Set up navigation button event listeners
  if (homeBtn) homeBtn.addEventListener('click', () => showContent(homeContent));
  if (uploadBtn) uploadBtn.addEventListener('click', () => showContent(uploadContent));
  if (comparisonBtn) comparisonBtn.addEventListener('click', () => showContent(comparisonContent));
  if (reportsBtn) reportsBtn.addEventListener('click', () => showContent(reportsContent));
  if (sorterBtn) sorterBtn.addEventListener('click', () => showContent(sorterContent));
  if (builderBtn) builderBtn.addEventListener('click', () => showContent(builderContent));
  if (dictionaryBtn) dictionaryBtn.addEventListener('click', () => showContent(dictionaryContent));
  if (bankAdviserBtn) bankAdviserBtn.addEventListener('click', () => showContent(bankAdviserContent));

  // Function to select a file
  async function selectFile(isCurrentFile) {
    try {
      const filePath = await window.api.selectFile();
      if (filePath) {
        if (isCurrentFile) {
          currentFile = filePath;
          currentFilePath.value = filePath;
          currentFileError.textContent = '';
        } else {
          previousFile = filePath;
          previousFilePath.value = filePath;
          previousFileError.textContent = '';
        }
      }
    } catch (error) {
      console.error('Error selecting file:', error);
      const errorElement = isCurrentFile ? currentFileError : previousFileError;
      errorElement.textContent = `Error: ${error.message}`;
    }
  }

  // Function to select output directory
  async function selectOutputDirectory() {
    try {
      const dirPath = await window.api.selectDirectory();
      if (dirPath) {
        outputDir = dirPath;
      }
    } catch (error) {
      console.error('Error selecting directory:', error);
    }
  }

  // Function to start the audit process
  async function startAudit() {
    // Validate inputs
    if (!currentFile) {
      currentFileError.textContent = 'Please select a current payroll file';
      return;
    }

    if (!previousFile) {
      previousFileError.textContent = 'Please select a previous payroll file';
      return;
    }

    // Validate report signature fields
    const reportName = document.getElementById('report-name');
    const reportDesignation = document.getElementById('report-designation');
    const reportNameError = document.getElementById('report-name-error');
    const reportDesignationError = document.getElementById('report-designation-error');

    let hasError = false;

    // Reset error messages
    if (reportNameError) reportNameError.textContent = '';
    if (reportDesignationError) reportDesignationError.textContent = '';

    // Check if name is provided
    if (!reportName || !reportName.value.trim()) {
      if (reportNameError) reportNameError.textContent = 'Please enter your name';
      hasError = true;
    }

    // Check if designation is provided
    if (!reportDesignation || !reportDesignation.value.trim()) {
      if (reportDesignationError) reportDesignationError.textContent = 'Please enter your designation';
      hasError = true;
    }

    if (hasError) {
      return;
    }

    if (!outputDir) {
      // Use a default output directory if none selected
      outputDir = currentFile.substring(0, currentFile.lastIndexOf('\\'));
    }

    try {
      // Clear previous results and show progress visualization
      comparisonResults.innerHTML = `
        <div class="progress-container" style="display: block !important; visibility: visible !important; opacity: 1 !important;">
          <h4 style="text-align: center;">Processing Payroll Files</h4>
          <div class="progress-visualization" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
            <div class="progress-stages" style="display: flex !important; justify-content: center !important; visibility: visible !important; opacity: 1 !important; margin: 0 auto !important; width: 100% !important; max-width: 600px !important;">
              <div class="progress-stage" id="stage-extraction" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                <div class="stage-icon" id="icon-extraction" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                  <i class="fas fa-file-pdf"></i>
                </div>
                <div class="stage-name" id="name-extraction" style="display: block !important; visibility: visible !important; opacity: 1 !important;">Extraction</div>
              </div>
              <div class="progress-stage" id="stage-parsing" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                <div class="stage-icon" id="icon-parsing" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                  <i class="fas fa-code"></i>
                </div>
                <div class="stage-name" id="name-parsing" style="display: block !important; visibility: visible !important; opacity: 1 !important;">Parsing</div>
              </div>
              <div class="progress-stage" id="stage-comparison" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                <div class="stage-icon" id="icon-comparison" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                  <i class="fas fa-not-equal"></i>
                </div>
                <div class="stage-name" id="name-comparison" style="display: block !important; visibility: visible !important; opacity: 1 !important;">Pre-Auditing</div>
              </div>
              <div class="progress-stage" id="stage-reporting" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                <div class="stage-icon" id="icon-reporting" style="display: flex !important; visibility: visible !important; opacity: 1 !important;">
                  <i class="fas fa-file-alt"></i>
                </div>
                <div class="stage-name" id="name-reporting" style="display: block !important; visibility: visible !important; opacity: 1 !important;">Reporting</div>
              </div>
            </div>

            <!-- Extraction progress UI with new design based on screenshot -->
            <div style="width: 100%; background-color: #000000; margin-top: 15px; border: 1px solid #000000; padding: 10px; border-radius: 10px;">
              <!-- Progress Label -->
              <div style="text-align: center; margin-bottom: 10px; color: #4caf50; font-weight: bold; font-size: 16px;">PROGRESS</div>

              <!-- Progress Bar -->
              <div style="position: relative; width: 100%; height: 30px; background-color: #000000; border-radius: 15px; overflow: hidden; border: 2px solid #00a2ff;">
                <!-- Green progress bar -->
                <div id="main-progress-bar" style="background-color: #00ff00; height: 100%; width: 8%; position: absolute; left: 0; top: 0; transition: width 0.5s ease-in-out;"></div>

                <!-- Percentage text (positioned at the center) -->
                <div id="main-progress-percent" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ffffff; font-weight: bold; font-size: 16px; z-index: 10;">8%</div>

                <!-- Progress count (positioned at the end of the bar) -->
                <div id="main-progress-count" style="position: absolute; top: 50%; right: 10px; transform: translateY(-50%); color: #ffffff; font-size: 14px; z-index: 10;">(0/0)</div>
              </div>
            </div>

            <!-- Processing details dialog box - ONLY place where processing messages should appear -->
            <div class="processing-details-container">
              <h5>Processing Details</h5>
              <div class="processing-details-box" id="processing-details-box"></div>
              <!-- Hidden progress log element for compatibility with updateProgressVisualization function -->
              <div id="progress-log" style="display: none;"></div>
            </div>

            <div class="stop-button-container">
              <button id="stop-comparison-btn" class="stop-btn">Stop Process</button>
            </div>
          </div>
        </div>
      `;

      // Initialize the processing details box (no welcome message needed)
      setTimeout(() => {
        const processingDetailsBox = document.getElementById('processing-details-box');
        if (processingDetailsBox) {
          // Clear any existing content
          processingDetailsBox.innerHTML = '';
        }
      }, 100);

      // Set up stop button event listener
      const stopComparisonBtn = document.getElementById('stop-comparison-btn');
      if (stopComparisonBtn) {
        stopComparisonBtn.addEventListener('click', () => {
          if (activeComparisonProcess) {
            window.api.stopProcess('comparison');
            activeComparisonProcess = null;
            // Add a message to the processing details box instead
            const processingDetailsBox = document.getElementById('processing-details-box');
            if (processingDetailsBox) {
              const detailEntry = document.createElement('div');
              detailEntry.className = 'processing-detail-entry';
              detailEntry.textContent = 'Process stopped by user.';
              processingDetailsBox.appendChild(detailEntry);
              processingDetailsBox.scrollTop = processingDetailsBox.scrollHeight;
            }
          }
        });
      }

      // Make sure the progress visualization is visible
      const progressVisualization = document.querySelector('.progress-visualization');
      if (progressVisualization) {
        progressVisualization.style.display = 'flex';
        progressVisualization.style.visibility = 'visible';
        progressVisualization.style.opacity = '1';
      }

      // Make sure the progress stages are visible
      const progressStages = document.querySelector('.progress-stages');
      if (progressStages) {
        progressStages.style.display = 'flex';
        progressStages.style.visibility = 'visible';
        progressStages.style.opacity = '1';
      }

      // Initialize progress visualization
      updateProgressVisualization('extraction', 'active', 'Starting extraction...');

      // Make sure the progress bar is initialized correctly
      setTimeout(() => {
        // Make sure the progress bar is visible and has the correct initial state
        const mainProgressBar = document.getElementById('main-progress-bar');
        const mainProgressPercent = document.getElementById('main-progress-percent');
        const mainProgressCount = document.getElementById('main-progress-count');

        // Log the elements to verify they exist
        console.log('Main progress bar element (in startAudit):', mainProgressBar);
        console.log('Main progress percent element (in startAudit):', mainProgressPercent);
        console.log('Main progress count element (in startAudit):', mainProgressCount);

        // Set initial realistic page counts (will be updated when actual counts are known)
        const estimatedTotalPages = 2946; // Example realistic page count

        console.log('Initializing progress bar with estimated page count');

        // Set extraction flags
        window.extractingPrevious = true;
        window.extractingCurrent = false;

        // Use our direct update function for consistency
        if (window.updateProgressBar) {
          window.updateProgressBar('main', 0, estimatedTotalPages);
        } else if (typeof updateProgressBar === 'function') {
          updateProgressBar('main', 0, estimatedTotalPages);
        } else {
          console.error('updateProgressBar function not found');

          // Fallback: update the progress bar directly
          if (mainProgressBar && mainProgressPercent && mainProgressCount) {
            mainProgressBar.style.width = '0%';
            mainProgressPercent.textContent = '0%';
            mainProgressCount.textContent = '(0/' + estimatedTotalPages + ')';
          }
        }

        // Store the estimated total for later use
        window.estimatedTotalPages = estimatedTotalPages;
      }, 100);

      // We're using the stop button that's already in the HTML

      // Show the comparison tab
      showContent(comparisonContent);

      // Always use employee_id as the default identifier
      const idField = 'employee_id';

      // Get the selected months and years
      const currentMonthSelect = document.getElementById('current-month');
      const currentYearSelect = document.getElementById('current-year');
      const previousMonthSelect = document.getElementById('previous-month');
      const previousYearSelect = document.getElementById('previous-year');

      const currentMonth = currentMonthSelect ? currentMonthSelect.value : 'May';
      const currentYear = currentYearSelect ? currentYearSelect.value : '2025';
      const previousMonth = previousMonthSelect ? previousMonthSelect.value : 'April';
      const previousYear = previousYearSelect ? previousYearSelect.value : '2025';

      console.log('Starting audit with files:', {
        prevFile: previousFile,
        currFile: currentFile,
        outputDir: outputDir,
        idField: idField,
        currentMonth: currentMonth,
        currentYear: currentYear,
        previousMonth: previousMonth,
        previousYear: previousYear
      });

      // Call the backend process
      activeComparisonProcess = true; // Set the active process flag
      console.log(`Month/Year info: Current: ${currentMonth} ${currentYear}, Previous: ${previousMonth} ${previousYear}`);

      // Get the signature information
      const reportName = document.getElementById('report-name').value.trim();
      const reportDesignation = document.getElementById('report-designation').value.trim();

      const result = await window.api.processPdfs({
        prevFile: previousFile,
        currFile: currentFile,
        outputDir: outputDir,
        idField: idField,
        currentMonth: currentMonth,
        currentYear: currentYear,
        previousMonth: previousMonth,
        previousYear: previousYear,
        reportName: reportName,
        reportDesignation: reportDesignation
      });
      activeComparisonProcess = null; // Reset the active process flag

      console.log('Received result from backend:', result);

      if (result && result.success) {
        // Display results
        let resultsHtml = '<div class="results-container">';
        resultsHtml += '<h4>Processing completed successfully</h4>';

        // Display audit summary if available
        if (result.comparisonData && result.comparisonData.length > 0) {
          resultsHtml += '<h4>Audit Summary:</h4>';
          resultsHtml += '<div class="comparison-summary">';

          // Count statistics
          const totalEmployees = result.comparisonData.length;
          const employeesWithChanges = result.comparisonData.filter(emp =>
            emp.changes.length > 0 && emp.changes[0] !== "No significant changes detected"
          ).length;
          const newEmployees = result.comparisonData.filter(emp =>
            emp.changes.includes("New employee (not in previous payroll)")
          ).length;
          const removedEmployees = result.comparisonData.filter(emp =>
            emp.changes.includes("Employee removed (not in current payroll)")
          ).length;

          resultsHtml += `<p><strong>Total employees analyzed:</strong> ${totalEmployees}</p>`;
          resultsHtml += `<p><strong>Employees with changes:</strong> ${employeesWithChanges}</p>`;
          resultsHtml += `<p><strong>New employees:</strong> ${newEmployees}</p>`;
          resultsHtml += `<p><strong>Removed employees:</strong> ${removedEmployees}</p>`;

          // Display significant changes
          if (employeesWithChanges > 0) {
            resultsHtml += '<h4>Significant Changes:</h4>';
            resultsHtml += '<ul class="changes-list">';

            result.comparisonData.forEach(emp => {
              if (emp.changes.length > 0 && emp.changes[0] !== "No significant changes detected") {
                resultsHtml += `<li><strong>${emp.name || emp.id}:</strong><ul>`;
                emp.changes.forEach(change => {
                  resultsHtml += `<li>${change}</li>`;
                });
                resultsHtml += '</ul></li>';
              }
            });

            resultsHtml += '</ul>';
          }

          resultsHtml += '</div>';
        }

        // Display report links
        if (result.links && result.links.length > 0) {
          // Filter out hidden report types
          const filteredLinks = result.links.filter(link =>
            !link.hidden && (link.type === 'Excel' || link.type === 'Word' || link.type === 'PDF')
          );

          if (filteredLinks.length > 0) {
            resultsHtml += '<h4>Reports:</h4>';
            resultsHtml += '<div class="report-buttons-container">';

            // Always show all three report types (Excel, Word, PDF)
            const reportTypes = ['Excel', 'Word', 'PDF'];
            reportTypes.forEach(type => {
              const link = filteredLinks.find(l => l.type === type);
              if (link) {
                resultsHtml += `<button class="report-button ${type.toLowerCase()}-button" data-path="${link.path}" data-type="${type}">${type} Report</button>`;

                // Automatically save to Report Manager
                setTimeout(() => {
                  saveToReportManager(link.path, type);
                }, 500);
              } else {
                // If the link doesn't exist, show a disabled button
                resultsHtml += `<button class="report-button ${type.toLowerCase()}-button disabled" disabled>${type} Report</button>`;
              }
            });

            resultsHtml += '</div>';

            // Add a note about opening the reports
            resultsHtml += '<p class="note">Click on the buttons above to open the generated reports. Reports are also saved to the Report Manager.</p>';
            resultsHtml += '<p class="note">Word and PDF reports are generated with proper formatting directly from the source data.</p>';
          } else {
            resultsHtml += '<p>No reports were generated. This might indicate that the PDF parsing did not find any payroll data.</p>';
          }
        } else {
          resultsHtml += '<p>No reports were generated. This might indicate that the PDF parsing did not find any payroll data.</p>';
          resultsHtml += '<p>Try using the sample files provided in the backend directory:</p>';
          resultsHtml += '<ul>';
          resultsHtml += '<li>C:\\Users\\<USER>\\Desktop\\backend\\previous_payroll_sample.pdf</li>';
          resultsHtml += '<li>C:\\Users\\<USER>\\Desktop\\backend\\current_payroll_sample.pdf</li>';
          resultsHtml += '</ul>';
        }

        resultsHtml += '</div>';
        comparisonResults.innerHTML = resultsHtml;

        // Add event listeners to report buttons
        document.querySelectorAll('.report-button:not(.disabled)').forEach(button => {
          button.addEventListener('click', async (e) => {
            e.preventDefault();
            const path = button.getAttribute('data-path');
            const type = button.getAttribute('data-type');
            if (path) {
              try {
                // Check if file exists before opening
                const fileExists = await window.api.checkFileExists(path);
                if (fileExists) {
                  window.api.openFile(path);
                  console.log(`Successfully opened report: ${path}`);
                  // Report is already saved to Report Manager automatically
                } else {
                  console.error(`Report file not found: ${path}`);

                  // For Word reports, try to find the file with a different extension
                  if (type === 'Word') {
                    // Try both .docx and .doc extensions
                    const docxPath = path.replace('.doc', '.docx');
                    const docExists = await window.api.checkFileExists(path);
                    const docxExists = await window.api.checkFileExists(docxPath);

                    console.log(`Word report path check: Original (.doc) exists: ${docExists}, .docx exists: ${docxExists}`);

                    if (docxExists) {
                      console.log(`Attempting to open Word report with .docx extension: ${docxPath}`);
                      window.api.openFile(docxPath);
                      console.log(`Successfully opened Word report with alternate extension: ${docxPath}`);
                      return;
                    } else if (docExists) {
                      console.log(`Attempting to open original Word report: ${path}`);
                      window.api.openFile(path);
                      console.log(`Successfully opened original Word report: ${path}`);
                      return;
                    }
                  }

                  // Try to find the file with a different name pattern (with month/year)
                  const pathParts = path.split('\\');
                  const dirPath = pathParts.slice(0, -1).join('\\');

                  // Get the current and previous months from the UI
                  const currentMonth = document.getElementById('current-month')?.value || 'Current';
                  const currentYear = document.getElementById('current-year')?.value || 'Year';
                  const previousMonth = document.getElementById('previous-month')?.value || 'Previous';
                  const previousYear = document.getElementById('previous-year')?.value || 'Year';

                  // Look for files with a pattern that includes the months
                  const pattern = `payroll_audit_${currentMonth}_${currentYear}_vs_${previousMonth}_${previousYear}`;

                  // Try to find a matching file
                  try {
                    const files = await window.api.listFiles(dirPath);
                    const matchingFile = files.find(file =>
                      file.includes(pattern) && file.toLowerCase().endsWith(`.${type.toLowerCase()}`)
                    );

                    if (matchingFile) {
                      const fullPath = `${dirPath}\\${matchingFile}`;
                      window.api.openFile(fullPath);
                      // Update the button's data-path attribute
                      button.setAttribute('data-path', fullPath);
                      // Also save to Report Manager
                      saveToReportManager(fullPath, type);
                      return;
                    }
                  } catch (listError) {
                    console.error(`Error listing files: ${listError.message}`);
                  }

                  alert(`Error: The ${type} report file could not be found. It may have failed to generate properly.`);
                }
              } catch (error) {
                console.error(`Error opening report: ${error.message}`);
                alert(`Error opening ${type} report: ${error.message}`);
              }
            }
          });
        });

        // Add event listener for LibreOffice link
        const libreOfficeLink = document.getElementById('libreoffice-link');
        if (libreOfficeLink) {
          libreOfficeLink.addEventListener('click', (e) => {
            e.preventDefault();
            window.api.openFile('https://www.libreoffice.org/download/download/');
          });
        }
      } else {
        let errorMessage = 'Unknown error occurred';
        if (result && result.error) {
          errorMessage = result.error;
        } else if (!result) {
          errorMessage = 'No response received from backend';
        }

        comparisonResults.innerHTML = `
          <div class="error-container">
            <h4>Error Processing Payroll Files</h4>
            <p class="error">${errorMessage}</p>
            <p>Please check the following:</p>
            <ul>
              <li>The selected files are valid PDF payroll documents</li>
              <li>The Python backend is properly installed</li>
              <li>Try using the sample files provided in the backend directory</li>
            </ul>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error processing PDFs:', error);
      comparisonResults.innerHTML = `
        <div class="error-container">
          <h4>Application Error</h4>
          <p class="error">${error.message || 'An unexpected error occurred'}</p>
          <p>Please try again or contact support.</p>
        </div>
      `;
    }
  }

  // PDF Sorter functionality
  const sortInputBrowseBtn = document.getElementById('sort-input-browse-btn');
  const sortOutputBrowseBtn = document.getElementById('sort-output-browse-btn');
  const startSortBtn = document.getElementById('start-sort-btn');
  const sortInputFile = document.getElementById('sort-input-file');
  const sortOutputFile = document.getElementById('sort-output-file');
  const sortInputFileError = document.getElementById('sort-input-file-error');
  const sortOutputFileError = document.getElementById('sort-output-file-error');
  const sortResults = document.getElementById('sort-results');

  // Variables to store PDF sorter file paths
  let sortInputFilePath = '';
  let sortOutputFilePath = '';

  // Function to select a PDF file for sorting
  async function selectSortInputFile() {
    try {
      const filePath = await window.api.selectFile();
      if (filePath) {
        sortInputFilePath = filePath;

        // Check if sortInputFile exists before setting its value
        if (sortInputFile) {
          sortInputFile.value = filePath;
        }

        // Check if sortInputFileError exists before setting its textContent
        if (sortInputFileError) {
          sortInputFileError.textContent = '';
        }

        // Suggest an output file name
        if (!sortOutputFilePath) {
          const lastDotIndex = filePath.lastIndexOf('.');
          const lastSlashIndex = filePath.lastIndexOf('\\');
          if (lastDotIndex > 0 && lastSlashIndex > 0) {
            const baseName = filePath.substring(lastSlashIndex + 1, lastDotIndex);
            const extension = filePath.substring(lastDotIndex);
            sortOutputFilePath = filePath.substring(0, lastSlashIndex + 1) + baseName + '_sorted' + extension;

            // Check if sortOutputFile exists before setting its value
            if (sortOutputFile) {
              sortOutputFile.value = sortOutputFilePath;
            }
          }
        }
      }
    } catch (error) {
      console.error('Error selecting sort input file:', error);

      // Check if sortInputFileError exists before setting its textContent
      if (sortInputFileError) {
        sortInputFileError.textContent = `Error: ${error.message}`;
      }
    }
  }

  // Function to select an output file for the sorted PDF
  async function selectSortOutputFile() {
    try {
      const options = {
        properties: ['openFile', 'promptToCreate'],
        filters: [{ name: 'PDF Files', extensions: ['pdf'] }],
        title: 'Save Sorted PDF As'
      };

      const filePath = await window.api.selectFile(options);
      if (filePath) {
        sortOutputFilePath = filePath;

        // Check if sortOutputFile exists before setting its value
        if (sortOutputFile) {
          sortOutputFile.value = filePath;
        }

        // Check if sortOutputFileError exists before setting its textContent
        if (sortOutputFileError) {
          sortOutputFileError.textContent = '';
        }
      }
    } catch (error) {
      console.error('Error selecting sort output file:', error);

      // Check if sortOutputFileError exists before setting its textContent
      if (sortOutputFileError) {
        sortOutputFileError.textContent = `Error: ${error.message}`;
      }
    }
  }

  // Function to start the PDF sorting process
  async function startSort() {
    // Validate inputs
    if (!sortInputFilePath) {
      // Check if sortInputFileError exists before setting its textContent
      if (sortInputFileError) {
        sortInputFileError.textContent = 'Please select a PDF file to sort';
      }
      return;
    }

    try {
      // Clear previous results
      sortResults.innerHTML = `
        <div class="progress-container">
          <p>Sorting PDF...</p>
          <div class="processing-details-container">
            <h5>Processing Details</h5>
            <div class="processing-details-box" id="processing-details-box"></div>
            <!-- Hidden progress log element for compatibility with updateProgressVisualization function -->
            <div id="progress-log" style="display: none;"></div>
          </div>
        </div>
      `;

      // Get the selected ID field
      const sortIdFieldSelect = document.getElementById('sort-id-field-select');
      const idField = sortIdFieldSelect ? sortIdFieldSelect.value : 'employee_id';

      // Generate output file name automatically
      const lastDotIndex = sortInputFilePath.lastIndexOf('.');
      const lastSlashIndex = sortInputFilePath.lastIndexOf('\\');
      let outputFilePath = '';

      if (lastDotIndex > 0 && lastSlashIndex > 0) {
        const baseName = sortInputFilePath.substring(lastSlashIndex + 1, lastDotIndex);
        const extension = sortInputFilePath.substring(lastDotIndex);
        outputFilePath = sortInputFilePath.substring(0, lastSlashIndex + 1) +
                         baseName + '_sorted_by_' + idField + extension;
      } else {
        // Fallback if we can't parse the input path
        outputFilePath = sortInputFilePath + '_sorted_by_' + idField;
      }

      console.log('Starting PDF sort with:', {
        pdfFile: sortInputFilePath,
        outputFile: outputFilePath,
        idField: idField
      });

      // Call the backend process
      const result = await window.api.sortPdf({
        pdfFile: sortInputFilePath,
        outputFile: outputFilePath,
        idField: idField
      });

      console.log('Received result from backend:', result);

      if (result && result.success) {
        // Display results
        sortResults.innerHTML = `
          <div class="success-container">
            <h4>PDF Sorting Completed Successfully</h4>
            <p>The sorted PDF has been saved to:</p>
            <p class="file-path">${result.outputFile}</p>
            <div class="action-buttons">
              <button id="open-sorted-pdf-btn" class="action-btn">Open PDF</button>
            </div>
          </div>
        `;

        // Add event listeners for the action buttons
        const openSortedPdfBtn = document.getElementById('open-sorted-pdf-btn');
        if (openSortedPdfBtn) {
          openSortedPdfBtn.addEventListener('click', () => {
            // Open the sorted PDF
            window.open(`file://${result.outputFile}`, '_blank');
          });
        }
      } else {
        let errorMessage = 'Unknown error occurred';
        if (result && result.error) {
          errorMessage = result.error;
        } else if (!result) {
          errorMessage = 'No response received from backend';
        }

        sortResults.innerHTML = `
          <div class="error-container">
            <h4>Error Sorting PDF</h4>
            <p class="error">${errorMessage}</p>
            <p>Please check the following:</p>
            <ul>
              <li>The selected file is a valid PDF document</li>
              <li>The Python backend is properly installed</li>
              <li>The output file path is valid and writable</li>
            </ul>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error sorting PDF:', error);
      sortResults.innerHTML = `
        <div class="error-container">
          <h4>Application Error</h4>
          <p class="error">${error.message || 'An unexpected error occurred'}</p>
          <p>Please try again or contact support.</p>
        </div>
      `;
    }
  }

  // Data Builder functionality
  const buildInputBrowseBtn = document.getElementById('build-input-browse-btn');
  const buildOutputBrowseBtn = document.getElementById('build-output-browse-btn');
  const startBuildBtn = document.getElementById('start-build-btn');
  const buildInputFile = document.getElementById('build-input-file');
  const buildOutputFile = document.getElementById('build-output-file');
  const buildInputFileError = document.getElementById('build-input-file-error');
  const buildOutputFileError = document.getElementById('build-output-file-error');
  const buildResults = document.getElementById('build-results');

  // Variables to store Data Builder file paths
  let buildInputFilePath = '';
  let buildOutputFilePath = '';

  // Function to select a PDF file for data building
  async function selectBuildInputFile() {
    try {
      const filePath = await window.api.selectFile();
      if (filePath) {
        buildInputFilePath = filePath;

        // Check if buildInputFile exists before setting its value
        if (buildInputFile) {
          buildInputFile.value = filePath;
        }

        // Check if buildInputFileError exists before setting its textContent
        if (buildInputFileError) {
          buildInputFileError.textContent = '';
        }

        // Suggest an output file name
        if (!buildOutputFilePath) {
          const lastDotIndex = filePath.lastIndexOf('.');
          const lastSlashIndex = filePath.lastIndexOf('\\');
          if (lastDotIndex > 0 && lastSlashIndex > 0) {
            const baseName = filePath.substring(lastSlashIndex + 1, lastDotIndex);
            buildOutputFilePath = filePath.substring(0, lastSlashIndex + 1) + baseName + '_data.xlsx';

            // Check if buildOutputFile exists before setting its value
            if (buildOutputFile) {
              buildOutputFile.value = buildOutputFilePath;
            }
          }
        }
      }
    } catch (error) {
      console.error('Error selecting build input file:', error);

      // Check if buildInputFileError exists before setting its textContent
      if (buildInputFileError) {
        buildInputFileError.textContent = `Error: ${error.message}`;
      }
    }
  }

  // Function to select an output file for the data table
  async function selectBuildOutputFile() {
    try {
      const options = {
        properties: ['openFile', 'promptToCreate'],
        filters: [{ name: 'Excel Files', extensions: ['xlsx'] }],
        title: 'Save Excel File As'
      };

      const filePath = await window.api.selectFile(options);
      if (filePath) {
        buildOutputFilePath = filePath;

        // Check if buildOutputFile exists before setting its value
        if (buildOutputFile) {
          buildOutputFile.value = filePath;
        }

        // Check if buildOutputFileError exists before setting its textContent
        if (buildOutputFileError) {
          buildOutputFileError.textContent = '';
        }
      }
    } catch (error) {
      console.error('Error selecting build output file:', error);

      // Check if buildOutputFileError exists before setting its textContent
      if (buildOutputFileError) {
        buildOutputFileError.textContent = `Error: ${error.message}`;
      }
    }
  }

  // Function to start the data building process
  async function startBuild() {
    // Validate inputs
    if (!buildInputFilePath) {
      // Check if buildInputFileError exists before setting its textContent
      if (buildInputFileError) {
        buildInputFileError.textContent = 'Please select a PDF file to process';
      }
      return;
    }

    // Get the selected fields
    const selectedFields = [];
    document.querySelectorAll('.field-checkbox input[type="checkbox"]:checked').forEach(checkbox => {
      selectedFields.push(checkbox.value);
    });

    if (selectedFields.length === 0) {
      buildResults.innerHTML = `
        <div class="error-container">
          <h4>Error Building Data Table</h4>
          <p class="error">Please select at least one field to include in the data table</p>
        </div>
      `;
      return;
    }

    try {
      // Clear previous results
      buildResults.innerHTML = `
        <div class="progress-container">
          <p>Building data table...</p>
          <div class="processing-details-container">
            <h5>Processing Details</h5>
            <div class="processing-details-box" id="processing-details-box"></div>
          </div>
        </div>
      `;

      // Generate output file name automatically
      const lastDotIndex = buildInputFilePath.lastIndexOf('.');
      const lastSlashIndex = buildInputFilePath.lastIndexOf('\\');
      let outputFilePath = '';

      if (lastDotIndex > 0 && lastSlashIndex > 0) {
        const baseName = buildInputFilePath.substring(lastSlashIndex + 1, lastDotIndex);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
        outputFilePath = buildInputFilePath.substring(0, lastSlashIndex + 1) +
                         baseName + '_data_' + timestamp + '.xlsx';
      } else {
        // Fallback if we can't parse the input path
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
        outputFilePath = buildInputFilePath + '_data_' + timestamp + '.xlsx';
      }

      console.log('Starting data build with:', {
        pdfFile: buildInputFilePath,
        outputFile: outputFilePath,
        fields: selectedFields
      });

      // Call the backend process
      const result = await window.api.buildDataTable({
        pdfFile: buildInputFilePath,
        outputFile: outputFilePath,
        fields: selectedFields
      });

      console.log('Received result from backend:', result);

      if (result && result.success) {
        // Display results
        buildResults.innerHTML = `
          <div class="success-container">
            <h4>Data Table Building Completed Successfully</h4>
            <p>The data table has been saved to:</p>
            <p class="file-path">${result.outputFile}</p>
            <div class="action-buttons">
              <button id="open-data-table-btn" class="action-btn">Open Excel</button>
            </div>
          </div>
        `;

        // Add event listeners for the action buttons
        const openDataTableBtn = document.getElementById('open-data-table-btn');
        if (openDataTableBtn) {
          openDataTableBtn.addEventListener('click', () => {
            // Open the data table
            window.open(`file://${result.outputFile}`, '_blank');
          });
        }
      } else {
        let errorMessage = 'Unknown error occurred';
        if (result && result.error) {
          errorMessage = result.error;
        } else if (!result) {
          errorMessage = 'No response received from backend';
        }

        buildResults.innerHTML = `
          <div class="error-container">
            <h4>Error Building Data Table</h4>
            <p class="error">${errorMessage}</p>
            <p>Please check the following:</p>
            <ul>
              <li>The selected file is a valid PDF document</li>
              <li>The Python backend is properly installed</li>
              <li>The output file path is valid and writable</li>
            </ul>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error building data table:', error);
      buildResults.innerHTML = `
        <div class="error-container">
          <h4>Application Error</h4>
          <p class="error">${error.message || 'An unexpected error occurred'}</p>
          <p>Please try again or contact support.</p>
        </div>
      `;
    }
  }

  // Set up event listeners
  if (currentBrowseBtn) currentBrowseBtn.addEventListener('click', () => selectFile(true));
  if (previousBrowseBtn) previousBrowseBtn.addEventListener('click', () => selectFile(false));
  if (startAuditBtn) startAuditBtn.addEventListener('click', startAudit);

  // Set up PDF Sorter event listeners
  if (sortInputBrowseBtn) sortInputBrowseBtn.addEventListener('click', selectSortInputFile);
  if (sortOutputBrowseBtn) sortOutputBrowseBtn.addEventListener('click', selectSortOutputFile);
  if (startSortBtn) startSortBtn.addEventListener('click', startSort);

  // Set up Data Builder event listeners
  if (buildInputBrowseBtn) buildInputBrowseBtn.addEventListener('click', selectBuildInputFile);
  if (buildOutputBrowseBtn) buildOutputBrowseBtn.addEventListener('click', selectBuildOutputFile);
  if (startBuildBtn) startBuildBtn.addEventListener('click', startBuild);

  // Set up Report Manager tab switching
  const comparisonReportsTab = document.getElementById('comparison-reports-tab');
  const sortedPdfTab = document.getElementById('sorted-pdf-tab');
  const dataTablesTab = document.getElementById('data-tables-tab');

  const comparisonReportsContent = document.getElementById('comparison-reports-content');
  const sortedPdfContent = document.getElementById('sorted-pdf-content');
  const dataTablesContent = document.getElementById('data-tables-content');

  function showReportTab(tabContent) {
    // Hide all tab content
    [comparisonReportsContent, sortedPdfContent, dataTablesContent].forEach(content => {
      if (content) content.classList.remove('active');
    });

    // Show selected tab content
    if (tabContent) tabContent.classList.add('active');

    // Update active tab button
    [comparisonReportsTab, sortedPdfTab, dataTablesTab].forEach(tab => {
      if (tab) tab.classList.remove('active');
    });

    // Set active tab button
    if (tabContent === comparisonReportsContent) comparisonReportsTab.classList.add('active');
    else if (tabContent === sortedPdfContent) sortedPdfTab.classList.add('active');
    else if (tabContent === dataTablesContent) dataTablesTab.classList.add('active');
  }

  // Set up tab button event listeners
  if (comparisonReportsTab) comparisonReportsTab.addEventListener('click', () => showReportTab(comparisonReportsContent));
  if (sortedPdfTab) sortedPdfTab.addEventListener('click', () => showReportTab(sortedPdfContent));
  if (dataTablesTab) dataTablesTab.addEventListener('click', () => showReportTab(dataTablesContent));

  // Function to update progress visualization
  function updateProgressVisualization(stage, status, message) {
    // Get all stage elements
    const stages = ['extraction', 'parsing', 'comparison', 'reporting'];

    // Update progress bar width based on current stage
    const stageIndex = stages.indexOf(stage);
    if (stageIndex >= 0) {
      const progressBarContainer = document.getElementById('progress-bar-container');
      const progressBar = document.getElementById('progress-bar');

      if (progressBarContainer) {
        progressBarContainer.style.display = 'block';
        progressBarContainer.style.visibility = 'visible';
        progressBarContainer.style.opacity = '1';
      }

      if (progressBar) {
        progressBar.style.display = 'block';
        progressBar.style.visibility = 'visible';
        progressBar.style.opacity = '1';

        // Use requestAnimationFrame for smoother updates
        requestAnimationFrame(() => {
          const progressPercent = ((stageIndex + (status === 'completed' ? 1 : 0.5)) / stages.length) * 100;
          progressBar.style.width = `${progressPercent}%`;
          // Force a repaint
          progressBar.offsetHeight;
        });
      }
    }

    // Update stage icons and names
    stages.forEach((s, index) => {
      const stageElement = document.getElementById(`stage-${s}`);
      const icon = document.getElementById(`icon-${s}`);
      const name = document.getElementById(`name-${s}`);

      if (stageElement && icon && name) {
        // Make sure the stage element is visible
        stageElement.style.display = 'flex';
        stageElement.style.visibility = 'visible';
        stageElement.style.opacity = '1';

        // Make sure the icon is visible
        icon.style.display = 'flex';
        icon.style.visibility = 'visible';
        icon.style.opacity = '1';

        // Make sure the name is visible
        name.style.display = 'block';
        name.style.visibility = 'visible';
        name.style.opacity = '1';

        // Reset all classes
        icon.classList.remove('active', 'completed');
        name.classList.remove('active', 'completed');

        // Set appropriate class based on current stage
        if (index < stageIndex || (index === stageIndex && status === 'completed')) {
          icon.classList.add('completed');
          name.classList.add('completed');
        } else if (index === stageIndex && status === 'active') {
          icon.classList.add('active');
          name.classList.add('active');
        }
      }
    });

    // IMPORTANT: Do NOT add messages to any log or display area here
    // Processing details are already handled by the onBackendProgress event handler

    // Check for percentage progress in the message
    if (message) {
      // Make sure extraction progress section is visible
      const extractionProgress = document.querySelector('.extraction-progress');
      if (extractionProgress) {
        extractionProgress.style.display = 'block';
        extractionProgress.style.visibility = 'visible';
        extractionProgress.style.opacity = '1';
      }

      // Make sure progress rows are visible
      const progressRows = document.querySelectorAll('.progress-row');
      progressRows.forEach(row => {
        row.style.display = 'flex';
        row.style.visibility = 'visible';
        row.style.opacity = '1';
      });

      // Check for previous payslips progress
      const previousMatch = message.match(/Previous payslips: (\d+)%/);
      if (previousMatch) {
        const percent = parseInt(previousMatch[1]);
        const previousProgressBar = document.getElementById('previous-progress-bar');
        const previousProgressPercent = document.getElementById('previous-progress-percent');
        const previousProgressWrapper = previousProgressBar ? previousProgressBar.parentElement : null;

        if (previousProgressWrapper) {
          previousProgressWrapper.style.display = 'block';
          previousProgressWrapper.style.visibility = 'visible';
          previousProgressWrapper.style.opacity = '1';
        }

        if (previousProgressBar && previousProgressPercent) {
          previousProgressBar.style.display = 'block';
          previousProgressBar.style.visibility = 'visible';
          previousProgressBar.style.opacity = '1';

          requestAnimationFrame(() => {
            previousProgressBar.style.width = `${percent}%`;
            // Make sure we show the count format instead of just percentage
            if (totalPreviousPages > 0) {
              const currentCount = Math.round((percent / 100) * totalPreviousPages);
              previousProgressPercent.textContent = `${currentCount}/${totalPreviousPages} (${percent}%)`;
            } else {
              previousProgressPercent.textContent = `0/0 (${percent}%)`;
            }
            // Force a repaint
            previousProgressBar.offsetHeight;
          });
        }
      }

      // Check for current payslips progress
      const currentMatch = message.match(/Current payslips: (\d+)%/);
      if (currentMatch) {
        const percent = parseInt(currentMatch[1]);
        const currentProgressBar = document.getElementById('current-progress-bar');
        const currentProgressPercent = document.getElementById('current-progress-percent');
        const currentProgressWrapper = currentProgressBar ? currentProgressBar.parentElement : null;

        if (currentProgressWrapper) {
          currentProgressWrapper.style.display = 'block';
          currentProgressWrapper.style.visibility = 'visible';
          currentProgressWrapper.style.opacity = '1';
        }

        if (currentProgressBar && currentProgressPercent) {
          currentProgressBar.style.display = 'block';
          currentProgressBar.style.visibility = 'visible';
          currentProgressBar.style.opacity = '1';

          requestAnimationFrame(() => {
            currentProgressBar.style.width = `${percent}%`;
            // Make sure we show the count format instead of just percentage
            if (totalCurrentPages > 0) {
              const currentCount = Math.round((percent / 100) * totalCurrentPages);
              currentProgressPercent.textContent = `${currentCount}/${totalCurrentPages} (${percent}%)`;
            } else {
              currentProgressPercent.textContent = `0/0 (${percent}%)`;
            }
            // Force a repaint
            currentProgressBar.offsetHeight;
          });
        }
      }
    }
  }

  // Function to complete progress visualization
  function completeProgressVisualization() {
    updateProgressVisualization('reporting', 'completed', 'Processing completed successfully.');
  }

  // Function to save report to Report Manager
  function saveToReportManager(filePath, reportType) {
    // Get the reports container or create it if it doesn't exist
    let reportsContainer = document.getElementById('reports-container');
    if (!reportsContainer) {
      const reportsContent = document.getElementById('reports-content');
      if (reportsContent) {
        reportsContainer = document.createElement('div');
        reportsContainer.id = 'reports-container';
        reportsContent.appendChild(reportsContainer);
      }
    }

    if (reportsContainer) {
      // Create a new report entry
      const reportEntry = document.createElement('div');
      reportEntry.className = 'report-entry';

      // Get the file name from the path
      const fileName = filePath.split('\\').pop();

      // Get the current date and time
      const now = new Date();
      const dateTime = now.toLocaleString();

      // Create the report entry content
      reportEntry.innerHTML = `
        <div class="report-info">
          <div class="report-type ${reportType.toLowerCase()}-type">${reportType}</div>
          <div class="report-name">${fileName}</div>
          <div class="report-date">${dateTime}</div>
        </div>
        <div class="report-actions">
          <button class="report-open-btn" data-path="${filePath}">Open</button>
        </div>
      `;

      // Add the report entry to the container
      reportsContainer.insertBefore(reportEntry, reportsContainer.firstChild);

      // Add event listener to the open button
      const openBtn = reportEntry.querySelector('.report-open-btn');
      if (openBtn) {
        openBtn.addEventListener('click', () => {
          window.api.openFile(filePath);
        });
      }
    }
  }

  // Add stop buttons to PDF Sorter and Data Builder tabs
  function addStopButton(containerId, processType) {
    const container = document.getElementById(containerId);
    if (container) {
      const stopButtonContainer = document.createElement('div');
      stopButtonContainer.className = 'stop-button-container';
      stopButtonContainer.innerHTML = `<button id="stop-${processType}-btn" class="stop-btn">Stop Process</button>`;
      container.appendChild(stopButtonContainer);

      const stopButton = document.getElementById(`stop-${processType}-btn`);
      if (stopButton) {
        stopButton.addEventListener('click', () => {
          window.api.stopProcess(processType);
          if (processType === 'sort') {
            activeSortProcess = null;
          } else if (processType === 'build') {
            activeBuildProcess = null;
          }
          // Add a message to the processing details box instead
          const processingDetailsBox = document.getElementById('processing-details-box');
          if (processingDetailsBox) {
            const detailEntry = document.createElement('div');
            detailEntry.className = 'processing-detail-entry';
            detailEntry.textContent = 'Process stopped by user.';
            processingDetailsBox.appendChild(detailEntry);
            processingDetailsBox.scrollTop = processingDetailsBox.scrollHeight;
          }
        });
      }
    }
  }

  // Add stop buttons to PDF Sorter and Data Builder tabs
  addStopButton('sort-results', 'sort');
  addStopButton('build-results', 'build');

  // Simulated progress animation intervals
  let previousProgressInterval = null;
  let currentProgressInterval = null;

  // Function to start simulated progress animation
  function startSimulatedProgress(type, totalPages) {
    // Stop any existing interval
    if (type === 'previous' && previousProgressInterval) {
      clearInterval(previousProgressInterval);
    } else if (type === 'current' && currentProgressInterval) {
      clearInterval(currentProgressInterval);
    }

    // Start with a small value
    let simulatedCount = 1;
    let simulatedPercent = 0;

    // Create an interval to update the progress
    const interval = setInterval(() => {
      try {
        // Get the progress elements - do this inside the interval to ensure they exist
        const progressBar = document.getElementById(`${type}-progress-bar`);
        const progressPercent = document.getElementById(`${type}-progress-percent`);

        if (!progressBar || !progressPercent) {
          console.error(`Progress elements not found for ${type} in simulation`);
          return;
        }

        // Increment the simulated count with a variable rate to make it look more realistic
        // Pages process faster at the beginning and slower as we progress
        const incrementAmount = Math.max(1, Math.floor(Math.random() * 5) * (1 - simulatedCount / totalPages));
        simulatedCount += incrementAmount;

        // Don't exceed 15% of total pages in simulation to leave room for real progress
        if (simulatedCount > totalPages * 0.15) {
          simulatedCount = Math.floor(totalPages * 0.15);
          // Don't stop the simulation, just keep it at 15% with small variations
          // to show that processing is still happening
          simulatedCount += Math.floor(Math.random() * 3) - 1; // Add -1, 0, or 1
          simulatedCount = Math.max(Math.floor(totalPages * 0.14),
                                   Math.min(Math.floor(totalPages * 0.15), simulatedCount));
        }

        // Calculate the percentage
        simulatedPercent = Math.floor((simulatedCount / totalPages) * 100);

        // Format with leading zeros
        const formattedCount = String(simulatedCount).padStart(3, '0');
        const formattedText = `${formattedCount}/${totalPages} (${simulatedPercent}%)`;

        // Update the DOM directly
        progressBar.style.width = `${simulatedPercent}%`;
        progressPercent.textContent = formattedText;

        console.log(`Simulated ${type} progress: ${formattedText}`);
      } catch (error) {
        console.error(`Error in simulated progress for ${type}:`, error);
      }
    }, 200); // Slightly slower for less CPU usage

    // Store the interval
    if (type === 'previous') {
      previousProgressInterval = interval;
    } else if (type === 'current') {
      currentProgressInterval = interval;
    }
  }

  // Function to stop simulated progress
  function stopSimulatedProgress(type) {
    if (type === 'previous' && previousProgressInterval) {
      clearInterval(previousProgressInterval);
      previousProgressInterval = null;
    } else if (type === 'current' && currentProgressInterval) {
      clearInterval(currentProgressInterval);
      currentProgressInterval = null;
    }
  }

  // Function to directly update progress bars using direct DOM manipulation
  function updateProgressBar(type, currentPage, totalPages) {
    console.log(`UPDATING PROGRESS BAR: ${type}, ${currentPage}/${totalPages}`);

    try {
      // Store the total pages in the appropriate variable
      if (type === 'previous') {
        totalPreviousPages = totalPages;
        currentPreviousPage = currentPage;
      } else if (type === 'current') {
        totalCurrentPages = totalPages;
        currentCurrentPage = currentPage;
      }

      // Calculate percentage
      const percent = Math.floor((currentPage / totalPages) * 100);

      // Format the count
      const formattedCount = `(${currentPage}/${totalPages})`;

      console.log(`Setting main progress bar width to ${percent}% and text to ${percent}% ${formattedCount}`);

      // Get the main progress bar elements (we now use a single progress bar)
      const progressBar = document.getElementById('main-progress-bar');
      const progressPercent = document.getElementById('main-progress-percent');
      const progressCount = document.getElementById('main-progress-count');

      if (!progressBar) {
        console.error('Main progress bar element not found');
        return;
      }

      if (!progressPercent) {
        console.error('Main progress percent element not found');
        return;
      }

      if (!progressCount) {
        console.error('Main progress count element not found');
        return;
      }

      // Update the DOM directly with setAttribute for maximum reliability
      progressBar.setAttribute('style', `background-color: #00ff00; height: 100%; width: ${percent}%; position: absolute; left: 0; top: 0; transition: width 0.5s ease-in-out;`);
      progressPercent.textContent = `${percent}%`; // Use textContent instead of innerHTML for security
      progressCount.textContent = formattedCount;

      // Force a repaint
      document.body.offsetHeight;

      console.log(`Progress bar updated successfully to ${percent}%`);

      // Stop any simulated progress
      stopSimulatedProgress(type);
    } catch (error) {
      console.error(`Error updating progress bar: ${error.message}`);
    }
  }

  // Set up backend progress listener
  if (window.api.onBackendProgress) {
    // Variables to track extraction progress - make them globally accessible
    window.totalPreviousPages = 0;
    window.currentPreviousPage = 0;
    window.totalCurrentPages = 0;
    window.currentCurrentPage = 0;
    window.extractingPrevious = false;
    window.extractingCurrent = false;
    window.parsingStarted = false;
    window.comparisonStarted = false;
    window.reportingStarted = false;
    window.totalEmployees = 0;
    window.currentEmployee = 0;

    window.api.onBackendProgress((message) => {
      console.log('Backend progress:', message);

      // Use the checkBackendProgress function from progress-updater.js if available
      if (window.checkBackendProgress && typeof window.checkBackendProgress === 'function') {
        window.checkBackendProgress(message);
      }

      // Add message to processing details box - ONLY place where processing messages should appear
      const processingDetailsContainer = document.querySelector('.processing-details-container');
      const processingDetailsBox = document.getElementById('processing-details-box');

      // Make sure the container is visible
      if (processingDetailsContainer) {
        processingDetailsContainer.style.display = 'block';
      } else {
        console.error('Processing details container not found!');
        // Try to create it if it doesn't exist
        const progressVisualization = document.querySelector('.progress-visualization');
        if (progressVisualization) {
          const newContainer = document.createElement('div');
          newContainer.className = 'processing-details-container';

          const heading = document.createElement('h5');
          heading.textContent = 'Processing Details';

          const newBox = document.createElement('div');
          newBox.id = 'processing-details-box';
          newBox.className = 'processing-details-box';

          newContainer.appendChild(heading);
          newContainer.appendChild(newBox);

          // Add before the stop button container
          const stopButtonContainer = progressVisualization.querySelector('.stop-button-container');
          if (stopButtonContainer) {
            progressVisualization.insertBefore(newContainer, stopButtonContainer);
          } else {
            progressVisualization.appendChild(newContainer);
          }

          // Update the reference
          processingDetailsBox = newBox;
        }
      }

      if (processingDetailsBox) {
        // Check if the message is a page processing message
        const pageProcessingMatch = message.match(/Processing page (\d+)\/(\d+)/);
        if (pageProcessingMatch) {
          // Extract page numbers
          const currentPage = parseInt(pageProcessingMatch[1]);
          const totalPages = parseInt(pageProcessingMatch[2]);

          // Update the last entry if it's a page processing message
          const lastEntry = processingDetailsBox.lastChild;
          if (lastEntry && lastEntry.textContent && lastEntry.textContent.match(/Processing page \d+\/\d+/)) {
            lastEntry.textContent = message;
          } else {
            // Create a new entry if there's no previous page processing message
            const detailEntry = document.createElement('div');
            detailEntry.className = 'processing-detail-entry';

            // Add the message text
            const messageText = document.createTextNode(message);
            detailEntry.appendChild(messageText);

            processingDetailsBox.appendChild(detailEntry);

            // Log to console for debugging
            console.log('Added page processing detail:', message);
          }

          // DIRECT UPDATE: Force update the progress bars immediately
          console.log(`Direct update - Processing page ${currentPage}/${totalPages}`);

          // Calculate percentage
          const percent = Math.floor((currentPage / totalPages) * 100);

          // Format the count with leading zeros
          const formattedCount = String(currentPage).padStart(3, '0');
          const formattedText = `${formattedCount}/${totalPages} (${percent}%)`;

          // Update immediately to ensure the progress bar is in sync with the processing details
          try {
            // Determine which progress bar to update based on the current extraction phase
            if (extractingPrevious) {
              // Update previous payslips progress bar using direct DOM manipulation
              const progressBar = document.getElementById('previous-progress-bar');
              const progressPercent = document.getElementById('previous-progress-percent');

              if (progressBar && progressPercent) {
                // Update the DOM directly with setAttribute for maximum reliability
                progressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: ${percent}%; position: absolute; left: 0; top: 0;`);
                progressPercent.textContent = formattedText;

                // Store the current values
                window.totalPreviousPages = totalPages;
                window.currentPreviousPage = currentPage;

                console.log(`Updated previous progress bar: ${formattedText}`);
              } else {
                console.error('Previous progress elements not found');
              }
            } else if (extractingCurrent) {
              // Update current payslips progress bar using direct DOM manipulation
              const progressBar = document.getElementById('current-progress-bar');
              const progressPercent = document.getElementById('current-progress-percent');

              if (progressBar && progressPercent) {
                // Update the DOM directly with setAttribute for maximum reliability
                progressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: ${percent}%; position: absolute; left: 0; top: 0;`);
                progressPercent.textContent = formattedText;

                // Store the current values
                window.totalCurrentPages = totalPages;
                window.currentCurrentPage = currentPage;

                console.log(`Updated current progress bar: ${formattedText}`);
              } else {
                console.error('Current progress elements not found');
              }
            }

            // Force a repaint to ensure the UI updates
            document.body.offsetHeight;
          } catch (error) {
            console.error('Error updating progress bar:', error);
          }
        } else {
          // For non-page processing messages, always add a new entry
          const detailEntry = document.createElement('div');
          detailEntry.className = 'processing-detail-entry';

          // Add the message text
          const messageText = document.createTextNode(message);
          detailEntry.appendChild(messageText);

          processingDetailsBox.appendChild(detailEntry);

          // Log to console for debugging
          console.log('Added processing detail:', message);

          // Check for total pages information
          const totalPagesMatch = message.match(/Total pages in PDF: (\d+)/);
          if (totalPagesMatch) {
            const totalPages = parseInt(totalPagesMatch[1]);
            console.log(`Detected total pages: ${totalPages}`);

            // Format with leading zeros for better appearance
            const formattedCount = String(1).padStart(3, '0');
            const formattedText = `${formattedCount}/${totalPages} (0%)`;

            // Use a timeout to ensure the DOM has been updated
            setTimeout(() => {
              try {
                if (extractingPrevious) {
                  console.log(`Initializing previous progress bar with ${totalPages} pages`);

                  // Initialize using direct DOM manipulation
                  const progressBar = document.getElementById('previous-progress-bar');
                  const progressPercent = document.getElementById('previous-progress-percent');

                  if (progressBar && progressPercent) {
                    // Store the total pages
                    window.totalPreviousPages = totalPages;

                    // Update the DOM directly with setAttribute for maximum reliability
                    progressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: 0%; position: absolute; left: 0; top: 0;`);
                    progressPercent.textContent = formattedText;

                    console.log(`Initialized previous progress bar: ${formattedText}`);

                    // Start a simulated progress animation to show activity
                    startSimulatedProgress('previous', totalPages);
                  } else {
                    console.error('Previous progress elements not found for initialization');
                  }
                } else if (extractingCurrent) {
                  console.log(`Initializing current progress bar with ${totalPages} pages`);

                  // Initialize using direct DOM manipulation
                  const progressBar = document.getElementById('current-progress-bar');
                  const progressPercent = document.getElementById('current-progress-percent');

                  if (progressBar && progressPercent) {
                    // Store the total pages
                    window.totalCurrentPages = totalPages;

                    // Update the DOM directly with setAttribute for maximum reliability
                    progressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: 0%; position: absolute; left: 0; top: 0;`);
                    progressPercent.textContent = formattedText;

                    console.log(`Initialized current progress bar: ${formattedText}`);

                    // Start a simulated progress animation to show activity
                    startSimulatedProgress('current', totalPages);
                  } else {
                    console.error('Current progress elements not found for initialization');
                  }
                }
              } catch (error) {
                console.error('Error initializing progress bar:', error);
              }
            }, 100); // Small delay to ensure DOM is ready
          }
        }

        // Auto-scroll to bottom
        processingDetailsBox.scrollTop = processingDetailsBox.scrollHeight;

        // Force a repaint of all progress elements to ensure they update visually
        processingDetailsBox.offsetHeight;

        // Force update of progress bars to ensure they're in sync with the job
        const previousProgressBar = document.getElementById('previous-progress-bar');
        const currentProgressBar = document.getElementById('current-progress-bar');
        if (previousProgressBar) previousProgressBar.offsetHeight;
        if (currentProgressBar) currentProgressBar.offsetHeight;
      } else {
        console.error('Processing details box not found!');
      }

      // Track extraction progress for previous payslips
      if (message.includes('Extracting text from previous payroll file')) {
        window.extractingPrevious = true;
        window.extractingCurrent = false;
        window.parsingStarted = false;
        window.comparisonStarted = false;
        window.reportingStarted = false;

        // Reset the progress display
        const previousProgressBar = document.getElementById('previous-progress-bar');
        const previousProgressPercent = document.getElementById('previous-progress-percent');
        if (previousProgressBar && previousProgressPercent) {
          previousProgressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: 0%; position: absolute; left: 0; top: 0;`);
          previousProgressPercent.textContent = '000/2946 (0%)';
        }
      } else if (message.includes('Extracting text from current payroll file')) {
        window.extractingPrevious = false;
        window.extractingCurrent = true;
        window.parsingStarted = false;
        window.comparisonStarted = false;
        window.reportingStarted = false;

        // Reset the progress display
        const currentProgressBar = document.getElementById('current-progress-bar');
        const currentProgressPercent = document.getElementById('current-progress-percent');
        if (currentProgressBar && currentProgressPercent) {
          currentProgressBar.setAttribute('style', `background-color: #4caf50; height: 100%; width: 0%; position: absolute; left: 0; top: 0;`);
          currentProgressPercent.textContent = '000/2946 (0%)';
        }
      }

      // Get total pages - this is now handled directly in the message processing section
      const totalPagesMatch = message.match(/Total pages in PDF: (\d+)/);
      if (totalPagesMatch) {
        // This is now handled in the message processing section
        // We keep this check for compatibility with other code that might depend on it
        if (window.extractingPrevious) {
          window.totalPreviousPages = parseInt(totalPagesMatch[1]);
        } else if (window.extractingCurrent) {
          window.totalCurrentPages = parseInt(totalPagesMatch[1]);
        }
      }

      // Get current page being processed - this is now handled directly in the message processing section
      const pageMatch = message.match(/Processing page (\d+)\/(\d+)/);
      if (pageMatch) {
        const currentPage = parseInt(pageMatch[1]);
        const totalPages = parseInt(pageMatch[2]);

        if (window.extractingPrevious) {
          // Update the status message with more detailed information
          const percent = Math.floor((currentPage / window.totalPreviousPages) * 100);
          const statusMessage = `Extracting previous payslips (${currentPage}/${window.totalPreviousPages}) - ${percent}% complete`;
          updateProgressVisualization('extraction', 'active', statusMessage);

          // Update main progress bar to reflect extraction progress (25% of total process)
          const progressBar = document.getElementById('progress-bar');
          if (progressBar) {
            // Update more frequently to match actual progress
            // Calculate a more accurate percentage based on actual progress
            const overallPercent = Math.floor((percent * 0.125)); // 12.5% of total for previous extraction
            requestAnimationFrame(() => {
              progressBar.style.width = `${overallPercent}%`;
              // Force a repaint
              progressBar.offsetHeight;
            });
          }
        } else if (window.extractingCurrent) {
          // Update the status message with more detailed information
          const percent = Math.floor((currentPage / window.totalCurrentPages) * 100);
          const statusMessage = `Extracting current payslips (${currentPage}/${window.totalCurrentPages}) - ${percent}% complete`;
          updateProgressVisualization('extraction', 'active', statusMessage);

          // Update main progress bar to reflect extraction progress (25% of total process)
          const progressBar = document.getElementById('progress-bar');
          if (progressBar) {
            // Update more frequently to match actual progress
            // Calculate a more accurate percentage based on actual progress
            const overallPercent = Math.floor(12.5 + (percent * 0.125)); // 12.5-25% of total for current extraction
            requestAnimationFrame(() => {
              progressBar.style.width = `${overallPercent}%`;
              // Force a repaint
              progressBar.offsetHeight;
            });
          }
        }
      }

      // Track parsing progress
      const processingPayslipMatch = message.match(/Processing payslip (\d+)\/(\d+)/);
      if (processingPayslipMatch) {
        parsingStarted = true;
        currentEmployee = parseInt(processingPayslipMatch[1]);
        totalEmployees = parseInt(processingPayslipMatch[2]);

        const percent = Math.floor((currentEmployee / totalEmployees) * 100);

        // Update the status message with more detailed information
        const statusMessage = `Parsing payslips (${currentEmployee}/${totalEmployees}) - ${percent}% complete`;
        updateProgressVisualization('parsing', 'active', statusMessage);

        // Update main progress bar to reflect parsing progress (25% of total process)
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          // Update more frequently to match actual progress
          // Calculate a more accurate percentage based on actual progress
          const overallPercent = Math.floor(25 + (percent * 0.25)); // 25-50% of total for parsing
          requestAnimationFrame(() => {
            progressBar.style.width = `${overallPercent}%`;
            // Force a repaint
            progressBar.offsetHeight;
          });
        }
      }

      // Track comparison progress
      const comparingEmployeeMatch = message.match(/Comparing employee (\d+)\/(\d+)/);
      if (comparingEmployeeMatch) {
        comparisonStarted = true;
        currentEmployee = parseInt(comparingEmployeeMatch[1]);
        totalEmployees = parseInt(comparingEmployeeMatch[2]);

        const percent = Math.floor((currentEmployee / totalEmployees) * 100);

        // Update the status message with more detailed information
        const statusMessage = `Pre-Auditing payslips (${currentEmployee}/${totalEmployees}) - ${percent}% complete`;
        updateProgressVisualization('comparison', 'active', statusMessage);

        // Update main progress bar to reflect comparison progress (25% of total process)
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          // Update more frequently to match actual progress
          // Calculate a more accurate percentage based on actual progress
          const overallPercent = Math.floor(50 + (percent * 0.25)); // 50-75% of total for comparison
          requestAnimationFrame(() => {
            progressBar.style.width = `${overallPercent}%`;
            // Force a repaint
            progressBar.offsetHeight;
          });
        }
      }

      // Update progress visualization based on message content
      if (message.includes('Extracting') && !parsingStarted && !comparisonStarted && !reportingStarted) {
        updateProgressVisualization('extraction', 'active', message);
      } else if (message.includes('Parsing') || (parsingStarted && !comparisonStarted && !reportingStarted)) {
        // Set both progress bars to 100% when extraction is complete
        const previousProgressBar = document.getElementById('previous-progress-bar');
        const previousProgressPercent = document.getElementById('previous-progress-percent');
        const currentProgressBar = document.getElementById('current-progress-bar');
        const currentProgressPercent = document.getElementById('current-progress-percent');

        // Use our direct update function for consistency
        if (totalPreviousPages > 0) {
          // Update the progress bar to 100%
          updateProgressBar('previous', totalPreviousPages, totalPreviousPages);
          // Ensure it stays at 100% by setting a permanent style
          if (previousProgressBar) {
            previousProgressBar.setAttribute('style', 'background-color: #4caf50; height: 100%; width: 100%; position: absolute; left: 0; top: 0;');
          }
          console.log('Set previous progress bar to 100% complete');
        }

        if (totalCurrentPages > 0) {
          // Update the progress bar to 100%
          updateProgressBar('current', totalCurrentPages, totalCurrentPages);
          // Ensure it stays at 100% by setting a permanent style
          if (currentProgressBar) {
            currentProgressBar.setAttribute('style', 'background-color: #4caf50; height: 100%; width: 100%; position: absolute; left: 0; top: 0;');
          }
          console.log('Set current progress bar to 100% complete');
        }

        updateProgressVisualization('extraction', 'completed', 'Extraction completed.');
        updateProgressVisualization('parsing', 'active', message);
      } else if (message.includes('Comparing') || (comparisonStarted && !reportingStarted)) {
        updateProgressVisualization('parsing', 'completed', 'Parsing completed.');
        updateProgressVisualization('comparison', 'active', message.replace('Comparing', 'Pre-Auditing'));
      } else if (message.includes('Generating report')) {
        reportingStarted = true;
        updateProgressVisualization('comparison', 'completed', 'Pre-Auditing completed.');
        updateProgressVisualization('reporting', 'active', message);

        // Update main progress bar to reflect reporting progress (75-90% of total process)
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          // Start at 75% and gradually increase to 90% during report generation
          const startTime = Date.now();
          const updateInterval = setInterval(() => {
            const elapsedTime = Date.now() - startTime;
            // Assume report generation takes about 5 seconds
            const reportProgress = Math.min(elapsedTime / 5000, 1);
            const overallPercent = Math.floor(75 + (reportProgress * 15)); // 75-90% for reporting
            progressBar.style.width = `${overallPercent}%`;

            // Update status message with more detailed information
            const reportPercent = Math.floor(reportProgress * 100);
            const statusMessage = `Generating report - ${reportPercent}% complete`;
            updateProgressVisualization('reporting', 'active', statusMessage);

            // Clear interval when complete
            if (reportProgress >= 1) {
              clearInterval(updateInterval);
            }
          }, 200); // Update every 200ms for smooth animation

          // Store the interval ID to clear it if needed
          window.reportProgressInterval = updateInterval;
        }
      } else if (message.includes('Report generation complete')) {
        // Clear any existing report progress interval
        if (window.reportProgressInterval) {
          clearInterval(window.reportProgressInterval);
          window.reportProgressInterval = null;
        }

        // Update main progress bar to reflect report completion (90-95% of total process)
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          progressBar.style.width = '95%';

          // Update status message with more detailed information
          const statusMessage = `Report generation complete - finalizing`;
          updateProgressVisualization('reporting', 'active', statusMessage);
        }
      } else if (message.includes('Done')) {
        // Clear any existing report progress interval
        if (window.reportProgressInterval) {
          clearInterval(window.reportProgressInterval);
          window.reportProgressInterval = null;
        }

        // Complete the progress visualization
        completeProgressVisualization();

        // Update the progress bar to 100%
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          progressBar.style.width = '100%';
        }
      }
    });
  }

  // Listen for direct progress updates
  window.api.onDirectProgressUpdate((data) => {
    console.log(`Direct progress update received: ${data.current}/${data.total}`);

    // Update the main progress bar immediately
    const mainProgressBar = document.getElementById('main-progress-bar');
    const mainProgressPercent = document.getElementById('main-progress-percent');
    const mainProgressCount = document.getElementById('main-progress-count');

    console.log('Progress bar elements:');
    console.log('- mainProgressBar:', mainProgressBar);
    console.log('- mainProgressPercent:', mainProgressPercent);
    console.log('- mainProgressCount:', mainProgressCount);

    if (mainProgressBar && mainProgressPercent && mainProgressCount) {
      // Calculate percentage
      const percent = Math.floor((data.current / data.total) * 100);

      // Update the progress bar
      mainProgressBar.style.width = `${percent}%`;
      mainProgressPercent.textContent = `${percent}%`;
      mainProgressCount.textContent = `(${data.current}/${data.total})`;

      console.log(`Updated progress bar to ${percent}% (${data.current}/${data.total})`);
    } else {
      console.log('Main progress elements not found for direct update');

      // Try to find the elements by querying the DOM directly
      const progressBarContainer = document.querySelector('.progress-container');
      console.log('Progress bar container:', progressBarContainer);

      if (progressBarContainer) {
        const allElements = progressBarContainer.querySelectorAll('*');
        console.log('All elements in progress container:', allElements.length);
        allElements.forEach((el, i) => {
          console.log(`Element ${i}:`, el.tagName, el.className, el.id);
        });
      }
    }
  });

  // Function to save a report to the Report Manager
  async function saveToReportManager(filePath, fileType) {
    console.log(`Saving report to Report Manager: ${filePath} (${fileType})`);

    // First, check if the file exists
    try {
      const fileExists = await window.api.checkFileExists(filePath);
      if (!fileExists) {
        console.error(`File does not exist: ${filePath}`);

        // For Word reports, try with different extensions
        if (fileType === 'Word') {
          // Try both .docx and .doc extensions
          let alternateFilePath = '';
          if (filePath.toLowerCase().endsWith('.doc')) {
            alternateFilePath = filePath.replace(/\.doc$/i, '.docx');
          } else if (filePath.toLowerCase().endsWith('.docx')) {
            alternateFilePath = filePath.replace(/\.docx$/i, '.doc');
          }

          if (alternateFilePath) {
            const alternateExists = await window.api.checkFileExists(alternateFilePath);
            if (alternateExists) {
              console.log(`Found alternate file: ${alternateFilePath}`);
              filePath = alternateFilePath;
            } else {
              console.error(`Alternate file does not exist: ${alternateFilePath}`);
              return; // Don't add to Report Manager if file doesn't exist
            }
          } else {
            return; // Don't add to Report Manager if file doesn't exist
          }
        } else {
          return; // Don't add to Report Manager if file doesn't exist
        }
      }
    } catch (error) {
      console.error(`Error checking if file exists: ${error.message}`);
      return; // Don't add to Report Manager if there's an error
    }

    // Get the current date and time
    const now = new Date();
    const dateStr = now.toLocaleDateString();
    const timeStr = now.toLocaleTimeString();

    // Get the file name from the path
    const fileName = filePath.split('\\').pop();

    // Create a new report entry
    const reportEntry = document.createElement('div');
    reportEntry.className = 'report-entry';

    // Create the report info section
    const reportInfo = document.createElement('div');
    reportInfo.className = 'report-info';

    // Add the report type badge
    const reportType = document.createElement('div');
    reportType.className = `report-type ${fileType.toLowerCase()}-type`;
    reportType.textContent = fileType;
    reportInfo.appendChild(reportType);

    // Add the report name
    const reportName = document.createElement('div');
    reportName.className = 'report-name';
    reportName.textContent = fileName;
    reportInfo.appendChild(reportName);

    // Add the report date
    const reportDate = document.createElement('div');
    reportDate.className = 'report-date';
    reportDate.textContent = `Created on ${dateStr} at ${timeStr}`;
    reportInfo.appendChild(reportDate);

    // Add the report info to the entry
    reportEntry.appendChild(reportInfo);

    // Create the report actions section
    const reportActions = document.createElement('div');
    reportActions.className = 'report-actions';

    // Add the open button
    const openBtn = document.createElement('button');
    openBtn.className = 'report-open-btn';
    openBtn.innerHTML = '<i class="fas fa-external-link-alt"></i> Open';
    openBtn.addEventListener('click', () => {
      window.api.openFile(filePath);
    });
    reportActions.appendChild(openBtn);

    // Add the download button
    const downloadBtn = document.createElement('button');
    downloadBtn.className = 'report-download-btn';
    downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download';
    downloadBtn.addEventListener('click', async () => {
      try {
        // Ask the user where to save the file
        const savePath = await window.api.saveFileDialog({
          defaultPath: fileName,
          filters: [
            { name: 'All Files', extensions: ['*'] }
          ]
        });

        if (savePath) {
          // Copy the file to the selected location
          const success = await window.api.copyFile(filePath, savePath);
          if (success) {
            console.log(`File successfully downloaded to: ${savePath}`);
            alert(`File successfully downloaded to: ${savePath}`);
          } else {
            console.error(`Failed to download file to: ${savePath}`);
            alert(`Failed to download file. Please try again.`);
          }
        }
      } catch (error) {
        console.error('Error downloading file:', error);
        alert(`Error downloading file: ${error.message}`);
      }
    });
    reportActions.appendChild(downloadBtn);

    // Add the delete button
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'report-delete-btn';
    deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Delete';
    deleteBtn.addEventListener('click', async () => {
      try {
        // Confirm before deleting
        if (confirm(`Are you sure you want to delete this file?\n${fileName}`)) {
          // Delete the file
          const success = await window.api.deleteFile(filePath);
          if (success) {
            console.log(`File successfully deleted: ${filePath}`);
            // Remove the report entry from the UI
            reportEntry.remove();

            // Check if the list is now empty and add a "No reports available" message if needed
            const parentList = reportEntry.parentElement;
            if (parentList && parentList.children.length === 0) {
              let noReportsMsg = document.createElement('p');

              if (parentList.id === 'comparison-reports-list') {
                noReportsMsg.textContent = 'No payroll audit reports available yet.';
              } else if (parentList.id === 'sorted-pdf-list') {
                noReportsMsg.textContent = 'No sorted PDF files available yet.';
              } else if (parentList.id === 'data-tables-list') {
                noReportsMsg.textContent = 'No data tables available yet.';
              }

              parentList.appendChild(noReportsMsg);
            }
          } else {
            console.error(`Failed to delete file: ${filePath}`);
            alert(`Failed to delete file. Please try again.`);
          }
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        alert(`Error deleting file: ${error.message}`);
      }
    });
    reportActions.appendChild(deleteBtn);

    // Add the actions to the entry
    reportEntry.appendChild(reportActions);

    // Determine which list to add the report to based on file path and name
    let targetList;

    // Check if the file path contains the directory structure we created
    if (filePath.includes('payroll_audit_reports') || fileName.includes('audit')) {
      // This is a payroll audit report
      targetList = document.getElementById('comparison-reports-list');
    } else if (filePath.includes('sorted_pdfs') || fileName.includes('sorted') || fileName.toLowerCase().endsWith('.pdf')) {
      // This is a sorted PDF
      targetList = document.getElementById('sorted-pdf-list');
    } else if (filePath.includes('data_tables') || fileName.includes('data') || fileName.toLowerCase().endsWith('.xlsx')) {
      // This is a data table
      targetList = document.getElementById('data-tables-list');
    }

    if (targetList) {
      // Remove the "No reports available" message if it exists
      const noReportsMsg = targetList.querySelector('p');
      if (noReportsMsg && (
          noReportsMsg.textContent === 'No payroll audit reports available yet.' ||
          noReportsMsg.textContent === 'No sorted PDF files available yet.' ||
          noReportsMsg.textContent === 'No data tables available yet.'
      )) {
        noReportsMsg.remove();
      }

      // Add the entry to the list
      targetList.insertBefore(reportEntry, targetList.firstChild);
    }
  }

  // Function to load existing reports from the reports directory
  async function loadExistingReports() {
    try {
      console.log('Loading existing reports...');

      // Get the app directory path
      const appDir = await window.api.getAppDir();
      console.log('App directory:', appDir);

      // Define the reports directory paths
      const reportsDir = `${appDir}\\backend-dist\\reports`;
      const payrollAuditDir = `${reportsDir}\\payroll_audit_reports`;
      const sortedPdfsDir = `${reportsDir}\\sorted_pdfs`;
      const dataTablesDir = `${reportsDir}\\data_tables`;

      console.log('Checking for reports in:', payrollAuditDir, sortedPdfsDir, dataTablesDir);

      // Check if the directories exist
      const reportsDirExists = await window.api.checkFileExists(reportsDir);
      if (!reportsDirExists) {
        console.log('Reports directory does not exist yet');
        return;
      }

      // Load Payroll Audit Reports
      if (await window.api.checkFileExists(payrollAuditDir)) {
        const payrollAuditFiles = await window.api.listFiles(payrollAuditDir);
        console.log('Found Payroll Audit Reports:', payrollAuditFiles);

        for (const file of payrollAuditFiles) {
          const filePath = `${payrollAuditDir}\\${file}`;
          let fileType = 'Unknown';

          // Determine file type based on extension
          if (file.toLowerCase().endsWith('.xlsx')) {
            fileType = 'Excel';
          } else if (file.toLowerCase().endsWith('.docx') || file.toLowerCase().endsWith('.doc')) {
            fileType = 'Word';
          } else if (file.toLowerCase().endsWith('.pdf')) {
            fileType = 'PDF';
          } else if (file.toLowerCase().endsWith('.csv')) {
            fileType = 'CSV';
          } else if (file.toLowerCase().endsWith('.json')) {
            fileType = 'JSON';
          }

          // Only add visible report types (Excel, Word, PDF)
          if (['Excel', 'Word', 'PDF'].includes(fileType)) {
            saveToReportManager(filePath, fileType);
          }
        }
      }

      // Load Sorted PDFs
      if (await window.api.checkFileExists(sortedPdfsDir)) {
        const sortedPdfFiles = await window.api.listFiles(sortedPdfsDir);
        console.log('Found Sorted PDFs:', sortedPdfFiles);

        for (const file of sortedPdfFiles) {
          const filePath = `${sortedPdfsDir}\\${file}`;
          saveToReportManager(filePath, 'PDF');
        }
      }

      // Load Data Tables
      if (await window.api.checkFileExists(dataTablesDir)) {
        const dataTableFiles = await window.api.listFiles(dataTablesDir);
        console.log('Found Data Tables:', dataTableFiles);

        for (const file of dataTableFiles) {
          const filePath = `${dataTablesDir}\\${file}`;
          saveToReportManager(filePath, 'Excel');
        }
      }

      console.log('Finished loading existing reports');
    } catch (error) {
      console.error('Error loading existing reports:', error);
    }
  }

  // Initialize the UI
  showContent(uploadContent);

  // Load existing reports
  loadExistingReports();

  // Force refresh of styles by adding and removing a class
  document.addEventListener('DOMContentLoaded', () => {
    // Add a temporary class to the body
    document.body.classList.add('style-refresh');

    // Remove it after a short delay to force a repaint
    setTimeout(() => {
      document.body.classList.remove('style-refresh');
      console.log('Forced style refresh');
    }, 100);
  });
});
