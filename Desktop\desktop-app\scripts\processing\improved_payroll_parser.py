import re
import os
import json
import sys
from datetime import datetime
import argparse

# Define standard section names to match the Dictionary Manager
SECTION_PERSONAL_DETAILS = "PERSONAL DETAILS"
SECTION_EARNINGS = "EARNINGS"
SECTION_DEDUCTIONS = "DEDUCTIONS"
SECTION_EMPLOYERS_CONTRIBUTION = "EMPLOYERS CONTRIBUTION"
SECTION_LOANS = "LOANS"
SECTION_EMPLOYEE_BANK_DETAILS = "EMPLOYEE BANK DETAILS"

# HARDCODED EXCLUSION LIST - These items must NEVER appear in extraction or reports
# This is a strict requirement per user specifications
HARDCODED_EXCLUSIONS = [
    "NET PAY",
    "GRO<PERSON> SALARY",
    "INCOME TAX",
    "BASIC SALARY"
]

def is_excluded_item(item_name):
    """
    Check if an item is in the hardcoded exclusion list.

    Args:
        item_name: The item name to check

    Returns:
        True if the item should be excluded, False otherwise
    """
    if not item_name:
        return False

    item_upper = item_name.upper().strip()

    # Check exact matches first
    for excluded_item in HARDCODED_EXCLUSIONS:
        if item_upper == excluded_item.upper():
            print(f"EXCLUSION: Hardcoded exclusion applied to '{item_name}' - exact match with '{excluded_item}'")
            return True

    # Check partial matches for variations
    for excluded_item in HARDCODED_EXCLUSIONS:
        excluded_upper = excluded_item.upper()
        if excluded_upper in item_upper or item_upper in excluded_upper:
            print(f"EXCLUSION: Hardcoded exclusion applied to '{item_name}' - partial match with '{excluded_item}'")
            return True

    return False

# Disable output buffering to ensure immediate progress updates
sys.stdout.reconfigure(line_buffering=True)

# Print startup message immediately to show progress
print("Starting payroll parser...", flush=True)
print("Initializing modules...", flush=True)
print("Progress: 0/100", flush=True)

# Define total process steps for better progress tracking
TOTAL_PROCESS_STEPS = 3  # Extraction, Parsing, Pre-Auditing
CURRENT_PROCESS_STEP = 0

def update_process_phase(phase_name, step_number):
    """Update the current process phase for progress tracking"""
    global CURRENT_PROCESS_STEP
    CURRENT_PROCESS_STEP = step_number
    print(f"PHASE_CHANGE: {phase_name} ({step_number}/{TOTAL_PROCESS_STEPS})", flush=True)
    print(f"Progress: {step_number}/{TOTAL_PROCESS_STEPS}", flush=True)

# Lazy imports to improve startup time
def import_modules():
    global PyPDF2, pdfplumber, pd, fitz, cv2, np, Image, io, pytesseract, generate_reports, generate_improved_reports

    print("Importing core modules...")
    import pandas as pd

    print("Importing PDF processing modules...")
    import PyPDF2
    import pdfplumber

    print("Importing advanced processing modules...")
    import fitz  # PyMuPDF
    import cv2
    import numpy as np
    from PIL import Image
    import io
    import pytesseract

    print("Importing report generators...")
    from payroll_report_generator import generate_reports
    from improved_report_generator import generate_improved_reports

    # Import enhanced dictionary for standardization
    global enhanced_payroll_dictionaries
    try:
        import enhanced_payroll_dictionaries
        print("Imported enhanced payroll dictionary for standardization")

        # Verify dictionary is loaded correctly
        dictionary = enhanced_payroll_dictionaries.load_dictionary()

        # Count items in each section
        earnings_count = len(dictionary.get("EARNINGS", {}).get("items", {}))
        deductions_count = len(dictionary.get("DEDUCTIONS", {}).get("items", {}))
        loans_count = len(dictionary.get("LOANS", {}).get("items", {}))

        print(f"Loaded dictionary - Earnings: {earnings_count} items, Deductions: {deductions_count} items, Loans: {loans_count} items")

        # Print some keys for verification
        if "EARNINGS" in dictionary and "items" in dictionary["EARNINGS"]:
            print(f"Earnings dictionary keys: {list(dictionary['EARNINGS']['items'].keys())[:5]}...")
        if "DEDUCTIONS" in dictionary and "items" in dictionary["DEDUCTIONS"]:
            print(f"Deductions dictionary keys: {list(dictionary['DEDUCTIONS']['items'].keys())[:5]}...")
        if "LOANS" in dictionary and "items" in dictionary["LOANS"]:
            print(f"Loans dictionary keys: {list(dictionary['LOANS']['items'].keys())[:5]}...")
    except ImportError as e:
        print(f"Warning: enhanced_payroll_dictionaries module not found. Standardization will be disabled. Error: {e}")
        enhanced_payroll_dictionaries = None
    except Exception as e:
        print(f"Error loading dictionary: {e}")
        enhanced_payroll_dictionaries = None

    print("All modules imported successfully.")

# Don't import modules yet - wait until they're needed

def extract_payslips_from_pdf(pdf_path):
    """Extract individual payslips from a multi-page PDF, treating each page as a potential payslip."""
    print(f"Extracting payslips from: {pdf_path}")

    # Update process phase to Extraction (Phase 1)
    update_process_phase("EXTRACTION", 1)

    payslips = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"Total pages in PDF: {total_pages}")

            # Process pages in batches to avoid memory issues
            batch_size = 25  # Reduced batch size to prevent freezing
            for batch_start in range(0, total_pages, batch_size):
                batch_end = min(batch_start + batch_size, total_pages)
                print(f"Processing pages {batch_start+1} to {batch_end}...")

                for i in range(batch_start, batch_end):
                    try:
                        page = pdf.pages[i]
                        page_text = page.extract_text() or ""

                        # Only add pages that have substantial text and look like payslips
                        if len(page_text.strip()) > 100 and is_likely_payslip(page_text):
                            payslips.append({
                                "text": page_text,
                                "page_num": i + 1
                            })

                        # Print progress for every page for UI updates
                        # Print progress for every page to ensure immediate UI updates
                        print(f"Processing page {i+1}/{total_pages}", flush=True)
                        print(f"Progress: {i+1}/{total_pages}", flush=True)
                    except Exception as page_error:
                        print(f"Error processing page {i+1}: {page_error}")
                        continue  # Skip this page and continue with the next one

    except Exception as e:
        print(f"Error extracting payslips with pdfplumber: {e}")
        # Fallback to PyPDF2
        try:
            print("Falling back to PyPDF2...")
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                total_pages = len(reader.pages)
                print(f"Total pages in PDF: {total_pages}")

                # Process pages in batches to avoid memory issues
                batch_size = 25  # Reduced batch size to prevent freezing
                for batch_start in range(0, total_pages, batch_size):
                    batch_end = min(batch_start + batch_size, total_pages)
                    print(f"Processing pages {batch_start+1} to {batch_end}...")

                    for i in range(batch_start, batch_end):
                        try:
                            page = reader.pages[i]
                            page_text = page.extract_text() or ""

                            # Only add pages that have substantial text and look like payslips
                            if len(page_text.strip()) > 100 and is_likely_payslip(page_text):
                                payslips.append({
                                    "text": page_text,
                                    "page_num": i + 1
                                })

                            # Print progress for every page for UI updates
                            # Print progress for every page to ensure immediate UI updates
                            print(f"Processing page {i+1}/{total_pages}", flush=True)
                            print(f"Progress: {i+1}/{total_pages}", flush=True)
                        except Exception as page_error:
                            print(f"Error processing page {i+1}: {page_error}")
                            continue  # Skip this page and continue with the next one
        except Exception as e2:
            print(f"Error extracting payslips with PyPDF2: {e2}")

    print(f"Found {len(payslips)} individual payslips")
    return payslips

def is_likely_payslip(text):
    """Determine if the text is likely to be a payslip based on key patterns."""
    # Key patterns that indicate a payslip
    payslip_indicators = [
        r"PAYSLIP",
        r"Employee Name",
        r"Employee ID",
        r"SSF No",
        r"Ghana Card ID",
        r"BASIC SALARY",
        r"GROSS SALARY",
        r"NET PAY",
        r"DEDUCTIONS",
        r"EARNINGS"
    ]

    # Count how many indicators are present
    indicator_count = 0
    for pattern in payslip_indicators:
        if re.search(pattern, text, re.IGNORECASE):
            indicator_count += 1

    # If at least 3 indicators are present, it's likely a payslip
    return indicator_count >= 3

def identify_payslip_sections(text):
    """Identify individual payslip sections in the text."""
    print("Identifying individual payslip sections...")

    # Common patterns that indicate the start of a new payslip
    payslip_start_patterns = [
        r"THE CHURCH OF PENTECOST.*?Period.*?20\d{2}",
        r"Employee Name.*?SSF No",
        r"EMPLOYEE BANK DETAILS",
        r"AMT \(GHS\) DEDUCTIONS AMT \(GHS\) EARNINGS"
    ]

    # Split the text into lines
    lines = text.split('\n')

    # Initialize variables
    payslips = []
    current_payslip = []
    payslip_count = 0

    for line in lines:
        # Check if this line indicates the start of a new payslip
        is_new_payslip = False
        for pattern in payslip_start_patterns:
            if re.search(pattern, line, re.IGNORECASE) and "Printed" in line:
                is_new_payslip = True

        # If we found a new payslip and we have content in the current one
        if is_new_payslip and current_payslip:
            payslip_text = '\n'.join(current_payslip)
            payslips.append(payslip_text)
            current_payslip = []
            payslip_count += 1
            if payslip_count % 10 == 0:
                print(f"  Identified {payslip_count} payslips so far")

        # Add the line to the current payslip
        current_payslip.append(line)

    # Add the last payslip if there's content
    if current_payslip:
        payslip_text = '\n'.join(current_payslip)
        payslips.append(payslip_text)
        payslip_count += 1

    print(f"Total payslips identified: {payslip_count}")

    # Alternative approach: split by "Akatua by theSOFTtribe" which appears to be at the end of each payslip
    if payslip_count < 10:
        print("Few payslips found. Trying alternative approach...")
        payslips = re.split(r"Akatua by theSOFTtribe.*?Printed", text)
        print(f"Alternative approach found {len(payslips)} potential payslips")

    return payslips

def extract_employee_id_with_layout(pdf_path, page_num):
    """
    Extract Employee No. using advanced layout detection techniques.
    This function uses PyMuPDF to analyze the PDF layout and extract the Employee No.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to extract from (1-based)

    Returns:
        The extracted Employee No. or None if not found
    """
    print(f"Attempting to extract Employee No. using advanced layout detection for page {page_num}...")

    try:
        # Open the PDF with PyMuPDF
        doc = fitz.open(pdf_path)
        page = doc[page_num - 1]  # Convert to 0-based index

        # Get the page text with detailed layout information
        blocks = page.get_text("dict")["blocks"]

        # Look for blocks containing "Employee No." or similar
        employee_id = None

        # First, look specifically for the Employee No. formats: COP####, PW####, SEC####, E####
        # These are often found at the top left of the payslip
        for block in blocks:
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]

                    # Look for specific Employee No. formats
                    id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', line_text)
                    if id_candidates:
                        employee_id = id_candidates[0]
                        print(f"Found Employee No. in line: {employee_id}")



        # If not found, look for blocks containing "Employee No." or similar
        if not employee_id:
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]

                        # Check if this line contains "Employee No."
                        if re.search(r"Employee\s*No\.?", line_text, re.IGNORECASE):
                            print(f"Found 'Employee No.' in line: {line_text}")

                            # Extract the ID from this line
                            id_match = re.search(r"([A-Z0-9-]{3,15})\s+Employee\s*No\.?", line_text)
                            if id_match:
                                employee_id = id_match.group(1).strip()
                                print(f"Extracted Employee No.: {employee_id}")

                            # If not found in the same line, look at nearby lines
                            # Get the position of this line
                            line_y = line["bbox"][1]  # Top y-coordinate

                            # Look for text that could be an ID in nearby lines
                            for other_block in blocks:
                                if "lines" in other_block:
                                    for other_line in other_block["lines"]:
                                        other_y = other_line["bbox"][1]
                                        # Check if the line is close to the "Employee No." line
                                        if abs(other_y - line_y) < 20:
                                            other_text = ""
                                            for span in other_line["spans"]:
                                                other_text += span["text"]

                                            # Look for specific Employee No. formats
                                            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', other_text)
                                            if id_candidates:
                                                employee_id = id_candidates[0]
                                                print(f"Found Employee No. in nearby line: {employee_id}")



        # If we still don't have an ID, try OCR on the page
        if not employee_id:
            print("Trying OCR to extract Employee No...")
            # Render the page to an image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))

            # Convert to numpy array for OpenCV
            img_np = np.array(img)

            # Convert to grayscale
            gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)

            # Apply thresholding
            _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Focus on the top-left portion of the page where Employee No. is typically found
            # Sort contours by position (top to bottom, left to right)
            contours = sorted(contours, key=lambda c: (cv2.boundingRect(c)[1], cv2.boundingRect(c)[0]))

            # First, look for specific Employee No. formats in the entire image
            full_text = pytesseract.image_to_string(gray)
            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', full_text)
            if id_candidates:
                employee_id = id_candidates[0]
                print(f"Found Employee No. using OCR on full page: {employee_id}")
            else:
                # Look for text regions that might contain "Employee No."
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    # Focus on the top portion of the page
                    if y < img_np.shape[0] / 3 and w > 50 and h > 10:  # Top third of page, filter out small regions
                        # Extract the region
                        roi = gray[y:y+h, x:x+w]
                        # Apply OCR
                        text = pytesseract.image_to_string(roi)

                        # Look for specific Employee No. formats
                        id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', text)
                        if id_candidates:
                            employee_id = id_candidates[0]
                            print(f"Found Employee No. using OCR in region: {employee_id}")

                        # Check if this region contains Employee No. related text
                        if re.search(r'employee|no\.?', text.lower()):
                            # Look for Employee No. pattern in this region and nearby regions
                            expanded_roi = gray[max(0, y-20):min(gray.shape[0], y+h+20),
                                               max(0, x-20):min(gray.shape[1], x+w+100)]
                            expanded_text = pytesseract.image_to_string(expanded_roi)

                            # Look for specific Employee No. formats
                            id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', expanded_text)
                            if id_candidates:
                                employee_id = id_candidates[0]
                                print(f"Found Employee No. using OCR in expanded region: {employee_id}")

        doc.close()
        return employee_id

    except Exception as e:
        print(f"Error in advanced layout detection: {str(e)}")
        return None

def extract_department_advanced_layout(pdf_path, page_num):
    """
    Extract Department from a payslip using advanced layout detection.
    This function uses PyMuPDF and OpenCV to analyze the PDF structure more precisely,
    focusing specifically on finding the department which is on the same line as Employee Name.

    Args:
        pdf_path: Path to the PDF file
        page_num: Page number to extract from (1-based)

    Returns:
        The extracted Department or None if not found
    """
    print(f"Attempting to extract Department using advanced layout detection for page {page_num}...")

    try:
        # Open the PDF with PyMuPDF
        doc = fitz.open(pdf_path)
        page = doc[page_num - 1]  # Convert to 0-based index

        # Get the page text with detailed layout information
        blocks = page.get_text("dict")["blocks"]

        # Look for blocks containing "Employee Name" or similar
        department = None

        # First, look for lines containing "Employee Name" - department is on the same line
        for block in blocks:
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]

                    # Check if this line contains "Employee Name"
                    if re.search(r"Employee\s*Name", line_text, re.IGNORECASE):
                        print(f"Found 'Employee Name' in line: {line_text}")

                        # Extract the department from this line
                        # Department typically follows after "Employee Name" and the actual name
                        # It's often preceded by "Department:" or similar
                        dept_match = re.search(r"Department:?\s*([A-Za-z0-9\s\-\.]+)", line_text, re.IGNORECASE)
                        if dept_match:
                            department = dept_match.group(1).strip()
                            print(f"Extracted Department from Employee Name line: {department}")
                            break

                        # If not found with explicit label, try to extract based on position
                        # Department is typically the third major element on the line
                        # Format: [Employee ID] [Name] [Department]
                        parts = line_text.split()
                        if len(parts) > 5:  # Enough parts to potentially contain department
                            # Find where "Employee Name" ends
                            name_end_idx = -1
                            for i, part in enumerate(parts):
                                if "Name" in part:
                                    # Skip the actual name (typically 2-3 words after "Name")
                                    name_end_idx = i + 3
                                    break

                            if name_end_idx > 0 and name_end_idx < len(parts) - 1:
                                # Department is likely everything after the name
                                potential_dept = ' '.join(parts[name_end_idx:])
                                # Clean up - remove any trailing fields
                                potential_dept = re.sub(r'(Section|SSF|Ghana).*$', '', potential_dept).strip()
                                department = potential_dept
                                print(f"Extracted Department based on position: {department}")
                                break

        # If not found in the same line, look for nearby lines that might contain "Department"
        if not department:
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = ""
                        for span in line["spans"]:
                            line_text += span["text"]

                        # Check if this line contains "Department"
                        if re.search(r"Department", line_text, re.IGNORECASE):
                            print(f"Found 'Department' in line: {line_text}")

                            # Extract the department from this line
                            dept_match = re.search(r"Department:?\s*([A-Za-z0-9\s\-\.]+)", line_text, re.IGNORECASE)
                            if dept_match:
                                department = dept_match.group(1).strip()
                                print(f"Extracted Department: {department}")
                                break

        # If we still don't have a department, try OCR on the page
        if not department:
            print("Trying OCR to extract Department...")
            # Render the page to an image
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x zoom for better OCR
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))

            # Convert to numpy array for OpenCV
            img_np = np.array(img)

            # Convert to grayscale
            gray = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)

            # Focus on the header region (top 20% of the page)
            header_height = int(img_np.shape[0] * 0.2)
            header_region = gray[0:header_height, :]

            # Apply OCR to the header region
            header_text = pytesseract.image_to_string(header_region)

            # Look for department in the header text
            dept_match = re.search(r"Department:?\s*([A-Za-z0-9\s\-\.]+)", header_text, re.IGNORECASE)
            if dept_match:
                department = dept_match.group(1).strip()
                print(f"Extracted Department using OCR on header region: {department}")
            else:
                # Look for "Employee Name" and extract department based on position
                emp_name_match = re.search(r"Employee\s*Name", header_text, re.IGNORECASE)
                if emp_name_match:
                    # Extract the line containing "Employee Name"
                    emp_name_line = header_text.split('\n')[emp_name_match.start() // len(header_text.split('\n')[0])]
                    parts = emp_name_line.split()
                    if len(parts) > 5:  # Enough parts to potentially contain department
                        # Find where "Employee Name" ends
                        name_end_idx = -1
                        for i, part in enumerate(parts):
                            if "Name" in part:
                                # Skip the actual name (typically 2-3 words after "Name")
                                name_end_idx = i + 3
                                break

                        if name_end_idx > 0 and name_end_idx < len(parts) - 1:
                            # Department is likely everything after the name
                            potential_dept = ' '.join(parts[name_end_idx:])
                            # Clean up - remove any trailing fields
                            potential_dept = re.sub(r'(Section|SSF|Ghana).*$', '', potential_dept).strip()
                            department = potential_dept
                            print(f"Extracted Department based on position using OCR: {department}")

        doc.close()
        return department

    except Exception as e:
        print(f"Error in advanced department detection: {str(e)}")
        return None

def extract_employee_data(payslip_text):
    """Extract employee data from a single payslip."""

    # Import enhanced dictionary for standardization
    global enhanced_payroll_dictionaries
    if 'enhanced_payroll_dictionaries' not in globals() or enhanced_payroll_dictionaries is None:
        try:
            import enhanced_payroll_dictionaries
            print("Imported enhanced payroll dictionary for standardization in extract_employee_data")

            # Verify dictionary is loaded correctly
            dictionary = enhanced_payroll_dictionaries.load_dictionary()

            # Count items in each section
            earnings_count = len(dictionary.get("EARNINGS", {}).get("items", {}))
            deductions_count = len(dictionary.get("DEDUCTIONS", {}).get("items", {}))
            loans_count = len(dictionary.get("LOANS", {}).get("items", {}))

            print(f"Loaded dictionary in extract_employee_data - Earnings: {earnings_count} items, Deductions: {deductions_count} items, Loans: {loans_count} items")

            # Print some keys for verification
            if "EARNINGS" in dictionary and "items" in dictionary["EARNINGS"]:
                print(f"Earnings dictionary keys: {list(dictionary['EARNINGS']['items'].keys())[:5]}...")
            if "DEDUCTIONS" in dictionary and "items" in dictionary["DEDUCTIONS"]:
                print(f"Deductions dictionary keys: {list(dictionary['DEDUCTIONS']['items'].keys())[:5]}...")
            if "LOANS" in dictionary and "items" in dictionary["LOANS"]:
                print(f"Loans dictionary keys: {list(dictionary['LOANS']['items'].keys())[:5]}...")
        except ImportError as e:
            print(f"Warning: enhanced_payroll_dictionaries module not found in extract_employee_data. Standardization will be disabled. Error: {e}")
            enhanced_payroll_dictionaries = None
        except Exception as e:
            print(f"Error loading dictionary in extract_employee_data: {e}")
            enhanced_payroll_dictionaries = None

    employee = {
        "employee_id": None,      # Primary identifier (default)
        "ssf_no": None,           # Alternative identifier
        "ghana_card_id": None,    # Alternative identifier
        "name": None,             # Alternative identifier
        "job_title": None,
        "department": None,       # Added for sorting/comparison
        "section": None,          # Added for sorting/comparison
        "basic_salary": None,
        "gross_salary": None,
        "net_pay": None,
        "deductions": {},         # Dictionary for easier comparison
        "earnings": {},           # Dictionary for easier comparison
        "month": None,
        "year": None,
        "loan": None,             # Added for loan information (legacy)
        "bank_details": None,     # Added for employee bank details
        "employer_contributions": {},  # Added for employer's contributions
        "loan_details": {},       # Dictionary for detailed loan information
        "personal_details": {},   # Dictionary for personal details from dictionary
        "personal_details_changed": False  # Flag for personal details changes
    }

    # SIMPLIFIED EMPLOYEE NAME EXTRACTION
    # Since employee names are always present in payslips, we'll focus on extracting them correctly

    # Define a list of organizational terms that should never be identified as employee names
    org_terms = [
        "THE CHURCH OF PENTECOST", "CHURCH OF PENTECOST", "PENTECOST",
        "AREA", "DISTRICT", "REGION", "DEPARTMENT", "SECTION",
        "MINISTERS", "HEADQUARTERS", "OFFICE", "MINISTRY",
        "ABUAKWA", "OBUASI"
    ]

    # Define a blacklist of terms that should never be identified as employee names
    # This fixes the undefined name_blacklist variable issue and adds more terms
    # IMPORTANT: We've significantly reduced this list based on user feedback
    name_blacklist = [
        # Only keep the most critical terms that should never be names
        "EMPLOYEE BANK DETAILS",
        "EARNINGS", "DEDUCTIONS", "LOANS",
        "EMPLOYEE INFORMATION", "PAYSLIP",

        # These exact terms should never be names
        "EMPLOYEE NO", "SSF NO", "GHANA CARD ID",
        "JOB TITLE", "BASIC SALARY", "GROSS SALARY", "NET PAY"
    ]

    # Look for the exact format where employee names are found in payslips
    # Based on user feedback: Names are ALL CAPS and appear on the same line as "Department:"
    # Format: "Employee Name [space] [ALL CAPS NAME] [large space] Department: [department name]"
    name_patterns = [
        # EXACT FORMAT MATCH: Employee Name followed by ALL CAPS name, then Department
        r"Employee Name\s+([A-Z][A-Z\s\.\-]{2,30})\s+Department",

        # Backup pattern: Employee Name followed by ALL CAPS name (more general)
        r"Employee Name\s+([A-Z][A-Z\s\.\-]{2,30})",

        # Backup pattern: Look for ALL CAPS text between Employee Name and Department
        r"Employee Name.*?([A-Z][A-Z\s\.\-]{2,30}).*?Department",

        # Fallback pattern: Look for ALL CAPS text near employee ID
        r"(?:Employee No\.?\s*" + re.escape(str(employee.get("employee_id", ""))) + r".*?|.*?Employee No\.?\s*" + re.escape(str(employee.get("employee_id", ""))) + r")\s+([A-Z][A-Z\s\.\-]{2,30})"
    ]

    # Try each name pattern
    name_match = None
    for pattern in name_patterns:
        name_match = re.search(pattern, payslip_text, re.IGNORECASE)
        if name_match:
            break

    if name_match:
        employee_name = name_match.group(1).strip()

        # Verify this isn't an organizational term
        if not any(org_term.lower() in employee_name.lower() for org_term in org_terms):
            employee["name"] = employee_name
            print(f"Successfully extracted Employee Name: {employee_name}")
        else:
            print(f"WARNING: Extracted name '{employee_name}' appears to be an organizational term, not a person's name")
    else:
        # If the primary pattern fails, try an alternative pattern
        alt_pattern = r"Name\s*:\s*([A-Z][A-Za-z\s\.\-]{2,30})"
        alt_match = re.search(alt_pattern, payslip_text, re.IGNORECASE)

        if alt_match:
            employee_name = alt_match.group(1).strip()

            # Verify this isn't an organizational term
            if not any(org_term.lower() in employee_name.lower() for org_term in org_terms):
                employee["name"] = employee_name
                print(f"Successfully extracted Employee Name using alternative pattern: {employee_name}")
            else:
                print(f"WARNING: Extracted name '{employee_name}' appears to be an organizational term, not a person's name")

    # If still not found, try a more structured approach looking for specific formats
    if not employee.get("name"):
        # Look for specific patterns that indicate employee names in the payslip format
        structured_name_patterns = [
            # Look for name in a line with Employee No.
            r"Employee No\.?\s*" + re.escape(str(employee.get("employee_id", ""))) + r"\s+([A-Z][A-Za-z\s\.\-]{2,30})",
            # Look for name in a line with SSF No.
            r"SSF No\.?\s*" + re.escape(str(employee.get("ssf_no", ""))) + r"\s+([A-Z][A-Za-z\s\.\-]{2,30})",
            # Look for name in a specific format at the beginning of a line
            r"^([A-Z][A-Za-z\s\.\-]{2,30})\s+(?:Employee|SSF|Ghana)",
        ]

        # Try each structured pattern
        for pattern in structured_name_patterns:
            # Search in the first 30 lines where header information is typically found
            header_text = '\n'.join(payslip_text.split('\n')[:30])
            name_match = re.search(pattern, header_text, re.MULTILINE)
            if name_match:
                potential_name = name_match.group(1).strip()
                # Validate against the blacklist
                if not any(blacklisted_term.lower() in potential_name.lower() for blacklisted_term in name_blacklist):
                    employee["name"] = potential_name
                    print(f"Successfully extracted Employee Name using structured pattern: {potential_name}")
                    break

    # As a last resort, if we have an employee ID, try to find the name near it
    if not employee.get("name") and employee.get("employee_id"):
        # Look for lines containing the employee ID
        lines = payslip_text.split('\n')
        for line in lines:
            if employee["employee_id"] in line:
                # Try to extract a name from this line
                # First, remove known non-name elements
                cleaned_line = re.sub(r'Employee No\.?|SSF No\.?|Ghana Card ID|Department|Section', '', line)
                # Remove the employee ID
                cleaned_line = cleaned_line.replace(employee["employee_id"], '')
                # Split into words and look for potential name patterns
                words = cleaned_line.split()

                # Look for 2-3 consecutive capitalized words that could be a name
                for i in range(len(words) - 1):
                    if i+2 <= len(words):
                        potential_name = ' '.join(words[i:i+2])
                        if re.match(r'^[A-Z][A-Za-z\s\.\-]{2,30}$', potential_name) and not any(blacklisted_term.lower() in potential_name.lower() for blacklisted_term in name_blacklist):
                            employee["name"] = potential_name
                            print(f"Successfully extracted Employee Name from ID line: {potential_name}")
                            break

                    if i+3 <= len(words):
                        potential_name = ' '.join(words[i:i+3])
                        if re.match(r'^[A-Z][A-Za-z\s\.\-]{2,30}$', potential_name) and not any(blacklisted_term.lower() in potential_name.lower() for blacklisted_term in name_blacklist):
                            employee["name"] = potential_name
                            print(f"Successfully extracted Employee Name from ID line: {potential_name}")
                            break

                # If we found a name, break out of the line loop
                if employee.get("name"):
                    break

    # Extract employee number - this is critical for accurate matching
    # Try multiple patterns to ensure we capture the Employee No. correctly
    # Employee No. formats: COP####, PW####, SEC####, E####
    emp_id_patterns = [
        r"([A-Z0-9-]+)\s+Employee No\.",
        r"Employee No\.?\s*([A-Za-z0-9-]+)",
        r"Employee Number:?\s*([A-Za-z0-9-]+)",
        r"(COP\d{4})",  # Format: COP followed by 4 digits
        r"(PW\d{4})",   # Format: PW followed by 4 digits
        r"(SEC\d{4})",  # Format: SEC followed by 4 digits
        r"(E\d{4})",    # Format: E followed by 4 digits
        r"Employee No:?\s*([A-Za-z0-9-]+)",
        r"Emp\. No\.?\s*([A-Za-z0-9-]+)",
        r"EMP NO\.?\s*([A-Za-z0-9-]+)"
    ]

    for pattern in emp_id_patterns:
        emp_id_match = re.search(pattern, payslip_text)
        if emp_id_match:
            potential_id = emp_id_match.group(1).strip()
            # Validate the Employee No. format - COP####, PW####, SEC####, E####
            if re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', potential_id):
                employee["employee_id"] = potential_id
                print(f"Successfully extracted Employee No.: {potential_id}")
            # Also accept other alphanumeric formats as a fallback
            elif re.match(r'^[A-Za-z0-9-]{3,15}$', potential_id):
                employee["employee_id"] = potential_id
                print(f"Successfully extracted Employee No. (alternative format): {potential_id}")

    # If we still don't have an ID, try to find it in a different section
    if not employee.get("employee_id"):
        # Look specifically for the Employee No. formats: COP####, PW####, SEC####, E####
        id_candidates = re.findall(r'\b(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})\b', payslip_text)
        if id_candidates:
            employee["employee_id"] = id_candidates[0]
            print(f"Found Employee No. from pattern matching: {id_candidates[0]}")
        # If still not found, try a more general pattern
        elif not employee.get("employee_id"):
            id_candidates = re.findall(r'\b([A-Z]{2,4}[0-9]{3,6})\b', payslip_text)
            if id_candidates:
                employee["employee_id"] = id_candidates[0]
                print(f"Found potential Employee No. from general pattern matching: {id_candidates[0]}")

    # FALLBACK: We must never have a missing employee ID for sorting
    if not employee.get("employee_id"):
        # Try to generate a unique ID based on other available information
        if employee.get("name"):
            # Create an ID from the employee's name (first letter of each word + 4 digits)
            name_parts = employee.get("name").split()
            if len(name_parts) >= 2:
                initials = ''.join([part[0] for part in name_parts if part])
                # Add a numeric component based on the hash of the name
                name_hash = abs(hash(employee.get("name"))) % 10000
                employee["employee_id"] = f"{initials}{name_hash:04d}"
                print(f"Generated Employee ID from name: {employee['employee_id']}")
            else:
                # Single word name
                name_prefix = name_parts[0][:3].upper()
                name_hash = abs(hash(employee.get("name"))) % 10000
                employee["employee_id"] = f"{name_prefix}{name_hash:04d}"
                print(f"Generated Employee ID from single-word name: {employee['employee_id']}")
        elif employee.get("department"):
            # Create an ID from the department
            dept_parts = employee.get("department").split()
            if len(dept_parts) >= 2:
                initials = ''.join([part[0] for part in dept_parts if part])[:3].upper()
                # Add a numeric component based on the hash of the department
                dept_hash = abs(hash(employee.get("department"))) % 10000
                employee["employee_id"] = f"{initials}{dept_hash:04d}"
                print(f"Generated Employee ID from department: {employee['employee_id']}")
            else:
                # Single word department
                dept_prefix = dept_parts[0][:3].upper()
                dept_hash = abs(hash(employee.get("department"))) % 10000
                employee["employee_id"] = f"{dept_prefix}{dept_hash:04d}"
                print(f"Generated Employee ID from single-word department: {employee['employee_id']}")
        else:
            # Absolute last resort - use a timestamp to ensure uniqueness
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            employee["employee_id"] = f"EMP{timestamp[-6:]}"
            print(f"Using fallback Employee ID with timestamp: {employee['employee_id']}")

    # Ensure employee_id is never None or empty
    assert employee["employee_id"] is not None and employee["employee_id"] != "", "Employee ID must not be empty"

    # Extract SSF number
    ssf_match = re.search(r"([A-Z0-9]+)\s+SSF No", payslip_text)
    if ssf_match:
        employee["ssf_no"] = ssf_match.group(1).strip()

    # Extract Ghana Card ID
    ghana_card_match = re.search(r"(GHA-\d+-\d+)\s+Ghana Card ID", payslip_text)
    if ghana_card_match:
        employee["ghana_card_id"] = ghana_card_match.group(1).strip()

    # Extract job title - critical for sorting
    job_title_patterns = [
        r"([A-Z][A-Za-z\s]+)\s+Job Title",
        r"Job Title:?\s*([A-Za-z\s\-\.]+)",
        r"JOB TITLE:?\s*([A-Za-z\s\-\.]+)",
        r"Position:?\s*([A-Za-z\s\-\.]+)",
        r"POSITION:?\s*([A-Za-z\s\-\.]+)",
        r"Title:?\s*([A-Za-z\s\-\.]+)"
    ]

    # Try each job title pattern
    job_title_found = False
    for pattern in job_title_patterns:
        job_title_match = re.search(pattern, payslip_text, re.IGNORECASE)
        if job_title_match:
            job_title = job_title_match.group(1).strip()
            # Clean up job title
            job_title = ' '.join(job_title.split())
            employee["job_title"] = job_title
            print(f"Successfully extracted Job Title: {job_title}")
            job_title_found = True
            break

    # If job title wasn't found with the primary patterns, try alternative approaches
    if not job_title_found:
        # Look for common job titles in the text
        common_titles = [
            "MINISTER", "PASTOR", "ELDER", "DEACON", "DEACONESS",
            "DIRECTOR", "MANAGER", "SUPERVISOR", "ACCOUNTANT", "SECRETARY",
            "ADMINISTRATOR", "OFFICER", "ASSISTANT", "CLERK", "COORDINATOR"
        ]

        for title in common_titles:
            if title in payslip_text:
                # Look for the full job title around this keyword
                title_context = re.search(r"([A-Z][A-Za-z\s]{2,30}\s+" + title + r"(?:\s+[A-Za-z\s]{2,30})?)", payslip_text)
                if title_context:
                    employee["job_title"] = title_context.group(1).strip()
                    print(f"Found Job Title from context: {employee['job_title']}")
                    job_title_found = True
                    break
                else:
                    # Use just the title word if context not found
                    employee["job_title"] = title
                    print(f"Using common Job Title: {title}")
                    job_title_found = True
                    break

    # FALLBACK: We must never have a missing job title for sorting
    if not employee.get("job_title"):
        # Try to derive job title from department or other fields
        if employee.get("department"):
            dept = employee.get("department")
            if "MINISTER" in dept:
                employee["job_title"] = "MINISTER"
            elif "PASTOR" in dept:
                employee["job_title"] = "PASTOR"
            elif "AREA" in dept:
                employee["job_title"] = "AREA OFFICER"
            elif "DISTRICT" in dept:
                employee["job_title"] = "DISTRICT OFFICER"
            elif "HEADQUARTERS" in dept:
                employee["job_title"] = "HEADQUARTERS STAFF"
            else:
                # Use a generic job title based on department
                employee["job_title"] = f"{dept} STAFF"
            print(f"Derived Job Title from Department: {employee['job_title']}")
        elif employee.get("name"):
            # Use a generic job title with the employee name
            employee["job_title"] = f"STAFF MEMBER"
            print(f"Using generic Job Title: {employee['job_title']}")
        else:
            # Absolute last resort
            employee["job_title"] = "EMPLOYEE"
            print(f"Using fallback Job Title: {employee['job_title']}")

    # Ensure job title is never None or empty
    assert employee["job_title"] is not None and employee["job_title"] != "", "Job Title must not be empty"

    # Extract department and section - critical for employee identification and report grouping
    # Based on the payslip screenshot:
    # Department is labeled as "Department" and has format like "ABUAKWA AREA - MINISTERS"
    # Section is labeled as "Section" and has format like "ABUAKWA"

    # First, try to extract both fields using their explicit labels with more flexible patterns
    # This is the most reliable method when the labels are present

    # More flexible patterns that account for various whitespace and formatting
    dept_patterns = [
        r"(?i)Department\s+(.+)",                       # Simple case-insensitive pattern from regex_after_department.txt
        r"Department\s*[\s:]*\s*([A-Za-z0-9\s\-\.]+)",  # Standard format with flexible whitespace
        r"Department[\s:]*([^\n\r]+)",                  # Capture everything after Department until newline
        r"Department\s+(.*?)(?=\s+Job|\s+Section|\s+SSF|\s+Employee|\s+Ghana|\s*$)", # Capture until next field
        r"Department\s+(.*?)(?=\s+[A-Z][a-z]+\s+[A-Z])", # Capture until next capitalized field
        r"DEPARTMENT\s*:\s*([A-Za-z0-9\s\-\.]+)",       # All caps with colon format
        r"Department\s*:\s*([A-Za-z0-9\s\-\.]+)",       # Title case with colon format
    ]

    section_patterns = [
        r"Section\s*[\s:]*\s*([A-Za-z0-9\s\-\.]+)",    # Standard format with flexible whitespace
        r"Section[\s:]*([^\n\r]+)",                    # Capture everything after Section until newline
        r"Section\s+(.*?)(?=\s+Department|\s+Job|\s+Employee|\s+SSF|\s*$)", # Capture until next field
        r"Section\s+(.*?)(?=\s+[A-Z][a-z]+\s+[A-Z])",  # Capture until next capitalized field
    ]

    # Try each department pattern
    department_found = False
    for pattern in dept_patterns:
        dept_match = re.search(pattern, payslip_text, re.IGNORECASE)
        if dept_match:
            department = dept_match.group(1).strip()
            # Clean up department name - remove extra spaces, standardize case
            department = ' '.join(department.split())
            employee["department"] = department
            print(f"Successfully extracted Department: {department}")
            department_found = True
            break

    # Try each section pattern
    for pattern in section_patterns:
        section_match = re.search(pattern, payslip_text, re.IGNORECASE)
        if section_match:
            section = section_match.group(1).strip()
            # Clean up section name
            section = ' '.join(section.split())
            employee["section"] = section
            print(f"Successfully extracted Section: {section}")
            break

    # If department wasn't found with the primary patterns, try alternative patterns
    if not department_found:
        # Try alternative department patterns
        alt_dept_patterns = [
            r"Dept:?\s*([A-Za-z0-9\s\-\.]+)",
            r"DEPARTMENT:?\s*([A-Za-z0-9\s\-\.]+)",
            r"([A-Z][A-Za-z0-9\s\-\.]+)\s+Department"
        ]

        for pattern in alt_dept_patterns:
            dept_match = re.search(pattern, payslip_text, re.IGNORECASE)  # Added IGNORECASE
            if dept_match:
                department = dept_match.group(1).strip()
                department = ' '.join(department.split())
                employee["department"] = department
                print(f"Successfully extracted Department using alternative pattern: {department}")
                department_found = True
                break

    # If department still not found, look for common department keywords
    if not department_found:
        dept_keywords = ["AREA", "DISTRICT", "REGION", "HEADQUARTERS", "OFFICE", "MINISTRY", "DEPARTMENT"]
        for keyword in dept_keywords:
            dept_candidates = re.findall(r'\b([A-Z][A-Za-z\s\-\.]+' + keyword + r'(?:\s*-\s*[A-Z][A-Za-z\s\-\.]+)?)\b', payslip_text)
            if dept_candidates:
                employee["department"] = dept_candidates[0].strip()
                print(f"Found Department from keyword matching: {dept_candidates[0]}")
                department_found = True
                break

        # If still not found, try a more aggressive approach with broader patterns
        if not department_found:
            # Look for any capitalized phrases that might be departments
            broader_dept_candidates = re.findall(r'\b([A-Z][A-Z\s\-\.]{5,30})\b', payslip_text)
            for candidate in broader_dept_candidates:
                # Filter out common non-department phrases
                if (len(candidate) > 5 and
                    "EMPLOYEE" not in candidate and
                    "PAYSLIP" not in candidate and
                    "SALARY" not in candidate and
                    "EARNINGS" not in candidate and
                    "DEDUCTIONS" not in candidate):
                    employee["department"] = candidate.strip()
                    print(f"Found Department using broader pattern matching: {candidate}")
                    department_found = True
                    break

    # Special case for the exact format seen in the screenshot
    if not department_found:
        # Look for lines that might contain department information
        lines = payslip_text.split('\n')
        for i, line in enumerate(lines):
            if "ABUAKWA AREA" in line or "MINISTERS" in line:
                employee["department"] = line.strip()
                print(f"Found Department using exact content match: {line.strip()}")
                department_found = True
                break

    # If section wasn't found with the primary pattern, try alternative patterns
    if not employee.get("section"):
        # Try alternative section patterns
        alt_section_patterns = [
            r"SECTION:?\s*([A-Za-z0-9\s\-\.]+)",
            r"([A-Z][A-Za-z0-9\s\-\.]+)\s+Section"
        ]

        for pattern in alt_section_patterns:
            section_match = re.search(pattern, payslip_text)
            if section_match:
                section = section_match.group(1).strip()
                section = ' '.join(section.split())
                employee["section"] = section
                print(f"Successfully extracted Section using alternative pattern: {section}")
                break

    # Department is always present in the payslips
    # If still not found after all attempts, try a more aggressive approach
    if not employee.get("department"):
        # Look for any line that might contain department information
        lines = payslip_text.split('\n')

        # First, look for lines that contain "Department" or "DEPARTMENT"
        for line in lines:
            if "Department" in line or "DEPARTMENT" in line:
                # Extract everything after "Department" or "DEPARTMENT"
                dept_text = re.sub(r'^.*?(Department|DEPARTMENT)[:\s]*', '', line).strip()
                if dept_text:
                    employee["department"] = dept_text
                    print(f"Found Department using line extraction: {dept_text}")
                    break

        # If still not found, look for lines that are on the same line as Employee Name
        if not employee.get("department"):
            for line in lines:
                if "Employee Name" in line or "EMPLOYEE NAME" in line:
                    # The department is often on the same line as Employee Name
                    # Try to extract it from this line
                    parts = line.split()
                    if len(parts) > 5:  # Enough parts to potentially contain department
                        # Department is typically after the name and before other fields
                        potential_dept = ' '.join(parts[3:])  # Skip employee ID, name, etc.
                        employee["department"] = potential_dept
                        print(f"Found Department from Employee Name line: {potential_dept}")
                        break

    # ABSOLUTE FALLBACK: We must NEVER have a missing department
    # If all extraction methods have failed, use a default value based on other information
    if not employee.get("department"):
        # Try to derive department from employee ID if available
        if employee.get("employee_id"):
            emp_id = employee.get("employee_id")
            if emp_id.startswith("COP"):
                employee["department"] = "CHURCH OF PENTECOST HEADQUARTERS"
            elif emp_id.startswith("PW"):
                employee["department"] = "PENTECOST WORKERS DEPARTMENT"
            elif emp_id.startswith("SEC"):
                employee["department"] = "SECRETARIAT DEPARTMENT"
            else:
                # Use a generic department name with the employee ID
                employee["department"] = f"DEPARTMENT {emp_id}"
            print(f"Using derived Department from Employee ID: {employee['department']}")
        elif employee.get("name"):
            # Use a generic department with the employee name
            employee["department"] = f"DEPARTMENT OF {employee['name']}"
            print(f"Using derived Department from Employee Name: {employee['department']}")
        else:
            # Absolute last resort - use a timestamp to ensure uniqueness
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            employee["department"] = f"DEPARTMENT {timestamp}"
            print(f"Using fallback Department with timestamp: {employee['department']}")

    # Ensure department is never None or empty
    assert employee["department"] is not None and employee["department"] != "", "Department must not be empty"

    # If section is still not found, derive it from department if possible
    if not employee.get("section"):
        if employee.get("department"):
            # Try to use the first part of department (before any dash)
            dept_parts = employee["department"].split(" - ")
            if len(dept_parts) > 0:
                employee["section"] = dept_parts[0].strip()
                print(f"Derived Section from Department: {employee['section']}")
            else:
                # If no dash, just use the department as section
                employee["section"] = employee["department"]
                print(f"Using Department as Section: {employee['section']}")

            # Special case for ABUAKWA AREA - MINISTERS
            if "ABUAKWA AREA" in employee["department"]:
                employee["section"] = "ABUAKWA"
                print(f"Set Section to ABUAKWA based on department pattern")
        else:
            # As a last resort, use a default value
            employee["section"] = "SECTION"
            print("WARNING: Section not found in payslip. Using default value.")

    # Ensure section is never None or empty
    assert employee["section"] is not None and employee["section"] != "", "Section must not be empty"

    # CRITICAL FIX: Final validation to ensure we never use organizational information as employee names
    if employee.get("name"):
        # First, clean up the name - remove any trailing punctuation or extra spaces
        employee["name"] = employee["name"].strip().rstrip('.:,;')

        # Based on user feedback: ALL CAPS names are valid and expected
        # Check if the name is too short (likely not a real name)
        if len(employee["name"]) < 3:
            print(f"WARNING: Employee name '{employee['name']}' is too short. Clearing name to prevent confusion.")
            employee["name"] = None  # Clear the name

        # Check if the name is exactly one of the blacklisted terms (exact match only)
        # This is much less restrictive than before
        elif any(employee["name"] == blacklisted_term for blacklisted_term in name_blacklist):
            print(f"WARNING: Employee name '{employee['name']}' exactly matches a blacklisted term. Clearing name to prevent confusion.")
            employee["name"] = None  # Clear the name

        # Check against section - only if it's an exact match
        elif employee.get("section") and employee["name"] == employee["section"]:
            print(f"WARNING: Employee name '{employee['name']}' is the same as section. Clearing name to prevent confusion.")
            employee["name"] = None  # Clear the name

        # Check against department - only if it's an exact match
        elif employee.get("department") and employee["name"] == employee["department"]:
            print(f"WARNING: Employee name '{employee['name']}' is the same as department. Clearing name to prevent confusion.")
            employee["name"] = None  # Clear the name

        # Print debug information about the final name
        else:
            print(f"DEBUG: Final validated employee name: {employee['name']}")

    # ABSOLUTE FALLBACK: We must NEVER have a missing employee name for sorting
    if not employee.get("name"):
        # Try to derive name from other available information
        if employee.get("employee_id"):
            # Create a name based on the employee ID
            employee["name"] = f"EMPLOYEE {employee.get('employee_id')}"
            print(f"Using derived Employee Name from ID: {employee['name']}")
        elif employee.get("job_title"):
            # Create a name based on the job title with a unique identifier
            job_hash = abs(hash(employee.get("job_title"))) % 1000
            employee["name"] = f"{employee.get('job_title')} {job_hash:03d}"
            print(f"Using derived Employee Name from Job Title: {employee['name']}")
        elif employee.get("department"):
            # Create a name based on the department with a unique identifier
            dept_hash = abs(hash(employee.get("department"))) % 1000
            employee["name"] = f"STAFF {dept_hash:03d} ({employee.get('department')})"
            print(f"Using derived Employee Name from Department: {employee['name']}")
        else:
            # Absolute last resort - use a timestamp to ensure uniqueness
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            employee["name"] = f"EMPLOYEE {timestamp[-6:]}"
            print(f"Using fallback Employee Name with timestamp: {employee['name']}")

    # Ensure name is never None or empty
    assert employee["name"] is not None and employee["name"] != "", "Employee Name must not be empty"




    # We'll extract these values through the dynamic extraction process
    # The direct extraction is now handled by the dynamic extraction of earnings and deductions
    # This prevents duplication of values

    # Extract month and year - multiple patterns to handle different formats
    # First try the format with "Period" keyword
    period_match = re.search(r"([A-Za-z]+)\s+(\d{4})\s+Period", payslip_text)
    if period_match:
        employee["month"] = period_match.group(1).strip()
        employee["year"] = period_match.group(2).strip()
    else:
        # Try alternative format (like in the screenshot: "Period    April 2025")
        alt_period_match = re.search(r"Period\s+([A-Za-z]+)\s+(\d{4})", payslip_text)
        if alt_period_match:
            employee["month"] = alt_period_match.group(1).strip()
            employee["year"] = alt_period_match.group(2).strip()
        else:
            # Try to find month and year at the top of the payslip
            top_period_match = re.search(r"^([A-Za-z]+)\s+(\d{4})", payslip_text)
            if top_period_match:
                employee["month"] = top_period_match.group(1).strip()
                employee["year"] = top_period_match.group(2).strip()

    # Extract detailed loan information
    # Create a structured loan dictionary to store all loan details
    employee["loan_details"] = {}

    # Look for the LOANS section
    loans_section = re.search(r"LOANS(.*?)(?:EMPLOYEE BANK DETAILS|$)", payslip_text, re.DOTALL)
    if loans_section:
        loan_text = loans_section.group(1).strip()

        # Split the loan text into lines for processing
        loan_lines = loan_text.split('\n')

        # Variables to track the current loan being processed
        current_loan_type = None
        current_loan = {}

        # Process each line in the loans section
        for line in loan_lines:
            line = line.strip()

            # Skip empty lines
            if not line:
                continue

            # Check if this line contains a loan type
            # Look for lines that don't contain standard loan fields
            if not any(field in line for field in ["BALANCE B/F", "CURRENT DEDUCTION", "OUTST. BALANCE"]):
                # This might be a loan type line
                # Clean up the line to extract just the loan type
                potential_loan_type = line.strip()

                # If we already have a loan type and data, save it before starting a new one
                if current_loan_type and current_loan:
                    employee["loan_details"][current_loan_type] = current_loan
                    current_loan = {}

                # Standardize loan type if enhanced dictionary is available
                standardized_loan_type = potential_loan_type
                if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                    try:
                        standardized_loan_type = enhanced_payroll_dictionaries.get_standardized_name("LOANS", potential_loan_type)
                        if standardized_loan_type != potential_loan_type:
                            print(f"Standardized loan type: {potential_loan_type} -> {standardized_loan_type}")
                    except Exception as e:
                        print(f"Error standardizing loan type: {e}")

                # Set the new loan type
                current_loan_type = standardized_loan_type
                continue

            # Extract Balance B/F
            balance_bf_match = re.search(r"BALANCE B/F\s+([\d,.]+)", line)
            if balance_bf_match:
                # Convert string amount to float, removing commas
                amount_str = balance_bf_match.group(1).strip()
                amount_str = amount_str.replace(',', '')
                try:
                    current_loan["balance_bf"] = float(amount_str)
                except ValueError:
                    current_loan["balance_bf"] = 0
                continue

            # Extract Current Deduction
            current_deduction_match = re.search(r"CURRENT DEDUCTION\s+([\d,.]+)", line)
            if current_deduction_match:
                # Convert string amount to float, removing commas
                amount_str = current_deduction_match.group(1).strip()
                amount_str = amount_str.replace(',', '')
                try:
                    current_loan["current_deduction"] = float(amount_str)
                except ValueError:
                    current_loan["current_deduction"] = 0
                continue

            # Extract Outstanding Balance
            outst_balance_match = re.search(r"OUTST\. BALANCE\s+([\d,.]+)", line)
            if outst_balance_match:
                # Convert string amount to float, removing commas
                amount_str = outst_balance_match.group(1).strip()
                amount_str = amount_str.replace(',', '')
                try:
                    current_loan["outstanding_balance"] = float(amount_str)
                except ValueError:
                    current_loan["outstanding_balance"] = 0
                continue

        # Save the last loan if we have one
        if current_loan_type and current_loan:
            employee["loan_details"][current_loan_type] = current_loan

        # If we couldn't parse structured loans, try a fallback approach
        if not employee["loan_details"]:
            # Try to extract loan types and their details using regex patterns
            loan_types = re.findall(r"([A-Z][A-Z\s]+(?:ADVANCE|LOAN|MINS))", loan_text)

            for loan_type in loan_types:
                # Find the section of text after this loan type
                loan_type_section = re.search(rf"{re.escape(loan_type)}(.*?)(?:[A-Z][A-Z\s]+(?:ADVANCE|LOAN|MINS)|$)", loan_text, re.DOTALL)

                if loan_type_section:
                    loan_type_text = loan_type_section.group(1).strip()

                    # Extract loan details
                    balance_bf = re.search(r"BALANCE B/F\s+([\d,.]+)", loan_type_text)
                    current_deduction = re.search(r"CURRENT DEDUCTION\s+([\d,.]+)", loan_type_text)
                    outst_balance = re.search(r"OUTST\. BALANCE\s+([\d,.]+)", loan_type_text)

                    loan_details = {}
                    if balance_bf:
                        # Convert string amount to float, removing commas
                        amount_str = balance_bf.group(1).strip()
                        amount_str = amount_str.replace(',', '')
                        try:
                            loan_details["balance_bf"] = float(amount_str)
                        except ValueError:
                            loan_details["balance_bf"] = 0

                    if current_deduction:
                        # Convert string amount to float, removing commas
                        amount_str = current_deduction.group(1).strip()
                        amount_str = amount_str.replace(',', '')
                        try:
                            loan_details["current_deduction"] = float(amount_str)
                        except ValueError:
                            loan_details["current_deduction"] = 0

                    if outst_balance:
                        # Convert string amount to float, removing commas
                        amount_str = outst_balance.group(1).strip()
                        amount_str = amount_str.replace(',', '')
                        try:
                            loan_details["outstanding_balance"] = float(amount_str)
                        except ValueError:
                            loan_details["outstanding_balance"] = 0

                    if loan_details:
                        # Standardize loan type if enhanced dictionary is available
                        standardized_loan_type = loan_type.strip()
                        if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                            try:
                                standardized_loan_type = enhanced_payroll_dictionaries.get_standardized_name("LOANS", standardized_loan_type)
                                if standardized_loan_type != loan_type.strip():
                                    print(f"Standardized loan type (fallback): {loan_type.strip()} -> {standardized_loan_type}")
                            except Exception as e:
                                print(f"Error standardizing loan type (fallback): {e}")

                        employee["loan_details"][standardized_loan_type] = loan_details

    # For backward compatibility, also set the simple loan field
    # Use the first outstanding balance if available, otherwise use the old pattern
    if employee["loan_details"]:
        # Get the first loan's outstanding balance
        first_loan = next(iter(employee["loan_details"].values()), {})
        if first_loan.get("outstanding_balance"):
            # Format the amount as a string with commas
            employee["loan"] = f"{first_loan['outstanding_balance']:,.2f}"

    # If no structured loans were found, try the old pattern
    if not employee["loan"]:
        loan_match = re.search(r"Loan Balance:?\s*([\d,.]+)", payslip_text, re.IGNORECASE)
        if loan_match:
            employee["loan"] = loan_match.group(1).strip()

    # Extract bank details
    bank_details_section = re.search(r"EMPLOYEE BANK DETAILS(.*?)(?:EARNINGS|DEDUCTIONS)",
                                    payslip_text, re.DOTALL | re.IGNORECASE)
    if bank_details_section:
        bank_text = bank_details_section.group(1).strip()

        # First, try to extract bank details using dictionary patterns
        bank_details = {}
        if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
            try:
                # Get all bank details items from the dictionary
                dictionary = enhanced_payroll_dictionaries.load_dictionary()
                if SECTION_EMPLOYEE_BANK_DETAILS in dictionary and "items" in dictionary[SECTION_EMPLOYEE_BANK_DETAILS]:
                    bank_details_items = dictionary[SECTION_EMPLOYEE_BANK_DETAILS]["items"]

                    # Try to extract each item using its format
                    for item_name, item_data in bank_details_items.items():
                        format_str = item_data.get("format", "")
                        value_format = item_data.get("value_format", "")

                        # Skip section headers
                        if value_format == "Section Header" or value_format == "Column Header":
                            continue

                        # Extract the value using the item's format
                        value = enhanced_payroll_dictionaries.extract_item_value(item_name, format_str, value_format, bank_text)

                        # If value found, add to bank details
                        if value:
                            # Map standard names to the expected keys in bank_details
                            if item_name == "Bank Name":
                                bank_details["bank_name"] = value
                            elif item_name == "Branch":
                                bank_details["branch"] = value
                            elif item_name == "Account Number":
                                bank_details["account_number"] = value
                            else:
                                # For any other fields, use the item name as the key
                                bank_details[item_name] = value
                            print(f"Extracted bank detail using dictionary pattern: {item_name} = {value}")

                        # Also try variations
                        variations = item_data.get("variations", [])
                        for variation in variations:
                            # Skip if we already found the item
                            if item_name in bank_details or \
                               (item_name == "Bank Name" and "bank_name" in bank_details) or \
                               (item_name == "Branch" and "branch" in bank_details) or \
                               (item_name == "Account Number" and "account_number" in bank_details):
                                break

                            value = enhanced_payroll_dictionaries.extract_item_value(variation, format_str, value_format, bank_text)
                            if value:
                                # Map standard names to the expected keys in bank_details
                                if item_name == "Bank Name":
                                    bank_details["bank_name"] = value
                                elif item_name == "Branch":
                                    bank_details["branch"] = value
                                elif item_name == "Account Number":
                                    bank_details["account_number"] = value
                                else:
                                    # For any other fields, use the item name as the key
                                    bank_details[item_name] = value
                                print(f"Extracted bank detail using variation '{variation}': {item_name} = {value}")
            except Exception as e:
                print(f"Error extracting bank details using dictionary patterns: {e}")

        # Fallback to basic pattern matching if dictionary extraction didn't find everything
        if not bank_details.get("bank_name"):
            bank_name_match = re.search(r"Bank Name:?\s*([A-Za-z\s]+)", bank_text, re.IGNORECASE)
            if bank_name_match:
                bank_details["bank_name"] = bank_name_match.group(1).strip()
                print(f"Extracted bank name using fallback pattern: {bank_details['bank_name']}")

        if not bank_details.get("branch"):
            bank_branch_match = re.search(r"Branch:?\s*([A-Za-z\s]+)", bank_text, re.IGNORECASE)
            if bank_branch_match:
                bank_details["branch"] = bank_branch_match.group(1).strip()
                print(f"Extracted bank branch using fallback pattern: {bank_details['branch']}")

        if not bank_details.get("account_number"):
            account_number_match = re.search(r"Account Number:?\s*(\d+)", bank_text, re.IGNORECASE)
            if account_number_match:
                bank_details["account_number"] = account_number_match.group(1).strip()
                print(f"Extracted account number using fallback pattern: {bank_details['account_number']}")

        if bank_details:
            employee["bank_details"] = bank_details

    # Extract personal details using dictionary patterns
    # This is a new section to extract personal details using the dictionary
    if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
        try:
            # Get all personal details items from the dictionary
            dictionary = enhanced_payroll_dictionaries.load_dictionary()
            if SECTION_PERSONAL_DETAILS in dictionary and "items" in dictionary[SECTION_PERSONAL_DETAILS]:
                personal_details_items = dictionary[SECTION_PERSONAL_DETAILS]["items"]

                # Try to extract each item using its format
                for item_name, item_data in personal_details_items.items():
                    format_str = item_data.get("format", "")
                    value_format = item_data.get("value_format", "")

                    # Skip section headers
                    if value_format == "Section Header" or value_format == "Column Header":
                        continue

                    # Extract the value using the item's format
                    value = enhanced_payroll_dictionaries.extract_item_value(item_name, format_str, value_format, payslip_text)

                    # If value found, add to personal details
                    if value:
                        # Add to personal_details dictionary
                        employee["personal_details"][item_name] = value
                        print(f"Extracted personal detail using dictionary pattern: {item_name} = {value}")

                    # Also try variations
                    variations = item_data.get("variations", [])
                    for variation in variations:
                        # Skip if we already found the item
                        if item_name in employee["personal_details"]:
                            break

                        value = enhanced_payroll_dictionaries.extract_item_value(variation, format_str, value_format, payslip_text)
                        if value:
                            # Add to personal_details dictionary
                            employee["personal_details"][item_name] = value
                            print(f"Extracted personal detail using variation '{variation}': {item_name} = {value}")

                # Copy key personal details to the main employee object for backward compatibility
                if "Employee ID" in employee["personal_details"] and not employee["employee_id"]:
                    employee["employee_id"] = employee["personal_details"]["Employee ID"]
                    print(f"Set employee_id from personal_details: {employee['employee_id']}")

                if "Employee Name" in employee["personal_details"] and not employee["name"]:
                    employee["name"] = employee["personal_details"]["Employee Name"]
                    print(f"Set name from personal_details: {employee['name']}")

                if "SSF No" in employee["personal_details"] and not employee["ssf_no"]:
                    employee["ssf_no"] = employee["personal_details"]["SSF No"]
                    print(f"Set ssf_no from personal_details: {employee['ssf_no']}")

                if "Ghana Card ID" in employee["personal_details"] and not employee["ghana_card_id"]:
                    employee["ghana_card_id"] = employee["personal_details"]["Ghana Card ID"]
                    print(f"Set ghana_card_id from personal_details: {employee['ghana_card_id']}")

                if "Job Title" in employee["personal_details"] and not employee["job_title"]:
                    employee["job_title"] = employee["personal_details"]["Job Title"]
                    print(f"Set job_title from personal_details: {employee['job_title']}")

                if "Department" in employee["personal_details"] and not employee["department"]:
                    employee["department"] = employee["personal_details"]["Department"]
                    print(f"Set department from personal_details: {employee['department']}")

                if "Section" in employee["personal_details"] and not employee["section"]:
                    employee["section"] = employee["personal_details"]["Section"]
                    print(f"Set section from personal_details: {employee['section']}")
        except Exception as e:
            print(f"Error extracting personal details using dictionary patterns: {e}")

    # Extract all employer contributions dynamically
    # First, try to find the EMPLOYER'S CONTRIBUTION section
    employer_section = re.search(r"(?:EMPLOYER'S CONTRIBUTION|EMPLOYER CONTRIBUTION)(?:.*?)(?:LOAN DEDUCTIONS|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if employer_section:
        employer_text = employer_section.group(0)

        # First, try to extract employer contributions using dictionary patterns
        if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
            try:
                # Get all employer contribution items from the dictionary
                dictionary = enhanced_payroll_dictionaries.load_dictionary()
                if SECTION_EMPLOYERS_CONTRIBUTION in dictionary and "items" in dictionary[SECTION_EMPLOYERS_CONTRIBUTION]:
                    employer_items = dictionary[SECTION_EMPLOYERS_CONTRIBUTION]["items"]

                    # Try to extract each item using its format
                    for item_name, item_data in employer_items.items():
                        format_str = item_data.get("format", "")
                        value_format = item_data.get("value_format", "")

                        # Skip section headers
                        if value_format == "Section Header" or value_format == "Column Header":
                            continue

                        # Extract the value using the item's format
                        value = enhanced_payroll_dictionaries.extract_item_value(item_name, format_str, value_format, employer_text)

                        # If value found, add to employer contributions
                        if value:
                            # Add to employer_contributions dictionary
                            employee["employer_contributions"][item_name] = value
                            print(f"Extracted employer contribution using dictionary pattern: {item_name} = {value}")

                        # Also try variations
                        variations = item_data.get("variations", [])
                        for variation in variations:
                            # Skip if we already found the item
                            if item_name in employee["employer_contributions"]:
                                break

                            value = enhanced_payroll_dictionaries.extract_item_value(variation, format_str, value_format, employer_text)
                            if value:
                                # Add to employer_contributions dictionary
                                employee["employer_contributions"][item_name] = value
                                print(f"Extracted employer contribution using variation '{variation}': {item_name} = {value}")
            except Exception as e:
                print(f"Error extracting employer contributions using dictionary patterns: {e}")

        # Fallback to basic pattern matching
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        employer_items = re.finditer(r"([A-Z][A-Z\s\-\.0-9]+)\s+([\d,.]+)", employer_text)
        for item in employer_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()

            # Only skip section headers
            if item_name in ["EMPLOYER'S CONTRIBUTION", "EMPLOYER CONTRIBUTION"]:
                continue

            # Skip if we already extracted this item using dictionary patterns
            standardized_name = enhanced_payroll_dictionaries.get_standardized_name(SECTION_EMPLOYERS_CONTRIBUTION, item_name) if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None else item_name
            if standardized_name in employee["employer_contributions"]:
                continue

            # Standardize employer contribution name if enhanced dictionary is available
            if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                try:
                    if standardized_name != item_name:
                        print(f"Standardized employer contribution name: {item_name} -> {standardized_name}")
                except Exception as e:
                    print(f"Error standardizing employer contribution name: {e}")

            # Add to employer_contributions dictionary
            employee["employer_contributions"][standardized_name] = item_value
            print(f"Extracted employer contribution using fallback pattern: {standardized_name} = {item_value}")

    # No fallbacks - we only use the dynamic extraction to avoid duplicates
    # This ensures we only get the capitalized versions directly from the payslip

    # Extract all deductions dynamically
    # First, try to find the DEDUCTIONS section
    deductions_section = re.search(r"(?:DEDUCTIONS)(?:.*?)(?:NET PAY|EMPLOYER'S CONTRIBUTION|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if deductions_section:
        deductions_text = deductions_section.group(0)

        # First, try to extract deductions using dictionary patterns
        if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
            try:
                # Get all deductions items from the dictionary
                dictionary = enhanced_payroll_dictionaries.load_dictionary()
                if SECTION_DEDUCTIONS in dictionary and "items" in dictionary[SECTION_DEDUCTIONS]:
                    deductions_items = dictionary[SECTION_DEDUCTIONS]["items"]

                    # Try to extract each item using its format
                    for item_name, item_data in deductions_items.items():
                        format_str = item_data.get("format", "")
                        value_format = item_data.get("value_format", "")

                        # Skip section headers
                        if value_format == "Section Header" or value_format == "Column Header":
                            continue

                        # Extract the value using the item's format
                        value = enhanced_payroll_dictionaries.extract_item_value(item_name, format_str, value_format, deductions_text)

                        # If value found, check exclusion list before adding
                        if value:
                            # Check if this item is in the hardcoded exclusion list
                            if is_excluded_item(item_name):
                                print(f"EXCLUSION: Skipping excluded deduction item '{item_name}' with value {value}")
                                continue

                            # Handle NET PAY specially
                            if item_name == "NET PAY":
                                employee["net_pay"] = value
                                print(f"Set net_pay from deductions section using dictionary pattern: {value}")
                                continue

                            # Add to deductions dictionary
                            employee["deductions"][item_name] = value
                            print(f"Extracted deduction using dictionary pattern: {item_name} = {value}")

                        # Also try variations
                        variations = item_data.get("variations", [])
                        for variation in variations:
                            # Skip if we already found the item
                            if item_name in employee["deductions"]:
                                break

                            value = enhanced_payroll_dictionaries.extract_item_value(variation, format_str, value_format, deductions_text)
                            if value:
                                # Check if this item is in the hardcoded exclusion list
                                if is_excluded_item(item_name):
                                    print(f"EXCLUSION: Skipping excluded deduction variation '{variation}' for item '{item_name}' with value {value}")
                                    continue

                                # Handle NET PAY specially
                                if item_name == "NET PAY":
                                    employee["net_pay"] = value
                                    print(f"Set net_pay from deductions section using dictionary variation '{variation}': {value}")
                                    continue

                                # Add to deductions dictionary
                                employee["deductions"][item_name] = value
                                print(f"Extracted deduction using variation '{variation}': {item_name} = {value}")
            except Exception as e:
                print(f"Error extracting deductions using dictionary patterns: {e}")

        # Fallback to basic pattern matching
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        deduction_items = re.finditer(r"([A-Z][A-Z\s\-\.]+)\s+([\d,.]+)", deductions_text)
        for item in deduction_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()

            # Check if this item is in the hardcoded exclusion list
            if is_excluded_item(item_name):
                print(f"EXCLUSION: Skipping excluded deduction fallback item '{item_name}' with value {item_value}")
                continue

            # Handle NET PAY specially
            if item_name == "NET PAY" and not employee.get("net_pay"):
                employee["net_pay"] = item_value
                print(f"Set net_pay from deductions section using fallback pattern: {item_value}")
                continue

            # Only skip section headers
            if item_name in ["DEDUCTIONS"]:
                continue

            # Skip if we already extracted this item using dictionary patterns
            standardized_name = enhanced_payroll_dictionaries.get_standardized_name(SECTION_DEDUCTIONS, item_name) if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None else item_name
            if standardized_name in employee["deductions"]:
                continue

            # Check if the standardized name is also excluded
            if is_excluded_item(standardized_name):
                print(f"EXCLUSION: Skipping excluded standardized deduction '{standardized_name}' (original: '{item_name}') with value {item_value}")
                continue

            # Standardize deduction name if enhanced dictionary is available
            if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                try:
                    if standardized_name != item_name:
                        print(f"Standardized deduction name: {item_name} -> {standardized_name}")
                except Exception as e:
                    print(f"Error standardizing deduction name: {e}")

            # Add to deductions dictionary
            employee["deductions"][standardized_name] = item_value
            print(f"Extracted deduction using fallback pattern: {standardized_name} = {item_value}")

    # Make sure we extract NET PAY if it wasn't found in the deductions section
    if not employee["net_pay"]:
        net_pay_match = re.search(r"NET PAY\s+([\d,.]+)", payslip_text)
        if net_pay_match:
            employee["net_pay"] = net_pay_match.group(1).strip()
            print(f"Set net_pay from direct search: {employee['net_pay']}")

    # No fallbacks - we only use the dynamic extraction to avoid duplicates
    # This ensures we only get the capitalized versions directly from the payslip

    # Extract all earnings dynamically
    # First, try to find the EARNINGS section
    earnings_section = re.search(r"(?:EARNINGS|ALLOWANCES)(?:.*?)(?:DEDUCTIONS|GROSS SALARY|NET PAY|$)", payslip_text, re.DOTALL | re.IGNORECASE)
    if earnings_section:
        earnings_text = earnings_section.group(0)

        # First, try to extract earnings using dictionary patterns
        if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
            try:
                # Get all earnings items from the dictionary
                dictionary = enhanced_payroll_dictionaries.load_dictionary()
                if SECTION_EARNINGS in dictionary and "items" in dictionary[SECTION_EARNINGS]:
                    earnings_items = dictionary[SECTION_EARNINGS]["items"]

                    # Try to extract each item using its format
                    for item_name, item_data in earnings_items.items():
                        format_str = item_data.get("format", "")
                        value_format = item_data.get("value_format", "")

                        # Skip section headers
                        if value_format == "Section Header" or value_format == "Column Header":
                            continue

                        # Extract the value using the item's format
                        value = enhanced_payroll_dictionaries.extract_item_value(item_name, format_str, value_format, earnings_text)

                        # If value found, check exclusion list before adding
                        if value:
                            # Check if this item is in the hardcoded exclusion list
                            if is_excluded_item(item_name):
                                print(f"EXCLUSION: Skipping excluded earnings item '{item_name}' with value {value}")
                                continue

                            # Handle special cases for basic salary, gross salary, and net pay
                            if item_name == "BASIC SALARY":
                                employee["basic_salary"] = value
                                print(f"Set basic_salary using dictionary pattern: {value}")
                                continue
                            elif item_name == "GROSS SALARY":
                                employee["gross_salary"] = value
                                print(f"Set gross_salary using dictionary pattern: {value}")
                                continue
                            elif item_name == "NET PAY":
                                employee["net_pay"] = value
                                print(f"Set net_pay using dictionary pattern: {value}")
                                continue

                            # Add to earnings dictionary
                            employee["earnings"][item_name] = value
                            print(f"Extracted earning using dictionary pattern: {item_name} = {value}")

                        # Also try variations
                        variations = item_data.get("variations", [])
                        for variation in variations:
                            # Skip if we already found the item
                            if item_name in employee["earnings"]:
                                break

                            value = enhanced_payroll_dictionaries.extract_item_value(variation, format_str, value_format, earnings_text)
                            if value:
                                # Check if this item is in the hardcoded exclusion list
                                if is_excluded_item(item_name):
                                    print(f"EXCLUSION: Skipping excluded earnings variation '{variation}' for item '{item_name}' with value {value}")
                                    continue

                                # Handle special cases for basic salary, gross salary, and net pay
                                if item_name == "BASIC SALARY":
                                    employee["basic_salary"] = value
                                    print(f"Set basic_salary using dictionary variation '{variation}': {value}")
                                    continue
                                elif item_name == "GROSS SALARY":
                                    employee["gross_salary"] = value
                                    print(f"Set gross_salary using dictionary variation '{variation}': {value}")
                                    continue
                                elif item_name == "NET PAY":
                                    employee["net_pay"] = value
                                    print(f"Set net_pay using dictionary variation '{variation}': {value}")
                                    continue

                                # Add to earnings dictionary
                                employee["earnings"][item_name] = value
                                print(f"Extracted earning using variation '{variation}': {item_name} = {value}")
            except Exception as e:
                print(f"Error extracting earnings using dictionary patterns: {e}")

        # Fallback to basic pattern matching
        # Look for patterns like "ITEM_NAME 123.45" or "ITEM NAME 123.45"
        # This pattern matches any capitalized text followed by a number
        earnings_items = re.finditer(r"([A-Z][A-Z\s\-\.]+)\s+([\d,.]+)", earnings_text)
        for item in earnings_items:
            item_name = item.group(1).strip()
            item_value = item.group(2).strip()

            # Check if this item is in the hardcoded exclusion list
            if is_excluded_item(item_name):
                print(f"EXCLUSION: Skipping excluded earnings fallback item '{item_name}' with value {item_value}")
                continue

            # Handle special cases for basic salary, gross salary, and net pay
            if item_name == "BASIC SALARY" and not employee.get("basic_salary"):
                employee["basic_salary"] = item_value
                print(f"Set basic_salary using fallback pattern: {item_value}")
                continue
            elif item_name == "GROSS SALARY" and not employee.get("gross_salary"):
                employee["gross_salary"] = item_value
                print(f"Set gross_salary using fallback pattern: {item_value}")
                continue
            elif item_name == "NET PAY" and not employee.get("net_pay"):
                employee["net_pay"] = item_value
                print(f"Set net_pay using fallback pattern: {item_value}")
                continue

            # Only skip section headers
            if item_name in ["EARNINGS", "ALLOWANCES", "DEDUCTIONS"]:
                continue

            # Skip if we already extracted this item using dictionary patterns
            standardized_name = enhanced_payroll_dictionaries.get_standardized_name(SECTION_EARNINGS, item_name) if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None else item_name
            if standardized_name in employee["earnings"]:
                continue

            # Check if the standardized name is also excluded
            if is_excluded_item(standardized_name):
                print(f"EXCLUSION: Skipping excluded standardized earnings '{standardized_name}' (original: '{item_name}') with value {item_value}")
                continue

            # Standardize earnings name if enhanced dictionary is available
            if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                try:
                    if standardized_name != item_name:
                        print(f"Standardized earnings name: {item_name} -> {standardized_name}")
                except Exception as e:
                    print(f"Error standardizing earnings name: {e}")

            # Add to earnings dictionary
            employee["earnings"][standardized_name] = item_value
            print(f"Extracted earning using fallback pattern: {standardized_name} = {item_value}")

    # No fallbacks - we only use the dynamic extraction to avoid duplicates
    # This ensures we only get the capitalized versions directly from the payslip

    # Set the employee_id (primary identifier) based on hierarchy of available identifiers
    # Default to SSF number if available
    if employee["ssf_no"]:
        employee["employee_id"] = employee["ssf_no"]
    # Otherwise use Ghana Card ID
    elif employee["ghana_card_id"]:
        employee["employee_id"] = employee["ghana_card_id"]
    # Otherwise use name
    elif employee["name"]:
        employee["employee_id"] = employee["name"]
    # Generate a random ID as last resort
    else:
        employee["employee_id"] = f"Unknown_Employee_{hash(payslip_text) % 10000}"

    return employee

def parse_payroll_data(payslips, pdf_path=None):
    """Parse the extracted payslips to identify payroll data."""
    print("Parsing payroll data...")

    # Update process phase to Parsing (Phase 2)
    update_process_phase("PARSING", 2)

    # Extract employee data from each payslip
    employees = []
    for i, payslip in enumerate(payslips):
        # Print progress for every payslip for UI updates
        print(f"Processing payslip {i+1}/{len(payslips)}")

        payslip_text = payslip["text"]
        employee = extract_employee_data(payslip_text)

        # Add page number information
        page_num = payslip.get("page_num", 0)
        employee["page_num"] = page_num

        # If we have a PDF path and the employee No. is missing or doesn't match expected formats,
        # try to extract it using advanced layout detection
        if pdf_path and (not employee["employee_id"] or not re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', str(employee["employee_id"]))):
            print(f"Employee No. missing or doesn't match expected format: {employee['employee_id']}")
            print("Trying advanced layout detection...")

            # First check if we already have an ID from layout detection
            if "employee_id_from_layout" in payslip and payslip["employee_id_from_layout"]:
                employee["employee_id"] = payslip["employee_id_from_layout"]
                print(f"Using pre-extracted Employee No. from layout: {employee['employee_id']}")
            # Otherwise try to extract it now
            elif page_num > 0:
                layout_id = extract_employee_id_with_layout(pdf_path, page_num)
                if layout_id:
                    employee["employee_id"] = layout_id
                    print(f"Successfully extracted Employee No. using layout detection: {layout_id}")

        # If we have a PDF path and the department is missing or set to a generic value,
        # try to extract it using advanced layout detection
        if pdf_path and page_num > 0 and (not employee["department"] or employee["department"] == "General Department" or employee["department"] == "CHURCH OF PENTECOST"):
            print(f"Department missing or generic: {employee.get('department')}")
            print("Trying advanced department layout detection...")

            layout_dept = extract_department_advanced_layout(pdf_path, page_num)
            if layout_dept:
                employee["department"] = layout_dept
                print(f"Successfully extracted Department using advanced layout detection: {layout_dept}")

                # If we found a department, update the section as well
                if employee["department"] and not employee["section"]:
                    # Try to use the first part of department (before any dash)
                    dept_parts = employee["department"].split(" - ")
                    if len(dept_parts) > 0:
                        employee["section"] = dept_parts[0].strip()
                        print(f"Derived Section from Department: {employee['section']}")
                    else:
                        # If no dash, just use the department as section
                        employee["section"] = employee["department"]
                        print(f"Using Department as Section: {employee['section']}")

                    # Special case for ABUAKWA AREA - MINISTERS
                    if "ABUAKWA AREA" in employee["department"]:
                        employee["section"] = "ABUAKWA"
                        print(f"Set Section to ABUAKWA based on department pattern")

        # Only add employees with at least some data and a valid Employee No.
        if employee["employee_id"] and (employee["name"] or employee["basic_salary"] or employee["net_pay"]):
            # Final standardization of all dictionaries
            if 'enhanced_payroll_dictionaries' in globals() and enhanced_payroll_dictionaries is not None:
                try:
                    # Standardize earnings dictionary
                    if employee["earnings"]:
                        standardized_earnings = {}
                        for key, value in employee["earnings"].items():
                            std_key = enhanced_payroll_dictionaries.get_standardized_name(SECTION_EARNINGS, key)
                            standardized_earnings[std_key] = value
                        employee["earnings"] = standardized_earnings
                        print("Standardized earnings dictionary")

                    # Standardize deductions dictionary
                    if employee["deductions"]:
                        standardized_deductions = {}
                        for key, value in employee["deductions"].items():
                            std_key = enhanced_payroll_dictionaries.get_standardized_name(SECTION_DEDUCTIONS, key)
                            standardized_deductions[std_key] = value
                        employee["deductions"] = standardized_deductions
                        print("Standardized deductions dictionary")

                    # Standardize loan types
                    if employee["loan_details"]:
                        standardized_loan_details = {}
                        for loan_type, loan_info in employee["loan_details"].items():
                            std_loan_type = enhanced_payroll_dictionaries.get_standardized_name(SECTION_LOANS, loan_type)
                            if std_loan_type != loan_type:
                                print(f"Final standardization of loan type: {loan_type} -> {std_loan_type}")
                            standardized_loan_details[std_loan_type] = loan_info
                        employee["loan_details"] = standardized_loan_details
                        print("Standardized loan details dictionary")
                except Exception as e:
                    print(f"Error in final standardization: {e}")

            # Ensure employee name is not the same as section
            if employee.get("name") and employee.get("section") and employee["name"] == employee["section"]:
                print(f"WARNING: Employee name '{employee['name']}' is the same as section. Attempting to find better name.")

                # Try to extract a better name from the payslip text
                payslip_text = payslip.get("text", "")
                if payslip_text:
                    # Look for patterns that might indicate the actual name
                    name_patterns = [
                        r"Employee Name:?\s*([A-Z][A-Za-z\s\.\-]{2,30})",
                        r"Name:?\s*([A-Z][A-Za-z\s\.\-]{2,30})",
                        r"([A-Z][A-Za-z\s\.\-]{2,30})\s+Employee No\.",
                        r"Employee No\.\s*" + re.escape(employee.get("employee_id", "")) + r"\s+([A-Z][A-Za-z\s\.\-]{2,30})"
                    ]

                    for pattern in name_patterns:
                        match = re.search(pattern, payslip_text)
                        if match:
                            potential_name = match.group(1).strip()
                            # Validate that it looks like a name (not a department or other text)
                            if (re.match(r'^[A-Z][A-Za-z\s\.\-]{2,30}$', potential_name) and
                                not re.search(r'AREA|DISTRICT|REGION|DEPARTMENT|SECTION', potential_name) and
                                potential_name != employee["section"]):
                                print(f"Found better name: {potential_name}")
                                employee["name"] = potential_name
                                break

            # Validate the Employee No. format - COP####, PW####, SEC####, E####
            if re.match(r'^(COP\d{4}|PW\d{4}|SEC\d{4}|E\d{4})$', str(employee["employee_id"])):
                print(f"Valid Employee No. format: {employee['employee_id']}")
                employees.append(employee)
            else:
                print(f"WARNING: Employee No. doesn't match expected format: {employee['employee_id']}")
                # Still add it but with a warning
                employees.append(employee)

    print(f"Successfully extracted data for {len(employees)} employees")

    # If we didn't find many employees, try a more aggressive approach
    if len(employees) < 10:
        print("Few employees found. Trying more aggressive pattern matching...")

        # Combine all payslip texts for pattern matching
        all_text = "\n".join([p["text"] for p in payslips])

        # Look for patterns like "Employee Name" followed by a name
        name_matches = re.finditer(r"([A-Z]+\s+[A-Z]+\s+[A-Z]+)\s+Employee Name", all_text)
        for i, match in enumerate(name_matches):
            name = match.group(1).strip()

            # Create a basic employee record
            employee = {
                "employee_id": f"Employee_{i+1}",
                "ssf_no": None,
                "ghana_card_id": None,
                "name": name,
                "job_title": None,
                "department": None,  # Added department field
                "section": None,     # Added section field
                "basic_salary": None,
                "gross_salary": None,
                "net_pay": None,
                "deductions": {},
                "earnings": {},
                "month": None,
                "year": None,
                "loan": None,
                "bank_details": None,
                "employer_contributions": {},
                "loan_details": {},  # Dictionary for detailed loan information
                "personal_details_changed": False,  # Flag for personal details changes
                "page_num": 0
            }

            # Look for salary information near the name
            context = all_text[max(0, match.start() - 500):min(len(all_text), match.end() + 500)]

            # Try to find gross salary
            gross_match = re.search(r"GROSS SALARY\s+([\d,.]+)", context)
            if gross_match:
                employee["gross_salary"] = gross_match.group(1).strip()

            # Try to find net pay
            net_match = re.search(r"NET PAY\s+([\d,.]+)", context)
            if net_match:
                employee["net_pay"] = net_match.group(1).strip()

            # Try to find SSF number
            ssf_match = re.search(r"([A-Z0-9]+)\s+SSF No", context)
            if ssf_match:
                employee["ssf_no"] = ssf_match.group(1).strip()

            # Try to find Ghana Card ID
            ghana_card_match = re.search(r"(GHA-\d+-\d+)\s+Ghana Card ID", context)
            if ghana_card_match:
                employee["ghana_card_id"] = ghana_card_match.group(1).strip()

            # Try to find job title
            job_title_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Job Title", context)
            if job_title_match:
                employee["job_title"] = job_title_match.group(1).strip()

            # Try to find department
            dept_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Department", context)
            if dept_match:
                employee["department"] = dept_match.group(1).strip()

            # Try to find section
            section_match = re.search(r"([A-Z][A-Za-z\s]+)\s+Section", context)
            if section_match:
                employee["section"] = section_match.group(1).strip()

            # Try to find month and year
            period_match = re.search(r"([A-Za-z]+)\s+(\d{4})\s+Period", context)
            if period_match:
                employee["month"] = period_match.group(1).strip()
                employee["year"] = period_match.group(2).strip()

            employees.append(employee)

        print(f"Aggressive approach found {len(employees)} employees")

    return employees

def standardize_comparison_data(comparison_data):
    """
    Standardize all field names in the comparison data using dictionaries.

    This function ensures that all field names (earnings, deductions, loans) are
    standardized according to the dictionaries before generating reports.

    Args:
        comparison_data: List of comparison data dictionaries

    Returns:
        List of comparison data with standardized field names
    """
    # Import enhanced dictionary for standardization
    global enhanced_payroll_dictionaries
    if 'enhanced_payroll_dictionaries' not in globals() or enhanced_payroll_dictionaries is None:
        try:
            import enhanced_payroll_dictionaries
            print("Imported enhanced payroll dictionary for standardization in standardize_comparison_data")

            # Verify dictionary is loaded correctly
            dictionary = enhanced_payroll_dictionaries.load_dictionary()

            # Count items in each section
            earnings_count = len(dictionary.get("EARNINGS", {}).get("items", {}))
            deductions_count = len(dictionary.get("DEDUCTIONS", {}).get("items", {}))
            loans_count = len(dictionary.get("LOANS", {}).get("items", {}))

            print(f"Loaded dictionary in standardize_comparison_data - Earnings: {earnings_count} items, Deductions: {deductions_count} items, Loans: {loans_count} items")

            # Print some keys for verification
            if "EARNINGS" in dictionary and "items" in dictionary["EARNINGS"]:
                print(f"Earnings dictionary keys: {list(dictionary['EARNINGS']['items'].keys())[:5]}...")
            if "DEDUCTIONS" in dictionary and "items" in dictionary["DEDUCTIONS"]:
                print(f"Deductions dictionary keys: {list(dictionary['DEDUCTIONS']['items'].keys())[:5]}...")
            if "LOANS" in dictionary and "items" in dictionary["LOANS"]:
                print(f"Loans dictionary keys: {list(dictionary['LOANS']['items'].keys())[:5]}...")
        except ImportError as e:
            print(f"Warning: enhanced_payroll_dictionaries module not found in standardize_comparison_data. Standardization will be disabled. Error: {e}")
            enhanced_payroll_dictionaries = None
        except Exception as e:
            print(f"Error loading dictionary in standardize_comparison_data: {e}")
            enhanced_payroll_dictionaries = None

    # If dictionary is not available, return the original data
    if 'enhanced_payroll_dictionaries' not in globals() or enhanced_payroll_dictionaries is None:
        print("Dictionary not available. Skipping standardization.")
        return comparison_data

    # Create a deep copy of the comparison data to avoid modifying the original
    import copy
    standardized_data = copy.deepcopy(comparison_data)

    # Function to standardize a field name
    def standardize_field_name(field_name, field_type):
        try:
            if field_type == 'earnings':
                standardized = enhanced_payroll_dictionaries.get_standardized_name(SECTION_EARNINGS, field_name)
                if standardized != field_name:
                    print(f"Standardized earnings field: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'deductions':
                standardized = enhanced_payroll_dictionaries.get_standardized_name(SECTION_DEDUCTIONS, field_name)
                if standardized != field_name:
                    print(f"Standardized deductions field: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'loans':
                standardized = enhanced_payroll_dictionaries.get_standardized_name(SECTION_LOANS, field_name)
                if standardized != field_name:
                    print(f"Standardized loan field: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'personal_details':
                standardized = enhanced_payroll_dictionaries.get_standardized_name(SECTION_PERSONAL_DETAILS, field_name)
                if standardized != field_name:
                    print(f"Standardized personal details field: {field_name} -> {standardized}")
                return standardized
            elif field_type == 'bank_details':
                standardized = enhanced_payroll_dictionaries.get_standardized_name(SECTION_EMPLOYEE_BANK_DETAILS, field_name)
                if standardized != field_name:
                    print(f"Standardized bank details field: {field_name} -> {standardized}")
                return standardized
            else:
                return field_name
        except Exception as e:
            print(f"Error standardizing field name: {e}")
            return field_name

    # Process each employee in the comparison data
    for emp in standardized_data:
        # Standardize current earnings
        if "current_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["current_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["current_earnings"] = standardized_earnings

        # Standardize previous earnings
        if "previous_earnings" in emp:
            standardized_earnings = {}
            for key, value in emp["previous_earnings"].items():
                std_key = standardize_field_name(key, 'earnings')
                standardized_earnings[std_key] = value
            emp["previous_earnings"] = standardized_earnings

        # Standardize current deductions
        if "current_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["current_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["current_deductions"] = standardized_deductions

        # Standardize previous deductions
        if "previous_deductions" in emp:
            standardized_deductions = {}
            for key, value in emp["previous_deductions"].items():
                std_key = standardize_field_name(key, 'deductions')
                standardized_deductions[std_key] = value
            emp["previous_deductions"] = standardized_deductions

        # Standardize current loan details
        if "loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["loan_details"] = standardized_loans

        # Standardize previous loan details
        if "previous_loan_details" in emp:
            standardized_loans = {}
            for key, value in emp["previous_loan_details"].items():
                std_key = standardize_field_name(key, 'loans')
                standardized_loans[std_key] = value
            emp["previous_loan_details"] = standardized_loans

        # Standardize detailed changes
        if "detailed_changes" in emp:
            # Standardize earnings changes
            if "earnings" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["earnings"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'earnings')

            # Standardize deductions changes
            if "deductions" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["deductions"]:
                    if "type" in change:
                        change["type"] = standardize_field_name(change["type"], 'deductions')

            # Standardize loans changes
            if "loans" in emp["detailed_changes"]:
                for change in emp["detailed_changes"]["loans"]:
                    if "type" in change:
                        loan_type = change["type"]
                        # Handle loan types with additional info (e.g., "LOAN TYPE - BALANCE B/F")
                        if " - " in loan_type:
                            loan_type, additional = loan_type.split(" - ", 1)
                            std_loan_type = standardize_field_name(loan_type, 'loans')
                            change["type"] = f"{std_loan_type} - {additional}"
                        else:
                            change["type"] = standardize_field_name(loan_type, 'loans')

    print(f"Standardized {len(standardized_data)} employee records.")
    return standardized_data

def compare_payrolls(prev_data, curr_data, id_field="employee_id"):
    """
    Compare previous and current payroll data.

    Args:
        prev_data: List of employee data dictionaries from previous month
        curr_data: List of employee data dictionaries from current month
        id_field: The field to use as the unique identifier (default: "employee_id")
    """
    print("Comparing payroll data...")
    print(f"Using '{id_field}' as the unique identifier for comparison")

    # Update process phase to Pre-Auditing (Phase 3)
    update_process_phase("PRE-AUDITING", 3)

    comparison = []

    # Create a dictionary of previous employees by the specified ID field for easy lookup
    prev_dict = {emp[id_field]: emp for emp in prev_data if emp.get(id_field)}

    print(f"Previous payroll has {len(prev_data)} employees")
    print(f"Current payroll has {len(curr_data)} employees")

    # Function to safely convert amount strings to float for comparison
    def parse_amount(amount_str):
        if not amount_str:
            return 0.0
        try:
            # Remove any non-numeric characters except decimal point
            clean_str = ''.join(c for c in amount_str if c.isdigit() or c == '.')
            return float(clean_str)
        except (ValueError, TypeError):
            print(f"Warning: Could not parse amount '{amount_str}'")
            return 0.0

    # Function to format amount for display
    def format_amount(amount_str):
        if not amount_str:
            return "N/A"
        return amount_str

    # Function to calculate and format change
    def calculate_change(prev_val, curr_val, label, emp_id, department, section, prev_month=None, curr_month=None, employee_name=None):
        if prev_val > 0 and curr_val > 0 and prev_val != curr_val:
            diff = curr_val - prev_val

            # Format the employee identifier - store it for the detailed data structure
            # Include Employee No. and Department with proper formatting
            # Use the format "COP2626: Abuakwa Area - Ministers" with colon and dash
            if department and department != "Unknown":
                emp_identifier = f"{emp_id}: {department}"
            else:
                emp_identifier = f"{emp_id}"

            # Create change message with the format from the screenshot
            # Example: "Gross Salary changed from 11957.34 to 16277.34 (increase of 4320.00) in April 2025"
            change_msg = f"{label} changed from {prev_val:.2f} to {curr_val:.2f}"

            # Add month information if available
            month_info = ""
            if prev_month and curr_month:
                month_info = f" in {curr_month}"

            if diff > 0:
                change_msg += f" (increase of {diff:.2f}{month_info})"
            else:
                change_msg += f" (decrease of {abs(diff):.2f}{month_info})"

            # For the data structure, we need to include the employee identifier
            # This will be stripped out in the report generator
            full_change_msg = f"{emp_identifier}; {change_msg}"

            result = {
                "type": label,
                "previous": prev_val,
                "current": curr_val,
                "difference": diff,
                "employee_id": emp_id,
                "department": department,
                "section": section,
                "prev_month": prev_month,
                "curr_month": curr_month,
                "message": full_change_msg,
                "clean_message": change_msg
            }

            # Include employee name if provided
            if employee_name:
                result["employee_name"] = employee_name

            return result
        return None

    # Note: The name-section validation is now handled directly in the employee data extraction

    # Process current employees
    for i, curr_emp in enumerate(curr_data):
        if not curr_emp.get(id_field):
            print(f"Skipping employee with no {id_field}")
            continue

        emp_id = curr_emp[id_field]
        # Print progress for every employee for UI updates
        print(f"Comparing employee {i+1}/{len(curr_data)}: {emp_id}")

        # Get employee name if available
        emp_name = curr_emp.get("name", "Unknown")

        result = {
            "id": emp_id,
            "name": emp_name,
            "job_title": curr_emp.get("job_title", "Unknown"),
            "month": curr_emp.get("month", "Unknown"),
            "year": curr_emp.get("year", "Unknown"),
            "current_basic_salary": format_amount(curr_emp.get("basic_salary")),
            "current_gross_salary": format_amount(curr_emp.get("gross_salary")),
            "current_net_pay": format_amount(curr_emp.get("net_pay")),
            "current_deductions": curr_emp.get("deductions", {}),
            "current_earnings": curr_emp.get("earnings", {}),
            "current_bank_details": curr_emp.get("bank_details", {}),
            "current_employer_contributions": curr_emp.get("employer_contributions", {}),
            "current_loan_details": curr_emp.get("loan_details", {}),
            "changes": [],
            "detailed_changes": {
                "salary": [],
                "deductions": [],
                "earnings": [],
                "personal_details": [],
                "employer_contributions": [],
                "bank_details": [],
                "loans": [],
                "other": []
            },
            "has_focus_area_changes": False  # Flag to indicate if there are changes in focus areas
        }

        # Check if employee exists in previous payroll
        if emp_id in prev_dict:
            prev_emp = prev_dict[emp_id]
            print(f"Found matching employee in previous payroll")

            # Add previous data to result
            result["previous_basic_salary"] = format_amount(prev_emp.get("basic_salary"))
            result["previous_gross_salary"] = format_amount(prev_emp.get("gross_salary"))
            result["previous_net_pay"] = format_amount(prev_emp.get("net_pay"))
            result["previous_deductions"] = prev_emp.get("deductions", {})
            result["previous_earnings"] = prev_emp.get("earnings", {})
            result["previous_bank_details"] = prev_emp.get("bank_details", {})
            result["previous_employer_contributions"] = prev_emp.get("employer_contributions", {})
            result["previous_loan_details"] = prev_emp.get("loan_details", {})

            # Get employee details for change messages
            emp_id = curr_emp.get(id_field, "Unknown")
            department = curr_emp.get("department", "Unknown")
            section = curr_emp.get("section", "Unknown")

            # Get month information
            prev_month = prev_emp.get("month", "Unknown")
            curr_month = curr_emp.get("month", "Unknown")

            # Check for personal details changes

            # Check for Employee No. changes (should be rare but possible)
            prev_emp_id = prev_emp.get("employee_id")
            if prev_emp_id and emp_id and prev_emp_id != emp_id:
                change_msg = f"Employee No. changed from '{prev_emp_id}' to '{emp_id}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Employee No.",
                    "previous": prev_emp_id,
                    "current": emp_id,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for Employee Name changes
            prev_name = prev_emp.get("name")
            curr_name = curr_emp.get("name")
            if prev_name and curr_name and prev_name != curr_name:
                change_msg = f"Employee Name changed from '{prev_name}' to '{curr_name}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Employee Name",
                    "previous": prev_name,
                    "current": curr_name,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for SSF No. changes
            prev_ssf = prev_emp.get("ssf_no")
            curr_ssf = curr_emp.get("ssf_no")
            if prev_ssf and curr_ssf and prev_ssf != curr_ssf:
                change_msg = f"SSF No. changed from '{prev_ssf}' to '{curr_ssf}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "SSF No.",
                    "previous": prev_ssf,
                    "current": curr_ssf,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for Ghana Card ID changes
            prev_ghana = prev_emp.get("ghana_card_id")
            curr_ghana = curr_emp.get("ghana_card_id")
            if prev_ghana and curr_ghana and prev_ghana != curr_ghana:
                change_msg = f"Ghana Card ID changed from '{prev_ghana}' to '{curr_ghana}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Ghana Card ID",
                    "previous": prev_ghana,
                    "current": curr_ghana,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for Section changes
            prev_section = prev_emp.get("section")
            curr_section = curr_emp.get("section")
            if prev_section and curr_section and prev_section != curr_section:
                change_msg = f"Section changed from '{prev_section}' to '{curr_section}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Section",
                    "previous": prev_section,
                    "current": curr_section,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for Department changes
            prev_dept = prev_emp.get("department")
            curr_dept = curr_emp.get("department")
            if prev_dept and curr_dept and prev_dept != curr_dept:
                change_msg = f"Department changed from '{prev_dept}' to '{curr_dept}' in {curr_month}"
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Department",
                    "previous": prev_dept,
                    "current": curr_dept,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Check for job title changes
            prev_job = prev_emp.get("job_title")
            curr_job = curr_emp.get("job_title")
            if prev_job and curr_job and prev_job != curr_job:
                change_msg = f"Job title changed from '{prev_job}' to '{curr_job}' in {curr_month}"
                # Format the employee identifier with proper formatting
                if department and department != "Unknown":
                    emp_identifier = f"{emp_id}: {department}"
                else:
                    emp_identifier = f"{emp_id}"
                full_change_msg = f"{emp_identifier}; {change_msg}"
                result["changes"].append(full_change_msg)
                result["detailed_changes"]["personal_details"].append({
                    "type": "Job Title",
                    "previous": prev_job,
                    "current": curr_job,
                    "employee_id": emp_id,
                    "department": department,
                    "section": section,
                    "prev_month": prev_month,
                    "curr_month": curr_month,
                    "message": full_change_msg,
                    "clean_message": change_msg
                })
                result["has_focus_area_changes"] = True
                print(change_msg)

            # Get employee name for change records
            emp_name = curr_emp.get("name", "")

            # Check for basic salary changes
            prev_basic = parse_amount(prev_emp.get("basic_salary"))
            curr_basic = parse_amount(curr_emp.get("basic_salary"))
            basic_change = calculate_change(prev_basic, curr_basic, "Basic Salary", emp_id, department, section, prev_month, curr_month, emp_name)
            if basic_change:
                result["changes"].append(basic_change["message"])
                result["detailed_changes"]["salary"].append(basic_change)
                result["has_focus_area_changes"] = True
                print(basic_change["message"])

            # Check for gross salary changes
            prev_gross = parse_amount(prev_emp.get("gross_salary"))
            curr_gross = parse_amount(curr_emp.get("gross_salary"))
            gross_change = calculate_change(prev_gross, curr_gross, "Gross Salary", emp_id, department, section, prev_month, curr_month, emp_name)
            if gross_change:
                result["changes"].append(gross_change["message"])
                result["detailed_changes"]["salary"].append(gross_change)
                result["has_focus_area_changes"] = True
                print(gross_change["message"])

            # Check for net pay changes
            prev_net = parse_amount(prev_emp.get("net_pay"))
            curr_net = parse_amount(curr_emp.get("net_pay"))
            net_change = calculate_change(prev_net, curr_net, "Net Pay", emp_id, department, section, prev_month, curr_month, emp_name)
            if net_change:
                result["changes"].append(net_change["message"])
                result["detailed_changes"]["salary"].append(net_change)
                result["has_focus_area_changes"] = True
                print(net_change["message"])

            # Check for deduction changes
            prev_deductions = prev_emp.get("deductions", {})
            curr_deductions = curr_emp.get("deductions", {})

            # Check for changes in existing deductions
            for deduction_name in set(prev_deductions.keys()) | set(curr_deductions.keys()):
                prev_amount = parse_amount(prev_deductions.get(deduction_name, "0"))
                curr_amount = parse_amount(curr_deductions.get(deduction_name, "0"))

                # New deduction
                if deduction_name not in prev_deductions and deduction_name in curr_deductions:
                    # Use a default month name if curr_month is None
                    month_display = curr_month if curr_month else "current period"
                    change_msg = f"New {deduction_name} ({curr_amount:.2f}) in {month_display}"
                    # Format the employee identifier with proper formatting
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["deductions"].append({
                        "type": deduction_name,
                        "previous": 0,
                        "current": curr_amount,
                        "difference": curr_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg,
                        "employee_name": emp_name
                    })
                    # Skip setting focus area flag for "Loan Deduction" as per requirements
                    if deduction_name.lower() != "loan" and "loan" not in deduction_name.lower():
                        result["has_focus_area_changes"] = True
                    print(change_msg)

                # Removed deduction
                elif deduction_name in prev_deductions and deduction_name not in curr_deductions:
                    # Use a default month name if prev_month is None
                    month_display = prev_month if prev_month else "previous period"
                    change_msg = f"Removed {deduction_name} (was {prev_amount:.2f} in {month_display})"
                    # Format the employee identifier with proper formatting
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["deductions"].append({
                        "type": deduction_name,
                        "previous": prev_amount,
                        "current": 0,
                        "difference": -prev_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg,
                        "employee_name": emp_name
                    })
                    # Skip setting focus area flag for "Loan Deduction" as per requirements
                    if deduction_name.lower() != "loan" and "loan" not in deduction_name.lower():
                        result["has_focus_area_changes"] = True
                    print(change_msg)

                # Changed deduction amount
                elif prev_amount != curr_amount:
                    deduction_change = calculate_change(prev_amount, curr_amount, f"{deduction_name}", emp_id, department, section, prev_month, curr_month, emp_name)
                    if deduction_change:
                        result["changes"].append(deduction_change["message"])
                        result["detailed_changes"]["deductions"].append(deduction_change)
                        # Skip setting focus area flag for "Loan Deduction" as per requirements
                        if deduction_name.lower() != "loan" and "loan" not in deduction_name.lower():
                            result["has_focus_area_changes"] = True
                        print(deduction_change["message"])

            # Check for earnings/allowances changes
            prev_earnings = prev_emp.get("earnings", {})
            curr_earnings = curr_emp.get("earnings", {})

            # Check for changes in existing earnings
            for earning_name in set(prev_earnings.keys()) | set(curr_earnings.keys()):
                prev_amount = parse_amount(prev_earnings.get(earning_name, "0"))
                curr_amount = parse_amount(curr_earnings.get(earning_name, "0"))

                # New earning
                if earning_name not in prev_earnings and earning_name in curr_earnings:
                    # Use a default month name if curr_month is None
                    month_display = curr_month if curr_month else "current period"
                    change_msg = f"New {earning_name} ({curr_amount:.2f}) in {month_display}"
                    # Format the employee identifier with proper formatting
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["earnings"].append({
                        "type": earning_name,
                        "previous": 0,
                        "current": curr_amount,
                        "difference": curr_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg,
                        "employee_name": emp_name
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Removed earning
                elif earning_name in prev_earnings and earning_name not in curr_earnings:
                    # Use a default month name if prev_month is None
                    month_display = prev_month if prev_month else "previous period"
                    change_msg = f"Removed {earning_name} (was {prev_amount:.2f} in {month_display})"
                    # Format the employee identifier with proper formatting
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["earnings"].append({
                        "type": earning_name,
                        "previous": prev_amount,
                        "current": 0,
                        "difference": -prev_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg,
                        "employee_name": emp_name
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Changed earning amount
                elif prev_amount != curr_amount:
                    earning_change = calculate_change(prev_amount, curr_amount, f"{earning_name}", emp_id, department, section, prev_month, curr_month, emp_name)
                    if earning_change:
                        result["changes"].append(earning_change["message"])
                        result["detailed_changes"]["earnings"].append(earning_change)
                        result["has_focus_area_changes"] = True
                        print(earning_change["message"])

            # Check for bank details changes
            prev_bank = prev_emp.get("bank_details", {})
            curr_bank = curr_emp.get("bank_details", {})

            # Compare bank details
            if prev_bank and curr_bank:
                # Check for bank name changes
                prev_bank_name = prev_bank.get("bank_name")
                curr_bank_name = curr_bank.get("bank_name")
                if prev_bank_name and curr_bank_name and prev_bank_name != curr_bank_name:
                    change_msg = f"Bank changed from '{prev_bank_name}' to '{curr_bank_name}' in {curr_month}"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["bank_details"].append({
                        "type": "Bank Name",
                        "previous": prev_bank_name,
                        "current": curr_bank_name,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Check for account number changes
                prev_account = prev_bank.get("account_number")
                curr_account = curr_bank.get("account_number")
                if prev_account and curr_account and prev_account != curr_account:
                    change_msg = f"Bank account number changed from '{prev_account}' to '{curr_account}' in {curr_month}"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["bank_details"].append({
                        "type": "Account Number",
                        "previous": prev_account,
                        "current": curr_account,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

            # Check for loan details changes
            prev_loans = prev_emp.get("loan_details", {})
            curr_loans = curr_emp.get("loan_details", {})

            # Compare all loan details
            all_loan_keys = set(list(prev_loans.keys()) + list(curr_loans.keys()))
            for loan_type in all_loan_keys:
                prev_loan = prev_loans.get(loan_type, {})
                curr_loan = curr_loans.get(loan_type, {})

                # New loan
                if loan_type not in prev_loans and loan_type in curr_loans:
                    balance_bf = curr_loan.get("balance_bf", 0)
                    current_deduction = curr_loan.get("current_deduction", 0)
                    outstanding_balance = curr_loan.get("outstanding_balance", 0)

                    # Format according to new protocol: quote LOAN type first, then changes
                    change_msg = f"{loan_type} LOAN: New loan with Balance B/F of {format_amount(balance_bf)}, Current Deduction of {format_amount(current_deduction)}, and Outstanding Balance of {format_amount(outstanding_balance)} added in {curr_month}"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["loans"].append({
                        "type": loan_type,
                        "previous": "Not Found",
                        "current": f"Balance B/F: {format_amount(balance_bf)}, Current Deduction: {format_amount(current_deduction)}, Outstanding Balance: {format_amount(outstanding_balance)}",
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Removed loan
                elif loan_type in prev_loans and loan_type not in curr_loans:
                    balance_bf = prev_loan.get("balance_bf", 0)
                    current_deduction = prev_loan.get("current_deduction", 0)
                    outstanding_balance = prev_loan.get("outstanding_balance", 0)

                    # Format according to new protocol: quote LOAN type first, then changes
                    change_msg = f"{loan_type} LOAN: Balance of {format_amount(outstanding_balance)} in {prev_month} not found on current Payroll"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["loans"].append({
                        "type": loan_type,
                        "previous": f"Balance B/F: {format_amount(balance_bf)}, Current Deduction: {format_amount(current_deduction)}, Outstanding Balance: {format_amount(outstanding_balance)}",
                        "current": "Not Found",
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Changed loan details
                elif loan_type in prev_loans and loan_type in curr_loans:
                    # Check for Balance B/F changes
                    prev_balance_bf = prev_loan.get("balance_bf", 0)
                    curr_balance_bf = curr_loan.get("balance_bf", 0)
                    if prev_balance_bf != curr_balance_bf:
                        # Format according to new protocol: quote LOAN type first, then changes
                        change_msg = f"{loan_type} LOAN: Balance B/F changed from {format_amount(prev_balance_bf)} to {format_amount(curr_balance_bf)} in {curr_month}"
                        if department and department != "Unknown":
                            emp_identifier = f"{emp_id}: {department}"
                        else:
                            emp_identifier = f"{emp_id}"
                        full_change_msg = f"{emp_identifier}; {change_msg}"
                        result["changes"].append(full_change_msg)
                        result["detailed_changes"]["loans"].append({
                            "type": f"{loan_type} - Balance B/F",
                            "previous": format_amount(prev_balance_bf),
                            "current": format_amount(curr_balance_bf),
                            "employee_id": emp_id,
                            "department": department,
                            "section": section,
                            "prev_month": prev_month,
                            "curr_month": curr_month,
                            "message": full_change_msg,
                            "clean_message": change_msg
                        })
                        result["has_focus_area_changes"] = True
                        print(change_msg)

                    # Check for Current Deduction changes
                    prev_deduction = prev_loan.get("current_deduction", 0)
                    curr_deduction = curr_loan.get("current_deduction", 0)
                    if prev_deduction != curr_deduction:
                        # Format according to new protocol: quote LOAN type first, then changes
                        if prev_deduction < curr_deduction:
                            change_msg = f"{loan_type} LOAN: Current deduction increased from {format_amount(prev_deduction)} to {format_amount(curr_deduction)} in {curr_month}"
                        else:
                            change_msg = f"{loan_type} LOAN: Current deduction decreased from {format_amount(prev_deduction)} to {format_amount(curr_deduction)} in {curr_month}"

                        if department and department != "Unknown":
                            emp_identifier = f"{emp_id}: {department}"
                        else:
                            emp_identifier = f"{emp_id}"
                        full_change_msg = f"{emp_identifier}; {change_msg}"
                        result["changes"].append(full_change_msg)
                        result["detailed_changes"]["loans"].append({
                            "type": f"{loan_type} - Current Deduction",
                            "previous": format_amount(prev_deduction),
                            "current": format_amount(curr_deduction),
                            "employee_id": emp_id,
                            "department": department,
                            "section": section,
                            "prev_month": prev_month,
                            "curr_month": curr_month,
                            "message": full_change_msg,
                            "clean_message": change_msg
                        })
                        result["has_focus_area_changes"] = True
                        print(change_msg)

                    # Check for Outstanding Balance changes
                    prev_outstanding = prev_loan.get("outstanding_balance", 0)
                    curr_outstanding = curr_loan.get("outstanding_balance", 0)
                    if prev_outstanding != curr_outstanding:
                        # Format according to new protocol: quote LOAN type first, then changes
                        change_msg = f"{loan_type} LOAN: Outstanding balance changed from {format_amount(prev_outstanding)} to {format_amount(curr_outstanding)} in {curr_month}"
                        if department and department != "Unknown":
                            emp_identifier = f"{emp_id}: {department}"
                        else:
                            emp_identifier = f"{emp_id}"
                        full_change_msg = f"{emp_identifier}; {change_msg}"
                        result["changes"].append(full_change_msg)
                        result["detailed_changes"]["loans"].append({
                            "type": f"{loan_type} - Outstanding Balance",
                            "previous": format_amount(prev_outstanding),
                            "current": format_amount(curr_outstanding),
                            "employee_id": emp_id,
                            "department": department,
                            "section": section,
                            "prev_month": prev_month,
                            "curr_month": curr_month,
                            "message": full_change_msg,
                            "clean_message": change_msg
                        })
                        result["has_focus_area_changes"] = True
                        print(change_msg)

                    # Verify the relationship: BALANCE B/F - CURRENT DEDUCTION = OUTSTANDING BALANCE
                    expected_curr_outstanding = curr_balance_bf - curr_deduction
                    if abs(expected_curr_outstanding - curr_outstanding) > 0.01:  # Allow for small rounding differences
                        # Format according to new protocol: quote LOAN type first, then changes
                        change_msg = f"{loan_type} LOAN: Has inconsistent values in {curr_month}: Balance B/F ({format_amount(curr_balance_bf)}) - Current Deduction ({format_amount(curr_deduction)}) ≠ Outstanding Balance ({format_amount(curr_outstanding)})"
                        if department and department != "Unknown":
                            emp_identifier = f"{emp_id}: {department}"
                        else:
                            emp_identifier = f"{emp_id}"
                        full_change_msg = f"{emp_identifier}; {change_msg}"
                        result["changes"].append(full_change_msg)
                        result["detailed_changes"]["loans"].append({
                            "type": f"{loan_type} - Inconsistency",
                            "previous": "N/A",
                            "current": f"Balance B/F: {format_amount(curr_balance_bf)}, Current Deduction: {format_amount(curr_deduction)}, Outstanding Balance: {format_amount(curr_outstanding)}, Expected Outstanding: {format_amount(expected_curr_outstanding)}",
                            "employee_id": emp_id,
                            "department": department,
                            "section": section,
                            "prev_month": prev_month,
                            "curr_month": curr_month,
                            "message": full_change_msg,
                            "clean_message": change_msg
                        })
                        result["has_focus_area_changes"] = True
                        print(change_msg)

            # Check for employer contributions changes
            prev_contrib = prev_emp.get("employer_contributions", {})
            curr_contrib = curr_emp.get("employer_contributions", {})

            # Compare all employer contributions
            all_contrib_keys = set(list(prev_contrib.keys()) + list(curr_contrib.keys()))
            for contrib_name in all_contrib_keys:
                prev_amount = parse_amount(prev_contrib.get(contrib_name, 0))
                curr_amount = parse_amount(curr_contrib.get(contrib_name, 0))

                # New contribution
                if contrib_name not in prev_contrib and contrib_name in curr_contrib:
                    change_msg = f"New employer contribution '{contrib_name}' of {format_amount(curr_amount)} added in {curr_month}"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["employer_contributions"].append({
                        "type": contrib_name,
                        "previous": 0,
                        "current": curr_amount,
                        "difference": curr_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Removed contribution
                elif contrib_name in prev_contrib and contrib_name not in curr_contrib:
                    change_msg = f"Employer contribution '{contrib_name}' of {format_amount(prev_amount)} removed in {curr_month}"
                    if department and department != "Unknown":
                        emp_identifier = f"{emp_id}: {department}"
                    else:
                        emp_identifier = f"{emp_id}"
                    full_change_msg = f"{emp_identifier}; {change_msg}"
                    result["changes"].append(full_change_msg)
                    result["detailed_changes"]["employer_contributions"].append({
                        "type": contrib_name,
                        "previous": prev_amount,
                        "current": 0,
                        "difference": -prev_amount,
                        "employee_id": emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "curr_month": curr_month,
                        "message": full_change_msg,
                        "clean_message": change_msg
                    })
                    result["has_focus_area_changes"] = True
                    print(change_msg)

                # Changed contribution amount
                elif prev_amount != curr_amount:
                    contrib_change = calculate_change(prev_amount, curr_amount, f"{contrib_name}", emp_id, department, section, prev_month, curr_month, emp_name)
                    if contrib_change:
                        result["changes"].append(contrib_change["message"])
                        result["detailed_changes"]["employer_contributions"].append(contrib_change)
                        result["has_focus_area_changes"] = True
                        print(contrib_change["message"])

            # If no changes were detected
            if not result["changes"]:
                result["changes"].append("No significant changes detected")
                print("No significant changes detected")

        else:
            # Get employee details for new employee
            emp_id = curr_emp.get(id_field, "Unknown")
            department = curr_emp.get("department", "Unknown")
            section = curr_emp.get("section", "Unknown")
            curr_month = curr_emp.get("month", "Unknown")
            emp_name = curr_emp.get("name", "Unknown")

            # Use a default month name if curr_month is None
            month_display = curr_month if curr_month else "current period"
            change_msg = f"New employee (not in previous payroll) in {month_display}"
            # Format the employee identifier with proper formatting
            if department and department != "Unknown":
                emp_identifier = f"{emp_id}: {department}"
            else:
                emp_identifier = f"{emp_id}"
            full_change_msg = f"{emp_identifier}; {change_msg}"
            result["changes"].append(full_change_msg)
            result["detailed_changes"]["other"].append({
                "type": "Employment Status",
                "employee_id": emp_id,
                "department": department,
                "section": section,
                "curr_month": curr_month,
                "message": full_change_msg,
                "clean_message": change_msg,
                "employee_name": emp_name
            })
            print(change_msg)

        comparison.append(result)

    # Check for employees who were removed
    for prev_emp_id, prev_emp in prev_dict.items():
        if not any(emp.get(id_field) == prev_emp_id for emp in curr_data):
            emp_name = prev_emp.get("name", "Unknown")
            department = prev_emp.get("department", "Unknown")
            section = prev_emp.get("section", "Unknown")

            prev_month = prev_emp.get("month", "Unknown")
            # Use a default month name if prev_month is None
            month_display = prev_month if prev_month else "previous period"
            change_msg = f"Employee removed (not in current payroll) after {month_display}"
            # Format the employee identifier with proper formatting
            if department and department != "Unknown":
                emp_identifier = f"{prev_emp_id}: {department}"
            else:
                emp_identifier = f"{prev_emp_id}"
            full_change_msg = f"{emp_identifier}; {change_msg}"
            print(change_msg)

            comparison.append({
                "id": prev_emp_id,
                "name": emp_name,
                "job_title": prev_emp.get("job_title", "Unknown"),
                "month": prev_emp.get("month", "Unknown"),
                "year": prev_emp.get("year", "Unknown"),
                "department": department,
                "section": section,
                "previous_basic_salary": format_amount(prev_emp.get("basic_salary")),
                "previous_gross_salary": format_amount(prev_emp.get("gross_salary")),
                "previous_net_pay": format_amount(prev_emp.get("net_pay")),
                "previous_deductions": prev_emp.get("deductions", {}),
                "previous_earnings": prev_emp.get("earnings", {}),
                "current_basic_salary": "N/A",
                "current_gross_salary": "N/A",
                "current_net_pay": "N/A",
                "current_deductions": {},
                "current_earnings": {},
                "changes": [full_change_msg],
                "detailed_changes": {
                    "other": [{
                        "type": "Employment Status",
                        "employee_id": prev_emp_id,
                        "department": department,
                        "section": section,
                        "prev_month": prev_month,
                        "message": full_change_msg,
                        "clean_message": change_msg,
                        "employee_name": emp_name
                    }]
                }
            })

    print(f"Comparison complete. Found {len(comparison)} entries.")
    return comparison

def generate_reports_old(comparison_data, output_dir):
    """Generate reports in various formats."""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

    print("Generating reports...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create Excel report
    excel_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.xlsx")
    wb = Workbook()

    # Summary worksheet
    ws_summary = wb.active
    ws_summary.title = "Summary"

    # Add title and timestamp
    ws_summary.cell(row=1, column=1, value="Payroll Comparison Report")
    ws_summary.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws_summary.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Add statistics
    ws_summary.cell(row=4, column=1, value="Statistics:").font = Font(bold=True)
    ws_summary.cell(row=5, column=1, value="Total employees analyzed:")
    ws_summary.cell(row=5, column=2, value=len(comparison_data))

    # Count employees with changes
    employees_with_changes = sum(1 for emp in comparison_data if any(change != "No significant changes detected" for change in emp["changes"]))
    ws_summary.cell(row=6, column=1, value="Employees with changes:")
    ws_summary.cell(row=6, column=2, value=employees_with_changes)

    # Count new employees
    new_employees = sum(1 for emp in comparison_data if "New employee" in str(emp["changes"][0]))
    ws_summary.cell(row=7, column=1, value="New employees:")
    ws_summary.cell(row=7, column=2, value=new_employees)

    # Count removed employees
    removed_employees = sum(1 for emp in comparison_data if "Employee removed" in str(emp["changes"][0]))
    ws_summary.cell(row=8, column=1, value="Removed employees:")
    ws_summary.cell(row=8, column=2, value=removed_employees)

    # Group employees by department
    departments = {}
    for emp in comparison_data:
        department = emp.get("department", "Unknown")
        if department not in departments:
            departments[department] = []
        departments[department].append(emp)

    # Create a worksheet for each department
    for department, employees in departments.items():
        # Create a safe worksheet name (max 31 chars, no invalid chars)
        safe_dept_name = department[:25].replace('/', '_').replace('\\', '_').replace('?', '_').replace('*', '_').replace('[', '_').replace(']', '_').replace(':', '_')
        ws_dept = wb.create_sheet(safe_dept_name)

        # Add department title
        ws_dept.cell(row=1, column=1, value=f"Department: {department}")
        ws_dept.cell(row=1, column=1).font = Font(bold=True, size=14)

        # Add headers
        headers = ["Employee ID", "Section", "Employee Name", "Previous Salary", "Current Salary", "Change",
                  "Previous Net Pay", "Current Net Pay", "Change", "Changes"]

        # Style the header row
        header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
        header_font = Font(bold=True)
        header_border = Border(bottom=Side(style='thin'))

        for col, header in enumerate(headers, 1):
            cell = ws_dept.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = header_border

        # Add employee data
        for row, emp in enumerate(employees, 4):
            ws_dept.cell(row=row, column=1, value=emp["id"])
            ws_dept.cell(row=row, column=2, value=emp.get("section", "Unknown"))
            ws_dept.cell(row=row, column=3, value=emp.get("name", "Unknown"))

            # Salary data
            prev_salary = emp.get("previous_basic_salary", "N/A")
            curr_salary = emp.get("current_basic_salary", "N/A")
            ws_dept.cell(row=row, column=4, value=prev_salary)
            ws_dept.cell(row=row, column=5, value=curr_salary)

            # Calculate salary change
            if prev_salary != "N/A" and curr_salary != "N/A":
                try:
                    prev_val = float(''.join(c for c in prev_salary if c.isdigit() or c == '.'))
                    curr_val = float(''.join(c for c in curr_salary if c.isdigit() or c == '.'))
                    if prev_val > 0:
                        diff = curr_val - prev_val
                        if diff > 0:
                            change_text = f"Increase: {diff:.2f}"
                        else:
                            change_text = f"Decrease: {abs(diff):.2f}"
                        ws_dept.cell(row=row, column=6, value=change_text)
                except (ValueError, TypeError):
                    ws_dept.cell(row=row, column=6, value="N/A")
            else:
                ws_dept.cell(row=row, column=6, value="N/A")

            # Net pay data
            prev_net = emp.get("previous_net_pay", "N/A")
            curr_net = emp.get("current_net_pay", "N/A")
            ws_dept.cell(row=row, column=7, value=prev_net)
            ws_dept.cell(row=row, column=8, value=curr_net)

            # Calculate net pay change
            if prev_net != "N/A" and curr_net != "N/A":
                try:
                    prev_val = float(''.join(c for c in prev_net if c.isdigit() or c == '.'))
                    curr_val = float(''.join(c for c in curr_net if c.isdigit() or c == '.'))
                    if prev_val > 0:
                        diff = curr_val - prev_val
                        if diff > 0:
                            change_text = f"Increase: {diff:.2f}"
                        else:
                            change_text = f"Decrease: {abs(diff):.2f}"
                        ws_dept.cell(row=row, column=9, value=change_text)
                except (ValueError, TypeError):
                    ws_dept.cell(row=row, column=9, value="N/A")
            else:
                ws_dept.cell(row=row, column=9, value="N/A")

            # Changes
            changes_text = "\n".join(emp["changes"])
            ws_dept.cell(row=row, column=10, value=changes_text)
            ws_dept.cell(row=row, column=10).alignment = Alignment(wrapText=True)

        # Adjust column widths
        for col in range(1, 11):
            max_length = 0
            for row in range(3, ws_dept.max_row + 1):
                cell = ws_dept.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max_length + 2
            ws_dept.column_dimensions[chr(64 + col)].width = adjusted_width

    # Detailed comparison worksheet
    ws_detail = wb.create_sheet("All Changes")

    # Add headers
    headers = ["Employee ID", "Department", "Section", "Employee Name", "Previous Salary", "Current Salary", "Change",
               "Previous Net Pay", "Current Net Pay", "Change", "Changes"]

    # Style the header row
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")

    header_font = Font(bold=True)
    header_border = Border(bottom=Side(style='thin'))

    for col, header in enumerate(headers, 1):
        cell = ws_detail.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border

    # Add data
    for row, emp in enumerate(comparison_data, 2):
        ws_detail.cell(row=row, column=1, value=emp["id"])
        ws_detail.cell(row=row, column=2, value=emp.get("department", "Unknown"))
        ws_detail.cell(row=row, column=3, value=emp.get("section", "Unknown"))
        ws_detail.cell(row=row, column=4, value=emp.get("name", "Unknown"))

        # Salary data
        prev_salary = emp.get("previous_basic_salary", "N/A")
        curr_salary = emp.get("current_basic_salary", "N/A")
        ws_detail.cell(row=row, column=5, value=prev_salary)
        ws_detail.cell(row=row, column=6, value=curr_salary)

        # Calculate salary change
        if prev_salary != "N/A" and curr_salary != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_salary if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_salary if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
                    ws_detail.cell(row=row, column=7, value=change_text)
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=7, value="N/A")
        else:
            ws_detail.cell(row=row, column=7, value="N/A")

        # Net pay data
        prev_net = emp.get("previous_net_pay", "N/A")
        curr_net = emp.get("current_net_pay", "N/A")
        ws_detail.cell(row=row, column=8, value=prev_net)
        ws_detail.cell(row=row, column=9, value=curr_net)

        # Calculate net pay change
        if prev_net != "N/A" and curr_net != "N/A":
            try:
                prev_val = float(''.join(c for c in prev_net if c.isdigit() or c == '.'))
                curr_val = float(''.join(c for c in curr_net if c.isdigit() or c == '.'))
                if prev_val > 0:
                    diff = curr_val - prev_val
                    if diff > 0:
                        change_text = f"Increase: {diff:.2f}"
                    else:
                        change_text = f"Decrease: {abs(diff):.2f}"
                    ws_detail.cell(row=row, column=10, value=change_text)
            except (ValueError, TypeError):
                ws_detail.cell(row=row, column=10, value="N/A")
        else:
            ws_detail.cell(row=row, column=10, value="N/A")

        # Changes
        changes_text = "\n".join(emp["changes"])
        ws_detail.cell(row=row, column=11, value=changes_text)
        ws_detail.cell(row=row, column=11).alignment = Alignment(wrapText=True)

    # Format the worksheets
    for ws in [ws_summary, ws_detail]:
        # Adjust column widths
        max_col = 12 if ws == ws_detail else 10
        for col in range(1, max_col):
            max_length = 0
            for row in range(1, ws.max_row + 1):
                cell = ws.cell(row=row, column=col)
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max_length + 2
            ws.column_dimensions[chr(64 + col)].width = adjusted_width

    # Save Excel file
    wb.save(excel_path)
    print(f"Excel: {excel_path}")

    # Create CSV report
    csv_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.csv")

    # Prepare data for CSV (flatten the changes array)
    csv_data = []
    for emp in comparison_data:
        emp_copy = emp.copy()
        emp_copy["changes"] = "; ".join(emp["changes"])
        csv_data.append(emp_copy)

    df = pd.DataFrame(csv_data)
    df.to_csv(csv_path, index=False)
    print(f"CSV: {csv_path}")

    # Create JSON report
    json_path = os.path.join(output_dir, f"payroll_comparison_{timestamp}.json")
    with open(json_path, 'w') as f:
        json.dump(comparison_data, f, indent=2)
    print(f"JSON: {json_path}")

    # Return paths to the generated reports
    reports = {
        "excel": excel_path,
        "csv": csv_path,
        "json": json_path
    }

    print("Report generation complete.")
    return reports

def main(prev_file, curr_file, output_dir, id_field="employee_id", current_month=None, current_year=None, previous_month=None, previous_year=None, report_name=None, report_designation=None):
    """
    Main function to process payroll files and generate comparison reports.

    Args:
        prev_file: Path to the previous month's payroll PDF
        curr_file: Path to the current month's payroll PDF
        output_dir: Directory to save the reports
        id_field: Field to use as the unique identifier for comparison (default: "employee_id")
        current_month: Current month name (optional)
        current_year: Current year (optional)
        previous_month: Previous month name (optional)
        previous_year: Previous year (optional)
        report_name: Name of the person generating the report (optional)
        report_designation: Designation of the person generating the report (optional)
    """
    print("Starting payroll audit...")
    print(f"Previous file: {prev_file}")
    print(f"Current file: {curr_file}")
    print(f"Output directory: {output_dir}")
    print(f"Using '{id_field}' as the unique identifier")

    # Import modules now that we're ready to use them
    print("Loading required modules...")
    import_modules()

    # Extract payslips from PDFs
    print("\nExtracting text from previous payroll file...")
    prev_payslips = extract_payslips_from_pdf(prev_file)
    print(f"Found {len(prev_payslips)} potential payslips in previous file")

    print("\nExtracting text from current payroll file...")
    curr_payslips = extract_payslips_from_pdf(curr_file)
    print(f"Found {len(curr_payslips)} potential payslips in current file")

    # Parse payroll data
    print("\nParsing previous payroll data...")
    prev_data = parse_payroll_data(prev_payslips, prev_file)
    print(f"Successfully extracted data for {len(prev_data)} employees from previous file")

    print("\nParsing current payroll data...")
    curr_data = parse_payroll_data(curr_payslips, curr_file)
    print(f"Successfully extracted data for {len(curr_data)} employees from current file")

    # Compare payrolls using the specified identifier
    print("\nComparing payroll data...")
    comparison = compare_payrolls(prev_data, curr_data, id_field)
    print(f"Comparison complete. Found {len(comparison)} entries.")

    # Standardize all field names in the comparison data using dictionaries
    print("\nStandardizing field names using dictionaries...")
    standardized_comparison = standardize_comparison_data(comparison)
    print("Field names standardization complete.")

    # Generate reports with month/year information if provided
    print("\nGenerating reports...")
    if current_month and current_year and previous_month and previous_year:
        print(f"Using month/year info: Current: {current_month} {current_year}, Previous: {previous_month} {previous_year}")
        if report_name and report_designation:
            print(f"Report signature: {report_name}, {report_designation}")
            reports = generate_improved_reports(
                standardized_comparison,
                output_dir,
                current_month=current_month,
                current_year=current_year,
                previous_month=previous_month,
                previous_year=previous_year,
                report_name=report_name,
                report_designation=report_designation
            )
        else:
            reports = generate_improved_reports(
                standardized_comparison,
                output_dir,
                current_month=current_month,
                current_year=current_year,
                previous_month=previous_month,
                previous_year=previous_year
            )
    else:
        if report_name and report_designation:
            print(f"Report signature: {report_name}, {report_designation}")
            reports = generate_improved_reports(
                standardized_comparison,
                output_dir,
                report_name=report_name,
                report_designation=report_designation
            )
        else:
            reports = generate_improved_reports(standardized_comparison, output_dir)

    print(f"Reports generated successfully:")
    print(f"Excel report: {reports['excel']}")
    print(f"CSV report: {reports['csv']}")
    print(f"JSON report: {reports['json']}")

    print("\nPayroll audit completed successfully.")
    return reports

def sort_pdf_by_identifier(pdf_path, output_path, id_field="employee_id"):
    """
    Sort a PDF file by the specified identifier and save the sorted PDF.

    Args:
        pdf_path: Path to the PDF file to sort
        output_path: Path to save the sorted PDF
        id_field: Field to use for sorting (default: "employee_id")
    """
    print(f"Sorting PDF by {id_field}...")
    print(f"Input file: {pdf_path}")

    # Create a dedicated reports directory structure in the app folder
    app_dir = os.path.dirname(os.path.abspath(__file__))
    reports_dir = os.path.join(app_dir, "reports")
    sorted_pdfs_dir = os.path.join(reports_dir, "sorted_pdfs")

    # Create the directory if it doesn't exist
    if not os.path.exists(sorted_pdfs_dir):
        os.makedirs(sorted_pdfs_dir)

    # Get the filename from the output_path
    filename = os.path.basename(output_path)

    # Create the new output path in the sorted_pdfs directory
    new_output_path = os.path.join(sorted_pdfs_dir, filename)

    # Use the new output path
    output_path = new_output_path
    print(f"Output file: {output_path}")

    # Extract payslips from PDF
    payslips = extract_payslips_from_pdf(pdf_path)

    # Parse payroll data with advanced layout detection
    employees = parse_payroll_data(payslips, pdf_path)

    # Sort employees by the specified identifier
    if id_field in ["employee_id", "ssf_no", "ghana_card_id", "name", "department", "section", "job_title"]:
        # Special handling for employee_id to ensure numeric sorting when appropriate
        if id_field == "employee_id":
            # Define a custom key function for employee_id sorting
            def employee_id_sort_key(employee):
                # Get the employee_id, defaulting to empty string if not present
                emp_id = employee.get(id_field, "")

                # If the ID is None, return a tuple with True to sort it first
                if emp_id is None:
                    return (True, "")

                # Extract any numeric part from the ID for numeric sorting
                # Example: "COP1021" -> extract "1021" for numeric comparison
                numeric_part = ""
                for part in re.findall(r'\d+', emp_id):
                    if len(part) > len(numeric_part):
                        numeric_part = part

                # If we found a numeric part, use it for sorting
                if numeric_part:
                    # Return a tuple with: (is_none, prefix, numeric_part_as_int, original_string)
                    prefix = emp_id[:emp_id.find(numeric_part)]
                    return (False, prefix, int(numeric_part), emp_id)
                else:
                    # If no numeric part, just use the lowercase string
                    return (False, emp_id.lower(), 0, emp_id)

            # Sort using the custom key function
            sorted_employees = sorted(employees, key=employee_id_sort_key)
            print(f"Sorting by Employee No. with special numeric handling")
        else:
            # For other fields, use standard case-insensitive sorting
            sorted_employees = sorted(
                employees,
                key=lambda x: (x.get(id_field) is None, x.get(id_field, "").lower())
            )
            print(f"Sorting by {id_field} alphabetically")
    else:
        print(f"Invalid identifier: {id_field}. Using employee_id instead.")
        # Fall back to employee_id with numeric sorting
        sorted_employees = sorted(
            employees,
            key=lambda x: (
                x.get("employee_id") is None,
                re.sub(r'\d+', lambda m: m.group().zfill(10), x.get("employee_id", "").lower())
            )
        )

    # Get the page numbers in sorted order
    sorted_pages = [emp.get("page_num", 0) for emp in sorted_employees if emp.get("page_num", 0) > 0]

    # If we couldn't extract page numbers for all employees, we can't sort the PDF
    if len(sorted_pages) < len(employees) / 2:
        print("Warning: Could not determine page numbers for many employees.")
        print("PDF sorting may be incomplete or incorrect.")

    try:
        # Use PyPDF2 to create a new PDF with sorted pages
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            writer = PyPDF2.PdfWriter()

            # Add pages in sorted order
            for page_num in sorted_pages:
                if 1 <= page_num <= len(reader.pages):
                    writer.add_page(reader.pages[page_num - 1])

            # Add any pages that weren't in the sorted list (in case we missed some)
            all_pages = set(range(1, len(reader.pages) + 1))
            missing_pages = all_pages - set(sorted_pages)
            for page_num in missing_pages:
                writer.add_page(reader.pages[page_num - 1])

            # Save the sorted PDF
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)

        print(f"Sorted PDF saved to: {output_path}")
        return output_path

    except Exception as e:
        print(f"Error sorting PDF: {e}")
        return None

def build_data_table(pdf_path, output_path, selected_fields):
    """
    Build a comprehensive data table from a PDF file with selected fields as columns.
    Similar to CRITICAL KPI sheet but for a single period.

    Args:
        pdf_path: Path to the PDF file
        output_path: Path to save the Excel file
        selected_fields: List of fields to include in the table
    """
    print(f"Building enhanced data table from {pdf_path}...")

    # Import required modules for Excel formatting
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
    from openpyxl.utils import get_column_letter

    # Create a dedicated reports directory structure in the app folder
    app_dir = os.path.dirname(os.path.abspath(__file__))
    reports_dir = os.path.join(app_dir, "reports")
    data_tables_dir = os.path.join(reports_dir, "data_tables")

    # Create the directory if it doesn't exist
    if not os.path.exists(data_tables_dir):
        os.makedirs(data_tables_dir)

    # Get the filename from the output_path
    filename = os.path.basename(output_path)

    # Create the new output path in the data_tables directory
    new_output_path = os.path.join(data_tables_dir, filename)

    # Use the new output path
    output_path = new_output_path
    print(f"Output file: {output_path}")
    print(f"Selected fields: {selected_fields}")

    # Extract payslips from PDF
    payslips = extract_payslips_from_pdf(pdf_path)

    # Parse payroll data
    employees = parse_payroll_data(payslips)

    # Create a new workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "DATA TABLE"

    # Define styles
    header_font = Font(bold=True, size=12)
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    header_border = Border(bottom=Side(style='thin'))

    # Add title and timestamp
    ws.cell(row=1, column=1, value=f"Payroll Data Table - {datetime.now().strftime('%Y-%m-%d')}")
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=2, column=1, value=f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Extract period information if available
    period_info = None
    for emp in employees:
        if emp.get("month") and emp.get("year"):
            period_info = f"{emp.get('month')} {emp.get('year')}"
            break

    if period_info:
        ws.cell(row=3, column=1, value=f"Period: {period_info}")

    # Collect fields for the data table
    # Note: We don't need a separate all_fields dictionary since we build specific category sets below

    # Always include these basic fields at the beginning
    basic_fields = ["employee_id", "name", "department"]

    # Process selected fields to determine what to include
    # Check for section-specific fields in the new format (e.g., "EARNINGS:Basic Salary")
    section_specific_fields = {}
    general_fields = []

    # Import enhanced dictionary for standardization
    try:
        import enhanced_payroll_dictionaries
        dictionary = enhanced_payroll_dictionaries.load_dictionary()
        print("Loaded dictionary for Data Builder")
    except Exception as e:
        print(f"Error loading dictionary: {e}")
        dictionary = {}

    # Process the selected fields
    for field in selected_fields:
        if ":" in field:
            # This is a section-specific field
            section, item = field.split(":", 1)
            if section not in section_specific_fields:
                section_specific_fields[section] = []
            section_specific_fields[section].append(item)
        else:
            # This is a general field
            general_fields.append(field)

    # Check for old-style category inclusion
    include_earnings = "earnings" in general_fields
    include_deductions = "deductions" in general_fields
    include_loan_details = "loan_details" in general_fields
    include_bank_details = "bank_details" in general_fields
    include_employer_contributions = "employer_contributions" in general_fields

    # Also check for section-specific fields
    if "EARNINGS" in section_specific_fields:
        include_earnings = True
    if "DEDUCTIONS" in section_specific_fields:
        include_deductions = True
    if "LOANS" in section_specific_fields:
        include_loan_details = True
    if "EMPLOYEE BANK DETAILS" in section_specific_fields:
        include_bank_details = True
    if "EMPLOYERS CONTRIBUTION" in section_specific_fields:
        include_employer_contributions = True

    # Collect all unique items for each category
    all_earnings = set()
    all_deductions = set()
    all_loan_fields = set()
    all_bank_fields = set()
    all_contribution_fields = set()

    # Scan all employees to find every possible field
    for emp in employees:
        # Earnings
        if include_earnings and "earnings" in emp:
            all_earnings.update(emp["earnings"].keys())

        # Deductions
        if include_deductions and "deductions" in emp:
            all_deductions.update(emp["deductions"].keys())

        # Loan details
        if include_loan_details and "loan_details" in emp:
            for loan_type, loan_data in emp["loan_details"].items():
                all_loan_fields.add(f"LOAN: {loan_type}")
                if "balance_bf" in loan_data:
                    all_loan_fields.add(f"LOAN: {loan_type} - BALANCE B/F")
                if "current_deduction" in loan_data:
                    all_loan_fields.add(f"LOAN: {loan_type} - CURRENT DEDUCTION")
                if "outstanding_balance" in loan_data:
                    all_loan_fields.add(f"LOAN: {loan_type} - OUTST. BALANCE")

        # Bank details
        if include_bank_details and "bank_details" in emp:
            for key in emp["bank_details"].keys():
                all_bank_fields.add(key)

        # Employer contributions
        if include_employer_contributions and "employer_contributions" in emp:
            all_contribution_fields.update(emp["employer_contributions"].keys())

    # Sort all field sets
    all_earnings = sorted(all_earnings)
    all_deductions = sorted(all_deductions)
    all_loan_fields = sorted(all_loan_fields)
    all_bank_fields = sorted(all_bank_fields)
    all_contribution_fields = sorted(all_contribution_fields)

    # Create headers list
    headers = []

    # Add basic fields first
    for field in basic_fields:
        if field in selected_fields or field == "employee_id":  # Always include employee_id
            headers.append(field.upper().replace("_", " "))

    # Add other selected fields that are not special categories
    for field in selected_fields:
        if field not in basic_fields and field not in ["earnings", "deductions", "loan_details", "bank_details", "employer_contributions"]:
            headers.append(field.upper().replace("_", " "))

    # Add earnings fields
    if include_earnings:
        # If we have specific earnings fields selected, only include those
        if "EARNINGS" in section_specific_fields and section_specific_fields["EARNINGS"]:
            for earning in section_specific_fields["EARNINGS"]:
                headers.append(f"EARNING: {earning}")
        else:
            # Otherwise include all earnings
            for earning in all_earnings:
                headers.append(f"EARNING: {earning}")

    # Add deductions fields
    if include_deductions:
        # If we have specific deductions fields selected, only include those
        if "DEDUCTIONS" in section_specific_fields and section_specific_fields["DEDUCTIONS"]:
            for deduction in section_specific_fields["DEDUCTIONS"]:
                headers.append(f"DEDUCTION: {deduction}")
        else:
            # Otherwise include all deductions
            for deduction in all_deductions:
                headers.append(f"DEDUCTION: {deduction}")

    # Add loan fields
    if include_loan_details:
        # If we have specific loan fields selected, only include those
        if "LOANS" in section_specific_fields and section_specific_fields["LOANS"]:
            for loan_type in section_specific_fields["LOANS"]:
                headers.append(f"LOAN: {loan_type}")
        else:
            # Otherwise include all loan fields
            for loan_field in all_loan_fields:
                headers.append(loan_field)

    # Add bank details fields
    if include_bank_details:
        # If we have specific bank fields selected, only include those
        if "EMPLOYEE BANK DETAILS" in section_specific_fields and section_specific_fields["EMPLOYEE BANK DETAILS"]:
            for bank_field in section_specific_fields["EMPLOYEE BANK DETAILS"]:
                headers.append(f"BANK: {bank_field}")
        else:
            # Otherwise include all bank fields
            for bank_field in all_bank_fields:
                headers.append(f"BANK: {bank_field}")

    # Add employer contribution fields
    if include_employer_contributions:
        # If we have specific contribution fields selected, only include those
        if "EMPLOYERS CONTRIBUTION" in section_specific_fields and section_specific_fields["EMPLOYERS CONTRIBUTION"]:
            for contrib_field in section_specific_fields["EMPLOYERS CONTRIBUTION"]:
                headers.append(f"EMPLOYER CONTRIBUTION: {contrib_field}")
        else:
            # Otherwise include all contribution fields
            for contrib_field in all_contribution_fields:
                headers.append(f"EMPLOYER CONTRIBUTION: {contrib_field}")

    # Add headers to worksheet
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = header_border
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

    # Add data rows
    row_idx = 5
    for emp in employees:
        col_idx = 1

        # Add basic fields
        for field in basic_fields:
            if field in selected_fields or field == "employee_id":  # Always include employee_id
                ws.cell(row=row_idx, column=col_idx, value=emp.get(field, ""))
                col_idx += 1

        # Add other selected fields that are not special categories
        for field in selected_fields:
            if field not in basic_fields and field not in ["earnings", "deductions", "loan_details", "bank_details", "employer_contributions"]:
                ws.cell(row=row_idx, column=col_idx, value=emp.get(field, ""))
                col_idx += 1

        # Add earnings fields
        if include_earnings:
            emp_earnings = emp.get("earnings", {})
            # If we have specific earnings fields selected, only include those
            if "EARNINGS" in section_specific_fields and section_specific_fields["EARNINGS"]:
                for earning in section_specific_fields["EARNINGS"]:
                    ws.cell(row=row_idx, column=col_idx, value=emp_earnings.get(earning, ""))
                    col_idx += 1
            else:
                # Otherwise include all earnings
                for earning in all_earnings:
                    ws.cell(row=row_idx, column=col_idx, value=emp_earnings.get(earning, ""))
                    col_idx += 1

        # Add deductions fields
        if include_deductions:
            emp_deductions = emp.get("deductions", {})
            # If we have specific deductions fields selected, only include those
            if "DEDUCTIONS" in section_specific_fields and section_specific_fields["DEDUCTIONS"]:
                for deduction in section_specific_fields["DEDUCTIONS"]:
                    ws.cell(row=row_idx, column=col_idx, value=emp_deductions.get(deduction, ""))
                    col_idx += 1
            else:
                # Otherwise include all deductions
                for deduction in all_deductions:
                    ws.cell(row=row_idx, column=col_idx, value=emp_deductions.get(deduction, ""))
                    col_idx += 1

        # Add loan fields
        if include_loan_details:
            emp_loans = emp.get("loan_details", {})
            # If we have specific loan fields selected, only include those
            if "LOANS" in section_specific_fields and section_specific_fields["LOANS"]:
                for loan_type in section_specific_fields["LOANS"]:
                    # Get loan data
                    loan_data = emp_loans.get(loan_type, {})
                    # Use outstanding balance as the default value
                    ws.cell(row=row_idx, column=col_idx, value=loan_data.get("outstanding_balance", ""))
                    col_idx += 1
            else:
                # Otherwise include all loan fields
                for loan_field in all_loan_fields:
                    # Extract loan type and field from the combined field name
                    if " - " in loan_field:
                        loan_type, field_name = loan_field.replace("LOAN: ", "").split(" - ", 1)
                    else:
                        loan_type = loan_field.replace("LOAN: ", "")
                        field_name = "outstanding_balance"  # Default field

                    # Map field names to the actual keys in the loan dictionaries
                    field_map = {
                        "BALANCE B/F": "balance_bf",
                        "CURRENT DEDUCTION": "current_deduction",
                        "OUTST. BALANCE": "outstanding_balance"
                    }

                    # Get the actual field key
                    field_key = field_map.get(field_name, "outstanding_balance")

                    # Get loan data
                    loan_data = emp_loans.get(loan_type, {})
                    ws.cell(row=row_idx, column=col_idx, value=loan_data.get(field_key, ""))
                    col_idx += 1

        # Add bank details fields
        if include_bank_details:
            emp_bank = emp.get("bank_details", {})
            # If we have specific bank fields selected, only include those
            if "EMPLOYEE BANK DETAILS" in section_specific_fields and section_specific_fields["EMPLOYEE BANK DETAILS"]:
                for bank_field in section_specific_fields["EMPLOYEE BANK DETAILS"]:
                    ws.cell(row=row_idx, column=col_idx, value=emp_bank.get(bank_field, ""))
                    col_idx += 1
            else:
                # Otherwise include all bank fields
                for bank_field in all_bank_fields:
                    ws.cell(row=row_idx, column=col_idx, value=emp_bank.get(bank_field, ""))
                    col_idx += 1

        # Add employer contribution fields
        if include_employer_contributions:
            emp_contrib = emp.get("employer_contributions", {})
            # If we have specific contribution fields selected, only include those
            if "EMPLOYERS CONTRIBUTION" in section_specific_fields and section_specific_fields["EMPLOYERS CONTRIBUTION"]:
                for contrib_field in section_specific_fields["EMPLOYERS CONTRIBUTION"]:
                    ws.cell(row=row_idx, column=col_idx, value=emp_contrib.get(contrib_field, ""))
                    col_idx += 1
            else:
                # Otherwise include all contribution fields
                for contrib_field in all_contribution_fields:
                    ws.cell(row=row_idx, column=col_idx, value=emp_contrib.get(contrib_field, ""))
                    col_idx += 1

        row_idx += 1

    # Format the worksheet
    # Adjust column widths
    for col in range(1, len(headers) + 1):
        max_length = 0
        for row in range(4, ws.max_row + 1):  # Start from header row
            cell = ws.cell(row=row, column=col)
            if cell.value:
                max_length = max(max_length, len(str(cell.value).split('\n')[0]))  # Take first line for width calculation
        adjusted_width = min(max_length + 2, 50)  # Cap width at 50 characters
        ws.column_dimensions[get_column_letter(col)].width = adjusted_width

    # Enable filtering
    if ws.max_row > 4:  # Only add filter if there's data
        ws.auto_filter.ref = f"A4:{get_column_letter(len(headers))}{ws.max_row}"

    # Freeze panes for better navigation
    ws.freeze_panes = "A5"  # Freeze header row

    # Save Excel file
    try:
        wb.save(output_path)
        print(f"Enhanced data table saved to: {output_path}")
    except Exception as e:
        print(f"Error saving Excel file: {e}")
        return None

    return output_path

if __name__ == "__main__":
    # Already imported sys and argparse at the top
    print("Starting payroll parser command line tool...")

    # Create the main parser
    parser = argparse.ArgumentParser(description='Payroll processing tools')
    subparsers = parser.add_subparsers(dest='command', help='Command to run')

    # Parser for the compare command
    compare_parser = subparsers.add_parser('compare', help='Compare payroll PDFs and generate reports')
    compare_parser.add_argument('prev_file', help='Path to the previous month\'s payroll PDF')
    compare_parser.add_argument('curr_file', help='Path to the current month\'s payroll PDF')
    compare_parser.add_argument('output_dir', help='Directory to save the reports')
    compare_parser.add_argument('--id-field', default='employee_id',
                        choices=['employee_id', 'ssf_no', 'ghana_card_id', 'name', 'department', 'section', 'job_title'],
                        help='Field to use as the unique identifier (default: employee_id)')
    compare_parser.add_argument('--current-month', help='Current month name')
    compare_parser.add_argument('--current-year', help='Current year')
    compare_parser.add_argument('--previous-month', help='Previous month name')
    compare_parser.add_argument('--previous-year', help='Previous year')
    compare_parser.add_argument('--report-name', help='Name of the person generating the report')
    compare_parser.add_argument('--report-designation', help='Designation of the person generating the report')

    # Parser for the sort command
    sort_parser = subparsers.add_parser('sort', help='Sort a PDF file by a specified identifier')
    sort_parser.add_argument('pdf_file', help='Path to the PDF file to sort')
    sort_parser.add_argument('output_file', help='Path to save the sorted PDF')
    sort_parser.add_argument('--id-field', default='employee_id',
                        choices=['employee_id', 'ssf_no', 'ghana_card_id', 'name', 'department', 'section', 'job_title'],
                        help='Field to use for sorting (default: employee_id)')

    # Parser for the build command
    build_parser = subparsers.add_parser('build', help='Build a comprehensive data table from a PDF file')
    build_parser.add_argument('pdf_file', help='Path to the PDF file')
    build_parser.add_argument('output_file', help='Path to save the Excel file')
    build_parser.add_argument('--fields', nargs='+', required=True,
                        help='''Fields to include in the table (space-separated). Available fields:
                        - Basic fields: employee_id, name, department, section, ssf_no, ghana_card_id, position, basic_salary, gross_salary, net_pay, taxable_income
                        - Special categories: earnings, deductions, loan_details, bank_details, employer_contributions
                        When you select a special category like "earnings", all items under that category will be included as separate columns.''')

    # Parse arguments
    if len(sys.argv) > 1:
        args = parser.parse_args()

        if args.command == 'compare':
            main(
                args.prev_file,
                args.curr_file,
                args.output_dir,
                args.id_field,
                current_month=args.current_month,
                current_year=args.current_year,
                previous_month=args.previous_month,
                previous_year=args.previous_year,
                report_name=args.report_name,
                report_designation=args.report_designation
            )
        elif args.command == 'sort':
            sort_pdf_by_identifier(args.pdf_file, args.output_file, args.id_field)
        elif args.command == 'build':
            build_data_table(args.pdf_file, args.output_file, args.fields)
        else:
            parser.print_help()
    else:
        parser.print_help()
        sys.exit(1)
