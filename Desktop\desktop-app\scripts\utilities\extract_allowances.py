import os
import sys
import re
import pandas as pd

print("<PERSON><PERSON><PERSON> started. Extracting data from ALL ALLOWANCES and AWARDS & GRANTS PDFs...")

class AllowancesExtractor:
    """
    Class for extracting data from ALL ALLOWANCES and AWARDS & GRANTS PDFs.
    """

    def __init__(self):
        """Initialize the AllowancesExtractor class."""
        self.allowances_data = {}
        self.grants_data = {}
        self.sections_found = set()

    def extract_allowances_data(self, allowances_file):
        """
        Extract data from ALL ALLOWANCES PDF.

        Args:
            allowances_file (str): Path to the ALL ALLOWANCES PDF file
        """
        print(f"Extracting data from allowances file: {allowances_file}")

        # Initialize allowances data dictionary
        self.allowances_data = {}

        # Check if the file exists
        if not allowances_file or not os.path.exists(allowances_file):
            print(f"Allowances file not found: {allowances_file}")
            return

        try:
            # Import required modules for PDF extraction
            import PyPDF2
            
            # Extract text from the PDF
            with open(allowances_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                print(f"ALL ALLOWANCES PDF has {num_pages} pages")
                
                # First pass: identify all sections in the document
                print("First pass: Identifying all sections in the document...")
                for page_num in range(num_pages):
                    if page_num % 5 == 0:
                        print(f"Scanning page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Split the text into lines
                    lines = text.split('\n')
                    
                    # Process each line to identify sections
                    for line in lines:
                        # Look for section headers (focus areas and subtitles)
                        section_match = re.search(r'(?:SA Period Code: MAR \d{4} )?([\w\s\-\&\(\)]+(?:ALLOWANCE|SUBSIDY|BENEFIT|BONUS|INCENTIVE|PAYMENT|REIMBURSEMENT|STIPEND|GRANT|COMPENSATION|CLAIMS))', line, re.IGNORECASE)
                        if section_match:
                            # Extract the section name and clean it up
                            section_name = section_match.group(1).strip()
                            # Remove any numbers from the section name
                            section_name = re.sub(r'^\d+\s+', '', section_name)
                            section_name = re.sub(r'\s+\d+$', '', section_name)
                            
                            # Add to sections found
                            self.sections_found.add(section_name)
                            print(f"Identified section: {section_name}")
                
                print(f"Found {len(self.sections_found)} sections: {', '.join(self.sections_found)}")
                
                # Second pass: extract employee data for each section
                print("Second pass: Extracting employee data for each section...")
                
                # Initialize variables for tracking the current section
                current_section = None
                
                # Process each page
                for page_num in range(num_pages):
                    if page_num % 5 == 0:
                        print(f"Processing page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Split the text into lines
                    lines = text.split('\n')
                    
                    # Process each line
                    for line in lines:
                        # Check if this line indicates a new section
                        for section in self.sections_found:
                            if section in line:
                                current_section = section
                                print(f"Found section in text: {current_section}")
                                break
                        
                        # Look for employee data pattern: Employee Code, Employee Name, Amount
                        # This pattern may vary depending on the actual format of the PDF
                        employee_match = re.search(r'(COP\d{4})\s+([A-Z\s\.\-]+)\s+(\d{1,3}(?:,\d{3})*(?:\.\d+)?)', line)
                        if employee_match and current_section:
                            employee_no = employee_match.group(1)
                            employee_name = employee_match.group(2).strip()
                            amount_str = employee_match.group(3).replace(',', '')
                            
                            try:
                                amount = float(amount_str)
                            except ValueError:
                                print(f"Could not convert amount to float: {amount_str}")
                                amount = 0.0
                            
                            # Store the data
                            if employee_no not in self.allowances_data:
                                self.allowances_data[employee_no] = {
                                    'EMPLOYEE NO.': employee_no,
                                    'EMPLOYEE NAME': employee_name,
                                    'ALL ALLOWANCES': 0.0,
                                    'ALLOWANCE TYPES': []
                                }
                            
                            # Add the amount to the total
                            self.allowances_data[employee_no]['ALL ALLOWANCES'] += amount
                            
                            # Add the allowance type to the list
                            if current_section not in self.allowances_data[employee_no]['ALLOWANCE TYPES']:
                                self.allowances_data[employee_no]['ALLOWANCE TYPES'].append(current_section)
                            
                            print(f"Found allowance for {employee_no}: {current_section} = {amount}")
            
            print(f"Extracted allowances data for {len(self.allowances_data)} employees")
            
            # Print sample of extracted data
            if self.allowances_data:
                print("\nSample of extracted allowances data:")
                for i, (employee_no, data) in enumerate(list(self.allowances_data.items())[:5]):
                    print(f"  Employee {employee_no}: {data}")
                
                if len(self.allowances_data) > 5:
                    print(f"  ... and {len(self.allowances_data) - 5} more entries")
            else:
                print("No allowances data was extracted!")
                
        except Exception as e:
            print(f"Error extracting data from allowances file: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def extract_grants_data(self, grants_file):
        """
        Extract data from AWARDS & GRANTS PDF.

        Args:
            grants_file (str): Path to the AWARDS & GRANTS PDF file
        """
        print(f"Extracting data from grants file: {grants_file}")

        # Initialize grants data dictionary
        self.grants_data = {}

        # Check if the file exists
        if not grants_file or not os.path.exists(grants_file):
            print(f"Grants file not found: {grants_file}")
            return

        try:
            # Import required modules for PDF extraction
            import PyPDF2
            
            # Extract text from the PDF
            with open(grants_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                print(f"AWARDS & GRANTS PDF has {num_pages} pages")
                
                # Process each page
                for page_num in range(num_pages):
                    print(f"Processing page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Print a sample of the text for debugging
                    sample_text = text[:500] + "..." if len(text) > 500 else text
                    print(f"Sample text from page {page_num + 1}: {sample_text}")
                    
                    # Look for employee data pattern: Employee Code, Employee Name, Amount
                    # This pattern may vary depending on the actual format of the PDF
                    for line in text.split('\n'):
                        employee_match = re.search(r'(COP\d{4})\s+([A-Z\s\.\-]+)\s+(\d{1,3}(?:,\d{3})*(?:\.\d+)?)', line)
                        if employee_match:
                            employee_no = employee_match.group(1)
                            employee_name = employee_match.group(2).strip()
                            amount_str = employee_match.group(3).replace(',', '')
                            
                            try:
                                amount = float(amount_str)
                            except ValueError:
                                print(f"Could not convert amount to float: {amount_str}")
                                amount = 0.0
                            
                            # Store the data
                            if employee_no not in self.grants_data:
                                self.grants_data[employee_no] = {
                                    'EMPLOYEE NO.': employee_no,
                                    'EMPLOYEE NAME': employee_name,
                                    'AWARDS & GRANTS': amount
                                }
                            else:
                                # Add the amount to the total
                                self.grants_data[employee_no]['AWARDS & GRANTS'] += amount
                            
                            print(f"Found grant for {employee_no}: {amount}")
            
            print(f"Extracted grants data for {len(self.grants_data)} employees")
            
            # Print sample of extracted data
            if self.grants_data:
                print("\nSample of extracted grants data:")
                for i, (employee_no, data) in enumerate(list(self.grants_data.items())[:5]):
                    print(f"  Employee {employee_no}: {data}")
                
                if len(self.grants_data) > 5:
                    print(f"  ... and {len(self.grants_data) - 5} more entries")
            else:
                print("No grants data was extracted!")
                
        except Exception as e:
            print(f"Error extracting data from grants file: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def merge_with_payslip_data(self, payslip_data_file, output_file):
        """
        Merge the extracted allowances and grants data with the payslip data.

        Args:
            payslip_data_file (str): Path to the payslip data Excel file
            output_file (str): Path to the output Excel file
        """
        try:
            # Load the payslip data
            payslip_df = pd.read_excel(payslip_data_file)
            print(f"Loaded payslip data with {len(payslip_df)} records")
            
            # Convert allowances data to DataFrame
            allowances_list = []
            for employee_no, data in self.allowances_data.items():
                allowances_list.append(data)
            
            allowances_df = pd.DataFrame(allowances_list) if allowances_list else pd.DataFrame(columns=['EMPLOYEE NO.', 'EMPLOYEE NAME', 'ALL ALLOWANCES', 'ALLOWANCE TYPES'])
            print(f"Created allowances DataFrame with {len(allowances_df)} records")
            
            # Convert grants data to DataFrame
            grants_list = []
            for employee_no, data in self.grants_data.items():
                grants_list.append(data)
            
            grants_df = pd.DataFrame(grants_list) if grants_list else pd.DataFrame(columns=['EMPLOYEE NO.', 'EMPLOYEE NAME', 'AWARDS & GRANTS'])
            print(f"Created grants DataFrame with {len(grants_df)} records")
            
            # Merge the DataFrames
            # First merge payslip and allowances
            merged_df = pd.merge(payslip_df, allowances_df[['EMPLOYEE NO.', 'ALL ALLOWANCES', 'ALLOWANCE TYPES']], 
                                on='EMPLOYEE NO.', how='left')
            
            # Then merge with grants
            merged_df = pd.merge(merged_df, grants_df[['EMPLOYEE NO.', 'AWARDS & GRANTS']], 
                                on='EMPLOYEE NO.', how='left')
            
            # Fill NaN values with 0 for numeric columns and empty list for ALLOWANCE TYPES
            merged_df['ALL ALLOWANCES'] = merged_df['ALL ALLOWANCES'].fillna(0.0)
            merged_df['AWARDS & GRANTS'] = merged_df['AWARDS & GRANTS'].fillna(0.0)
            merged_df['ALLOWANCE TYPES'] = merged_df['ALLOWANCE TYPES'].fillna('').apply(lambda x: x if isinstance(x, list) else [])
            
            # Calculate TOTAL column
            merged_df['TOTAL'] = merged_df['NET PAY'] + merged_df['ALL ALLOWANCES'] + merged_df['AWARDS & GRANTS']
            
            # Add REMARKS column
            merged_df['REMARKS'] = 'PRE-AUDITED'
            
            # Save to Excel
            merged_df.to_excel(output_file, index=False)
            
            print(f"Successfully saved merged data to Excel file: {output_file}")
            print(f"Total records saved: {len(merged_df)}")
            
        except Exception as e:
            print(f"Error merging data: {str(e)}")
            import traceback
            print(traceback.format_exc())

def main():
    if len(sys.argv) < 3:
        print("Usage: python extract_allowances.py <allowances_pdf_file> <grants_pdf_file> [payslip_data_file] [output_excel_file]")
        sys.exit(1)
    
    allowances_file = sys.argv[1]
    grants_file = sys.argv[2]
    
    # Default payslip data file is payslip_extract3.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    payslip_data_file = os.path.join(desktop_path, "payslip_extract3.xlsx")
    
    # Default output file is bank_adviser_data.xlsx on the desktop
    output_file = os.path.join(desktop_path, "bank_adviser_data.xlsx")
    
    # If payslip data file is provided, use it
    if len(sys.argv) > 3:
        payslip_data_file = sys.argv[3]
    
    # If output file is provided, use it
    if len(sys.argv) > 4:
        output_file = sys.argv[4]
    
    print(f"Allowances file: {allowances_file}")
    print(f"Grants file: {grants_file}")
    print(f"Payslip data file: {payslip_data_file}")
    print(f"Output file: {output_file}")
    
    extractor = AllowancesExtractor()
    extractor.extract_allowances_data(allowances_file)
    extractor.extract_grants_data(grants_file)
    
    # Merge with payslip data if available
    if os.path.exists(payslip_data_file):
        extractor.merge_with_payslip_data(payslip_data_file, output_file)
    else:
        print(f"Payslip data file not found: {payslip_data_file}")

if __name__ == "__main__":
    main()
