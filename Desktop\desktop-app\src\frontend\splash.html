<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>TEMPLAR SYSTEMS Payroll Auditor</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f0f0f0;
      font-family: Arial, sans-serif;
      overflow: hidden;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .app-header {
      background-color: #1a237e;
      color: white;
      padding: 20px;
      text-align: center;
    }

    .title-container h1 {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .title-container h2 {
      font-size: 20px;
      font-weight: normal;
      margin-top: 0;
    }

    .app-content {
      flex: 1;
      padding: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .splash-content {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 30px;
      text-align: center;
      width: 80%;
      max-width: 600px;
    }

    .splash-icon {
      width: 150px;
      height: 150px;
      margin-bottom: 30px;
      animation: pulse 2s infinite;
    }

    h3 {
      font-size: 24px;
      margin-bottom: 30px;
      color: #333;
    }

    .loading-bar-container {
      margin: 30px 0;
    }

    .loading-bar {
      width: 100%;
      height: 20px;
      background-color: #e0e0e0;
      border-radius: 10px;
      overflow: hidden;
      border: 1px solid #ccc;
    }

    .loading-progress {
      height: 100%;
      width: 0%;
      background-color: #4caf50;
      border-radius: 10px;
      transition: width 0.5s ease-in-out;
    }

    .loading-text {
      font-size: 18px;
      margin-top: 15px;
      color: #333;
      font-weight: bold;
    }

    .app-footer {
      background-color: #1a237e;
      color: white;
      display: flex;
      justify-content: space-between;
      padding: 15px 20px;
    }

    .footer-right {
      text-align: right;
    }

    .footer-right p, .footer-left p {
      font-size: 14px;
      margin: 2px 0;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.05);
        opacity: 0.8;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }
  </style>
</head>
<body>
  <header class="app-header">
    <div class="title-container">
      <h1>TEMPLAR SYSTEMS</h1>
      <h2>Payroll Auditor</h2>
    </div>
  </header>

  <main class="app-content">
    <div class="splash-content">
      <img src="assets/icon.png" alt="Electron Icon" class="splash-icon">
      <h3>Starting Application</h3>

      <div class="loading-bar-container">
        <div class="loading-bar">
          <div class="loading-progress" id="loading-progress"></div>
        </div>
        <p class="loading-text" id="loading-text">Checking dependencies...</p>
      </div>
    </div>
  </main>

  <footer class="app-footer">
    <div class="footer-left">
      <p>TEMPLAR SYSTEMS Payroll Auditor</p>
    </div>
    <div class="footer-right">
      <p>CREATED BY: SAMUEL ASIEDU</p>
      <p>© 2025 All Rights Reserved</p>
    </div>
  </footer>

  <script>
    // Set initial progress immediately to avoid blank state
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Splash screen DOM loaded');

      // Get elements
      const loadingProgress = document.getElementById('loading-progress');
      const loadingText = document.getElementById('loading-text');

      // Set initial progress immediately
      if (loadingProgress) {
        loadingProgress.style.width = '30%';
      }

      if (loadingText) {
        loadingText.textContent = 'Checking dependencies...';
      }

      // Listen for messages from the main process
      if (window.api && window.api.onSplashMessage) {
        window.api.onSplashMessage((message) => {
          console.log('Received splash message:', message);

          if (message.progress && loadingProgress) {
            loadingProgress.style.width = message.progress + '%';
          }

          if (message.text && loadingText) {
            loadingText.textContent = message.text;
          }
        });
      } else {
        console.error('API not available for splash messages');

        // Fallback animation if API is not available
        setTimeout(() => {
          if (loadingProgress) loadingProgress.style.width = '60%';
          if (loadingText) loadingText.textContent = 'Initializing application...';
        }, 1000);

        setTimeout(() => {
          if (loadingProgress) loadingProgress.style.width = '100%';
          if (loadingText) loadingText.textContent = 'Ready!';
        }, 2000);
      }
    });
  </script>
</body>
</html>
