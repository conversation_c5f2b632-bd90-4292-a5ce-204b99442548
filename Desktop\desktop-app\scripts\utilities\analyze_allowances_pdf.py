import os
import sys
import re
import logging

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AllowanceAnalyzer')

print("<PERSON><PERSON><PERSON> started. Analyzing ALL ALLOWANCES PDF structure...")

def analyze_pdf_structure(pdf_file):
    """
    Analyze the structure of the ALL ALLOWANCES PDF.
    
    Args:
        pdf_file (str): Path to the ALL ALLOWANCES PDF file
    """
    # Check if the file exists
    if not pdf_file or not os.path.exists(pdf_file):
        print(f"PDF file not found: {pdf_file}")
        return
    
    try:
        # Import PyPDF2 for PDF extraction
        import PyPDF2
        
        # Open the PDF file
        with open(pdf_file, 'rb') as file:
            # Create a PDF reader object
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Get the number of pages
            num_pages = len(pdf_reader.pages)
            print(f"PDF has {num_pages} pages")
            
            # Analyze a sample of pages (first, middle, and last)
            sample_pages = [0]  # Start with first page
            
            if num_pages > 2:
                sample_pages.append(num_pages // 2)  # Add middle page
            
            if num_pages > 1:
                sample_pages.append(num_pages - 1)  # Add last page
            
            # Dictionary to store section information
            sections = {}
            
            # Process each sample page
            for page_num in sample_pages:
                print(f"\nAnalyzing page {page_num + 1} of {num_pages}...")
                
                # Extract text from the page
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                
                # Print the page text for analysis
                print(f"\nPage {page_num + 1} text sample:")
                print(text[:1000] + "..." if len(text) > 1000 else text)
                
                # Split the text into lines
                lines = text.split('\n')
                
                # Print the first few lines
                print("\nFirst few lines:")
                for i, line in enumerate(lines[:10]):
                    print(f"Line {i+1}: {line}")
                
                # Look for section headers
                current_section = None
                
                for i, line in enumerate(lines):
                    # Look for section headers (focus areas and subtitles)
                    section_match = re.search(r'(?:SA Period Code: MAR \d{4} )?([\w\s\-\&\(\)]+(?:ALLOWANCE|SUBSIDY|BENEFIT|BONUS|INCENTIVE|PAYMENT|REIMBURSEMENT|STIPEND|GRANT|COMPENSATION))', line, re.IGNORECASE)
                    if section_match:
                        current_section = section_match.group(1).strip()
                        print(f"Found section: {current_section} at line {i+1}")
                        
                        # Store section information
                        if current_section not in sections:
                            sections[current_section] = {
                                'count': 1,
                                'sample_lines': [line],
                                'pages': [page_num + 1]
                            }
                        else:
                            sections[current_section]['count'] += 1
                            sections[current_section]['sample_lines'].append(line)
                            if page_num + 1 not in sections[current_section]['pages']:
                                sections[current_section]['pages'].append(page_num + 1)
                    
                    # Look for employee data lines
                    employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
                    if employee_match and current_section:
                        employee_no = employee_match.group(1).strip()
                        print(f"Found employee: {employee_no} in section {current_section} at line {i+1}")
                        print(f"  Line content: {line}")
                        
                        # Look for amount in the line
                        amount_matches = re.findall(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                        if amount_matches:
                            print(f"  Potential amounts: {amount_matches}")
            
            # Print summary of sections found
            print("\nSections found in the PDF:")
            for section, info in sections.items():
                print(f"Section: {section}")
                print(f"  Appears {info['count']} times on pages: {info['pages']}")
                print(f"  Sample line: {info['sample_lines'][0]}")
                print()
            
            # Now analyze all pages to count total employees per section
            print("\nAnalyzing all pages to count employees per section...")
            
            # Dictionary to store employee counts per section
            employee_counts = {section: 0 for section in sections.keys()}
            
            # Process each page
            for page_num in range(num_pages):
                # Extract text from the page
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                
                # Split the text into lines
                lines = text.split('\n')
                
                # Track current section
                current_section = None
                
                for line in lines:
                    # Look for section headers
                    section_match = re.search(r'(?:SA Period Code: MAR \d{4} )?([\w\s\-\&\(\)]+(?:ALLOWANCE|SUBSIDY|BENEFIT|BONUS|INCENTIVE|PAYMENT|REIMBURSEMENT|STIPEND|GRANT|COMPENSATION))', line, re.IGNORECASE)
                    if section_match:
                        current_section = section_match.group(1).strip()
                        continue
                    
                    # Look for employee data lines
                    employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
                    if employee_match and current_section and current_section in employee_counts:
                        employee_counts[current_section] += 1
            
            # Print employee counts per section
            print("\nEmployee counts per section:")
            total_employees = 0
            for section, count in employee_counts.items():
                print(f"{section}: {count} employees")
                total_employees += count
            
            print(f"\nTotal employees found across all sections: {total_employees}")
            
    except Exception as e:
        print(f"Error analyzing PDF: {str(e)}")
        import traceback
        print(traceback.format_exc())

def main():
    if len(sys.argv) < 2:
        print("Usage: python analyze_allowances_pdf.py <allowances_pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    print(f"File path: {pdf_file}")
    print(f"File exists: {os.path.exists(pdf_file)}")
    
    analyze_pdf_structure(pdf_file)

if __name__ == "__main__":
    main()
