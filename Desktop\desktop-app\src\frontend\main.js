const { app, BrowserWindow, ipcMain, dialog, nativeImage } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const { checkDependencies } = require('./check_dependencies');
const AdmZip = require('adm-zip');

// Enable detailed logging
process.env.ELECTRON_ENABLE_LOGGING = true;
process.env.ELECTRON_ENABLE_STACK_DUMPING = true;

// Keep a global reference of the window object to prevent garbage collection
let mainWindow;
let backendProcess = null;

// Keep track of active processes
let activeProcesses = {
  comparison: null,
  sort: null,
  build: null,
  bankAdviser: null
};

function createWindow() {
  console.log('Creating main window...');
  console.log('Current directory:', __dirname);

  try {
    // Create the browser window
    mainWindow = new BrowserWindow({
      width: 1024,
      height: 768,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, 'assets', 'icon.png'),
      backgroundColor: '#1a237e', // Set background color to match the header
      show: true, // Show immediately
      paintWhenInitiallyHidden: true // Paint the window even when it's hidden
    });

    console.log('Main window created successfully');

    // Check if index.html exists
    const indexPath = path.join(__dirname, 'index.html');
    console.log(`Checking if index.html exists at: ${indexPath}`);

    if (fs.existsSync(indexPath)) {
      console.log('index.html found, loading file...');

      // Load the file immediately without clearing cache
      mainWindow.loadFile(indexPath);

      // Window ready to show event
      mainWindow.once('ready-to-show', () => {
        console.log('Window ready to show');

        // Window is already visible
        console.log('Main window is already visible');
      });

      // Log any load errors
      mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error(`Failed to load: ${errorDescription} (${errorCode})`);
        dialog.showErrorBox('Load Error', `Failed to load: ${errorDescription}`);
      });
    } else {
      console.error('index.html not found!');
      dialog.showErrorBox('File Not Found', `Could not find index.html at ${indexPath}`);
      app.quit();
    }

    // Emitted when the window is closed
    mainWindow.on('closed', function () {
      console.log('Main window closed');
      mainWindow = null;
      if (backendProcess) backendProcess.kill();
    });
  } catch (error) {
    console.error('Error creating window:', error);
    dialog.showErrorBox('Error', `Failed to create window: ${error.message}`);
    app.quit();
  }
}

// Create application menu with dependency installation option
function createMenu() {
  const { Menu } = require('electron');
  const template = [
    {
      label: 'File',
      submenu: [
        { role: 'quit' }
      ]
    },
    {
      label: 'Tools',
      submenu: [
        {
          label: 'Install Dependencies',
          click: () => {
            const installerPath = path.join(__dirname, 'install_dependencies.bat');
            // Run silently with windowsHide option
            require('child_process').exec(`"${installerPath}"`, { windowsHide: true }, (error) => {
              if (error) {
                console.error('Failed to run installer:', error);
                dialog.showMessageBox(mainWindow, {
                  type: 'error',
                  title: 'Installation Error',
                  message: 'Failed to run the installer.',
                  detail: error.message
                });
              }
            });
          }
        },
        {
          label: 'Move Tesseract to Standard Location',
          click: () => {
            const moveScript = path.join(__dirname, 'move_tesseract.bat');
            // Run silently with windowsHide option
            require('child_process').exec(`"${moveScript}"`, { windowsHide: true }, (error) => {
              if (error) {
                console.error('Failed to run move script:', error);
                dialog.showMessageBox(mainWindow, {
                  type: 'error',
                  title: 'Move Error',
                  message: 'Failed to move Tesseract to standard location.',
                  detail: error.message
                });
              } else {
                dialog.showMessageBox(mainWindow, {
                  type: 'info',
                  title: 'Tesseract Move',
                  message: 'Tesseract move process started.',
                  detail: 'Tesseract is being moved to the standard location (C:\\Program Files\\Tesseract-OCR). You may need to restart the application after the move is complete.'
                });
              }
            });
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Payroll Auditor',
              message: 'Payroll Auditor',
              detail: 'A tool for auditing and comparing payroll data.\n\nVersion: 1.0.0'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Check if Tesseract is in vendor directory and needs to be moved
function checkTesseractLocation() {
  return new Promise((resolve, reject) => {
    try {
      const vendorTesseractPath = path.join(__dirname, 'vendor', 'tesseract');
      const standardTesseractPath = 'C:\\Program Files\\Tesseract-OCR';

      console.log(`Checking for Tesseract in vendor directory: ${vendorTesseractPath}`);

      // If Tesseract is in vendor directory but not in standard location, move it
      if (fs.existsSync(vendorTesseractPath) && !fs.existsSync(standardTesseractPath)) {
        console.log('Found Tesseract in vendor directory but not in standard location');

        // Run the move_tesseract.bat script silently
        const moveScript = path.join(__dirname, 'move_tesseract.bat');
        console.log(`Running move script silently: ${moveScript}`);

        // Use windowsHide option to hide the command window
        require('child_process').exec(`"${moveScript}"`, { windowsHide: true }, (error) => {
          if (error) {
            console.error('Failed to run move script:', error);
            // Continue anyway
            resolve();
          } else {
            console.log('Move script started successfully');
            // Wait a bit for the script to complete
            setTimeout(() => {
              resolve();
            }, 5000);
          }
        });
      } else {
        console.log('No need to move Tesseract');
        resolve();
      }
    } catch (error) {
      console.error('Error checking Tesseract location:', error);
      // Continue anyway
      resolve();
    }
  });
}

// Get the app path for resources
function getResourcePath(relativePath) {
  // In production, resources are in the 'resources' directory
  if (app.isPackaged) {
    return path.join(process.resourcesPath, relativePath);
  } else {
    // In development, resources are in the project directory
    return path.join(__dirname, '..', relativePath);
  }
}

// Get the Python executable path
function getPythonPath() {
  if (app.isPackaged) {
    // In production, use the embedded Python
    return path.join(process.resourcesPath, 'python-embedded', 'python.exe');
  } else {
    // In development, use the system Python
    return 'python';
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  console.log('Electron app is ready');

  // Set up error handling for the app
  process.on('uncaughtException', (error) => {
    console.error('Uncaught exception:', error);
    dialog.showErrorBox('Uncaught Exception', `An error occurred: ${error.message}`);
  });

  // Create main window immediately
  createWindow();
  createMenu();

  // Initialize the global flag for dependency check
  global.dependenciesCheckFailed = false;

  // Run dependency checks in the background after the window is shown
  setTimeout(async () => {
    try {
      console.log('Checking dependencies silently in background...');
      const dependenciesOk = await checkDependencies(null, false, true);
      console.log(`Dependencies check result: ${dependenciesOk ? 'OK' : 'Missing dependencies'}`);

      // Set the global flag if dependencies check failed
      if (!dependenciesOk) {
        console.log('Dependencies check failed, will show message in main window later');
        global.dependenciesCheckFailed = true;

        // Show dependency warning if needed
        if (mainWindow && mainWindow.isVisible()) {
          dialog.showMessageBox(mainWindow, {
            type: 'warning',
            title: 'Missing Dependencies',
            message: 'Some dependencies are missing.',
            detail: 'The application may not function correctly. You can install missing dependencies from the Tools menu.',
            buttons: ['Continue Anyway'],
            defaultId: 0
          });
        }
      }

      // Check and move Tesseract if needed
      await checkTesseractLocation();
    } catch (error) {
      console.error('Error checking dependencies:', error);
      // Just continue with the application
    }
  }, 3000); // Run dependency checks 3 seconds after startup

  app.on('activate', function () {
    // On macOS it's common to re-create a window when the dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
}).catch(error => {
  console.error('Failed to initialize app:', error);
  dialog.showErrorBox('Initialization Error', `Failed to initialize app: ${error.message}`);
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', function () {
  console.log('All windows closed');
  if (process.platform !== 'darwin') app.quit();
});

// Log any unhandled exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  dialog.showErrorBox('Uncaught Exception', `An error occurred: ${error.message}`);
});

// Enhanced Dictionary management handlers
ipcMain.handle('get-enhanced-dictionary', async () => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'enhanced_payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Enhanced dictionary script not found: ${scriptPath}`);
      return null;
    }

    // Run the script to get the dictionary
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import enhanced_payroll_dictionaries
import json

# Get the dictionary
dictionary = enhanced_payroll_dictionaries.load_dictionary()

# Print the dictionary as JSON
print(json.dumps(dictionary))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Enhanced dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const dictionary = JSON.parse(output);
            resolve(dictionary);
          } catch (error) {
            console.error('Error parsing enhanced dictionary output:', error);
            resolve(null);
          }
        } else {
          console.error(`Enhanced dictionary process exited with code ${code}`);
          resolve(null);
        }
      });
    });
  } catch (error) {
    console.error('Error getting enhanced dictionary:', error);
    return null;
  }
});

ipcMain.handle('save-enhanced-dictionary', async (event, dictionary) => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'enhanced_payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Enhanced dictionary script not found: ${scriptPath}`);
      return false;
    }

    // Run the script to save the dictionary
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import enhanced_payroll_dictionaries
import json

# Parse the dictionary from JSON
dictionary = json.loads('''${JSON.stringify(dictionary)}''')

# Save the dictionary
success = enhanced_payroll_dictionaries.save_dictionary(dictionary)

# Print the result
print(json.dumps({
    'success': success
}))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Enhanced dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result.success);
          } catch (error) {
            console.error('Error parsing enhanced dictionary output:', error);
            resolve(false);
          }
        } else {
          console.error(`Enhanced dictionary process exited with code ${code}`);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('Error saving enhanced dictionary:', error);
    return false;
  }
});

ipcMain.handle('reset-enhanced-dictionary', async () => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'enhanced_payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Enhanced dictionary script not found: ${scriptPath}`);
      return false;
    }

    // Run the script to reset the dictionary
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import enhanced_payroll_dictionaries
import json
import os

# Delete the dictionary file
try:
    if os.path.exists(enhanced_payroll_dictionaries.DICTIONARY_PATH):
        os.remove(enhanced_payroll_dictionaries.DICTIONARY_PATH)
except Exception as e:
    print(f"Error deleting dictionary file: {e}", file=sys.stderr)

# Get the default dictionary
dictionary = enhanced_payroll_dictionaries.load_dictionary()

# Print success
print(json.dumps({
    'success': True
}))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Enhanced dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result.success);
          } catch (error) {
            console.error('Error parsing enhanced dictionary output:', error);
            resolve(false);
          }
        } else {
          console.error(`Enhanced dictionary process exited with code ${code}`);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('Error resetting enhanced dictionary:', error);
    return false;
  }
});

ipcMain.handle('import-dictionary', async () => {
  try {
    // Show file dialog to select Excel file
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'Excel Files', extensions: ['xlsx', 'xls'] }
      ]
    });

    if (result.canceled || result.filePaths.length === 0) {
      return { success: false, error: 'No file selected' };
    }

    const filePath = result.filePaths[0];

    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'enhanced_payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Enhanced dictionary script not found: ${scriptPath}`);
      return { success: false, error: 'Dictionary script not found' };
    }

    // Run the script to import the dictionary
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import enhanced_payroll_dictionaries
import json
import pandas as pd

try:
    # Read the Excel file
    df = pd.read_excel(r'${filePath.replace(/\\/g, '\\\\')}', sheet_name='Payslip Dictionary')

    # Convert to list of dictionaries
    excel_data = df.to_dict('records')

    # Import the data
    success = enhanced_payroll_dictionaries.import_from_excel(excel_data)

    # Print the result
    print(json.dumps({
        'success': success,
        'itemCount': len(excel_data)
    }))
except Exception as e:
    print(json.dumps({
        'success': False,
        'error': str(e)
    }))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Import dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result);
          } catch (error) {
            console.error('Error parsing import result:', error);
            resolve({ success: false, error: 'Error parsing import result' });
          }
        } else {
          console.error(`Import process exited with code ${code}`);
          resolve({ success: false, error: `Process exited with code ${code}` });
        }
      });
    });
  } catch (error) {
    console.error('Error importing dictionary:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-dictionary', async () => {
  try {
    // Show file dialog to save Excel file
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (result.canceled || !result.filePath) {
      return false;
    }

    const filePath = result.filePath;

    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'enhanced_payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Enhanced dictionary script not found: ${scriptPath}`);
      return false;
    }

    // Run the script to export the dictionary
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import enhanced_payroll_dictionaries
import json
import pandas as pd

try:
    # Get the dictionary data in Excel format
    excel_data = enhanced_payroll_dictionaries.export_to_excel_format()

    # Create a DataFrame
    df = pd.DataFrame(excel_data)

    # Create a writer
    writer = pd.ExcelWriter(r'${filePath.replace(/\\/g, '\\\\')}', engine='openpyxl')

    # Write the data to the Excel file
    df.to_excel(writer, sheet_name='Payslip Dictionary', index=False)

    # Save the Excel file
    writer.save()

    # Print success
    print(json.dumps({
        'success': True
    }))
except Exception as e:
    print(json.dumps({
        'success': False,
        'error': str(e)
    }))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Export dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result.success);
          } catch (error) {
            console.error('Error parsing export result:', error);
            resolve(false);
          }
        } else {
          console.error(`Export process exited with code ${code}`);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('Error exporting dictionary:', error);
    return false;
  }
});

// Original Dictionary management handlers
ipcMain.handle('get-dictionaries', async () => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Dictionary script not found: ${scriptPath}`);
      return null;
    }

    // Run the script to get the dictionaries
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import payroll_dictionaries
import json

# Get the dictionaries
earnings = payroll_dictionaries.get_earnings_dictionary()
deductions = payroll_dictionaries.get_deductions_dictionary()
loans = payroll_dictionaries.get_loans_dictionary()

# Print the dictionaries as JSON
print(json.dumps({
    'earnings': earnings,
    'deductions': deductions,
    'loans': loans
}))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const dictionaries = JSON.parse(output);
            resolve(dictionaries);
          } catch (error) {
            console.error('Error parsing dictionary output:', error);
            resolve(null);
          }
        } else {
          console.error(`Dictionary process exited with code ${code}`);
          resolve(null);
        }
      });
    });
  } catch (error) {
    console.error('Error getting dictionaries:', error);
    return null;
  }
});

ipcMain.handle('save-dictionaries', async (event, dictionaries) => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Dictionary script not found: ${scriptPath}`);
      return false;
    }

    // Run the script to save the dictionaries
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import payroll_dictionaries
import json

# Parse the dictionaries from JSON
dictionaries = json.loads('''${JSON.stringify(dictionaries)}''')

# Save the dictionaries
earnings_saved = payroll_dictionaries.save_earnings_dictionary(dictionaries['earnings'])
deductions_saved = payroll_dictionaries.save_deductions_dictionary(dictionaries['deductions'])
loans_saved = payroll_dictionaries.save_loans_dictionary(dictionaries['loans'])

# Print the result
print(json.dumps({
    'success': earnings_saved and deductions_saved and loans_saved
}))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output);
            resolve(result.success);
          } catch (error) {
            console.error('Error parsing dictionary output:', error);
            resolve(false);
          }
        } else {
          console.error(`Dictionary process exited with code ${code}`);
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.error('Error saving dictionaries:', error);
    return false;
  }
});

ipcMain.handle('reset-dictionaries', async () => {
  try {
    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Get the script path
    const scriptPath = path.join(__dirname, 'backend-dist', 'payroll_dictionaries.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`Dictionary script not found: ${scriptPath}`);
      return null;
    }

    // Run the script to reset the dictionaries
    const process = spawn(pythonPath, ['-c', `
import sys
# Use raw string for Windows path to avoid Unicode escape issues
sys.path.append(r'${path.dirname(scriptPath).replace(/\\/g, '\\\\')}')
import payroll_dictionaries
import json
import os

# Delete the dictionary files
try:
    if os.path.exists(payroll_dictionaries.EARNINGS_DICT_PATH):
        os.remove(payroll_dictionaries.EARNINGS_DICT_PATH)
    if os.path.exists(payroll_dictionaries.DEDUCTIONS_DICT_PATH):
        os.remove(payroll_dictionaries.DEDUCTIONS_DICT_PATH)
    if os.path.exists(payroll_dictionaries.LOANS_DICT_PATH):
        os.remove(payroll_dictionaries.LOANS_DICT_PATH)
except Exception as e:
    print(f"Error deleting dictionary files: {e}", file=sys.stderr)

# Get the default dictionaries
earnings = payroll_dictionaries.get_earnings_dictionary()
deductions = payroll_dictionaries.get_deductions_dictionary()
loans = payroll_dictionaries.get_loans_dictionary()

# Print the dictionaries as JSON
print(json.dumps({
    'earnings': earnings,
    'deductions': deductions,
    'loans': loans
}))
`]);

    return new Promise((resolve, reject) => {
      let output = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        console.error(`Dictionary error: ${data.toString()}`);
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const dictionaries = JSON.parse(output);
            resolve(dictionaries);
          } catch (error) {
            console.error('Error parsing dictionary output:', error);
            resolve(null);
          }
        } else {
          console.error(`Dictionary process exited with code ${code}`);
          resolve(null);
        }
      });
    });
  } catch (error) {
    console.error('Error resetting dictionaries:', error);
    return null;
  }
});

// IPC handler to run the backend process
ipcMain.handle('run-backend', async (event, { prevFile, currFile, outputDir }) => {
  return new Promise((resolve, reject) => {
    const pythonScript = path.join(__dirname, '..', 'backend', 'main.py');
    console.log(`Python script path: ${pythonScript}`);
    if (!fs.existsSync(pythonScript)) {
      resolve({ success: false, error: 'Backend script not found.' });
      return;
    }
    console.log(`Running Python script: ${pythonScript}`);
    console.log(`Parameters: ${prevFile}, ${currFile}, ${outputDir}`);

    backendProcess = spawn('python', [pythonScript, prevFile, currFile, outputDir]);
    let output = '';
    backendProcess.stdout.on('data', (data) => {
      const message = data.toString();
      console.log(`Python stdout: ${message}`);
      output += message;
      mainWindow.webContents.send('backend-progress', message);
    });
    backendProcess.stderr.on('data', (data) => {
      const errorMsg = data.toString();
      console.error(`Python stderr: ${errorMsg}`);
      mainWindow.webContents.send('backend-progress', 'Error: ' + errorMsg);
    });
    backendProcess.on('close', (code) => {
      mainWindow.webContents.send('backend-progress', 'Done. Exit code: ' + code);
      // Parse output for report links
      const links = [];
      const excelMatch = output.match(/Excel: (.*\.xlsx)/);
      const csvMatch = output.match(/CSV: (.*\.csv)/);
      const jsonMatch = output.match(/JSON: (.*\.json)/);
      if (excelMatch) links.push({ type: 'Excel', path: excelMatch[1] });
      if (csvMatch) links.push({ type: 'CSV', path: csvMatch[1] });
      if (jsonMatch) links.push({ type: 'JSON', path: jsonMatch[1] });
      resolve({ success: true, links });
    });
    backendProcess.on('error', (err) => {
      resolve({ success: false, error: err.message });
    });
  });
});

// File selection dialog
ipcMain.handle('select-file', async (event, options = {}) => {
  const defaultOptions = {
    properties: ['openFile'],
    filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
  };

  // Merge default options with provided options
  const dialogOptions = { ...defaultOptions, ...options };

  const result = await dialog.showOpenDialog(mainWindow, dialogOptions);
  if (!result.canceled && result.filePaths.length > 0) {
    // If multiSelections is enabled, return all file paths
    if (dialogOptions.properties && dialogOptions.properties.includes('multiSelections')) {
      return result.filePaths;
    }
    // Otherwise, return just the first file path
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });
  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

// Process PDFs handler - direct implementation instead of alias
ipcMain.handle('process-pdfs', async (event, data) => {
  console.log('process-pdfs called with data:', data);

  return new Promise((resolve, reject) => {
    try {
      const { prevFile, currFile, outputDir, idField, currentMonth, currentYear, previousMonth, previousYear } = data;

      // Validate inputs
      if (!prevFile || !currFile) {
        console.error('Missing required files');
        resolve({ success: false, error: 'Missing required files' });
        return;
      }

      // Get report signature information
      const reportName = data.reportName || '';
      const reportDesignation = data.reportDesignation || '';

      if (!reportName || !reportDesignation) {
        console.error('Missing report signature information');
        resolve({ success: false, error: 'Missing report signature information' });
        return;
      }

      // Use the improved parser - try both backend-dist and backend directories
      let pythonScript;

      // First try backend-dist directory (where our updated script is)
      const backendDistScript = path.join(__dirname, 'backend-dist', 'improved_payroll_parser.py');
      if (fs.existsSync(backendDistScript)) {
        pythonScript = backendDistScript;
        console.log(`Found Python script in backend-dist directory: ${pythonScript}`);
      } else {
        // Fall back to the original locations
        pythonScript = app.isPackaged
          ? path.join(process.resourcesPath, 'backend', 'improved_payroll_parser.py')
          : path.join(__dirname, '..', 'backend', 'improved_payroll_parser.py');
        console.log(`Using default Python script path: ${pythonScript}`);
      }

      console.log(`Python script path: ${pythonScript}`);

      if (!fs.existsSync(pythonScript)) {
        console.error(`Backend script not found: ${pythonScript}`);
        resolve({ success: false, error: 'Backend script not found.' });
        return;
      }

      console.log(`Running Python script: ${pythonScript}`);
      console.log(`Parameters: ${prevFile}, ${currFile}, ${outputDir || '.'}, ${idField || 'employee_id'}`);

      // Build command arguments for the compare command
      const args = [
        pythonScript,
        'compare',
        prevFile,
        currFile,
        outputDir || '.'
      ];

      // Add the ID field if specified
      if (idField) {
        args.push('--id-field', idField);
      }

      // Add month and year parameters to the command if provided
      if (currentMonth && currentYear) {
        args.push('--current-month', currentMonth);
        args.push('--current-year', currentYear);
      }

      if (previousMonth && previousYear) {
        args.push('--previous-month', previousMonth);
        args.push('--previous-year', previousYear);
      }

      // Add report signature information
      if (reportName) {
        args.push('--report-name', reportName);
      }

      if (reportDesignation) {
        args.push('--report-designation', reportDesignation);
      }

      // Log the month/year information and full command
      console.log(`Month/Year info passed to Python: Current: ${currentMonth || "Not provided"} ${currentYear || "Not provided"}, Previous: ${previousMonth || "Not provided"} ${previousYear || "Not provided"}`);
      console.log(`Full Python command: python ${args.join(' ')}`);

      // Get the Python executable path
      const pythonPath = getPythonPath();
      console.log(`Using Python executable: ${pythonPath}`);

      const process = spawn(pythonPath, args);

      // Store the active process
      activeProcesses.comparison = process;

      let output = '';

      process.stdout.on('data', (data) => {
        const message = data.toString();
        console.log(`Python stdout: ${message}`);
        output += message;

        // Send progress updates immediately to the frontend
        mainWindow.webContents.send('backend-progress', message);

        // Force immediate UI update for progress messages
        if (message.includes('Processing page')) {
          const match = message.match(/Processing page (\d+)\/(\d+)/);
          if (match) {
            const current = parseInt(match[1]);
            const total = parseInt(match[2]);
            console.log(`Sending direct progress update: ${current}/${total}`);
            mainWindow.webContents.send('direct-progress-update', { current, total });
          }
        }

        // Also check for any other progress indicators
        const progressMatch = message.match(/Progress: (\d+)\/(\d+)/);
        if (progressMatch) {
          const current = parseInt(progressMatch[1]);
          const total = parseInt(progressMatch[2]);
          console.log(`Sending direct progress update from Progress indicator: ${current}/${total}`);
          mainWindow.webContents.send('direct-progress-update', { current, total });
        }
      });

      process.stderr.on('data', (data) => {
        const errorMsg = data.toString();
        console.error(`Python stderr: ${errorMsg}`);
        mainWindow.webContents.send('backend-progress', 'Error: ' + errorMsg);
      });

      process.on('close', (code) => {
        console.log(`Python process exited with code ${code}`);
        mainWindow.webContents.send('backend-progress', 'Done. Exit code: ' + code);

        // Clear the active process reference
        activeProcesses.comparison = null;

        // Parse output for report links
        const links = [];
        const excelMatch = output.match(/Excel report: (.*\.xlsx)/);
        const wordMatch = output.match(/Word: (.*\.docx)/);
        const pdfMatch = output.match(/PDF: (.*\.pdf)/);
        const csvMatch = output.match(/CSV report: (.*\.csv)/);
        const jsonMatch = output.match(/JSON report: (.*\.json)/);

        // No need to rename files anymore as the improved report generator
        // already includes month and year information in the filenames

        if (excelMatch) {
          const excelPath = excelMatch[1];
          links.push({ type: 'Excel', path: excelPath });
          console.log(`Found Excel report: ${excelPath}`);
        }

        if (wordMatch) {
          const wordPath = wordMatch[1];
          links.push({ type: 'Word', path: wordPath });
          console.log(`Found Word report: ${wordPath}`);
        }

        if (pdfMatch) {
          const pdfPath = pdfMatch[1];
          links.push({ type: 'PDF', path: pdfPath });
          console.log(`Found PDF report: ${pdfPath}`);
        }

        // If we have Excel but not Word or PDF, try to generate them
        if (excelMatch && (!wordMatch || !pdfMatch)) {
          try {
            const excelPath = excelMatch[1];
            console.log(`Checking if we need to generate additional reports from: ${excelPath}`);

            // Generate Word document and PDF using Python converter if they don't exist
            const basePath = excelPath.replace('.xlsx', '');
            const wordPath = `${basePath}.docx`;
            const pdfPath = `${basePath}.pdf`;

            // Function to convert using Python script
            const convertWithPython = (inputPath, outputPath, format, sheetName = null) => {
              return new Promise((resolve, reject) => {
                // Path to the Python converter script
                const converterScript = path.join(__dirname, '..', 'backend', 'file_converter.py');

                if (!fs.existsSync(converterScript)) {
                  console.error(`Converter script not found: ${converterScript}`);
                  // Create the file_converter.py script if it doesn't exist
                  try {
                    const scriptContent = `#!/usr/bin/env python3
"""
File Converter for Payroll Auditor

This script provides functions to convert between different file formats:
- Excel to Word
- Excel to PDF
- Word to PDF

It uses Python libraries to handle the conversions without relying on external tools.
"""

import os
import sys
import pandas as pd
from docx import Document
from docx.shared import Pt, Inches, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_ALIGN_VERTICAL
import openpyxl
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def excel_to_docx(excel_path, docx_path):
    """
    Convert Excel file to Word document.

    Args:
        excel_path: Path to the Excel file
        docx_path: Path to save the Word document

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Converting Excel to Word: {excel_path} -> {docx_path}")

        # Load the Excel workbook
        wb = openpyxl.load_workbook(excel_path)

        # Create a new Word document
        doc = Document()

        # Add title
        title = doc.add_heading('Payroll Audit Report', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add timestamp
        timestamp = doc.add_paragraph()
        timestamp.add_run(f"Generated from: {os.path.basename(excel_path)}")
        timestamp.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Process each worksheet
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]

            # Add sheet name as heading
            doc.add_heading(sheet_name, level=2)

            # Get all rows and columns with data
            rows = list(sheet.rows)
            if not rows:
                doc.add_paragraph("No data in this sheet")
                continue

            # Create table - no row limit, handle all data
            num_rows = len(rows)  # Process all rows
            num_cols = len(rows[0])

            # Create table with header row
            table = doc.add_table(rows=num_rows, cols=num_cols)
            table.style = 'Table Grid'

            # Add header row
            header_row = table.rows[0]
            for col_idx, cell in enumerate(rows[0]):
                header_cell = header_row.cells[col_idx]
                header_cell.text = str(cell.value) if cell.value is not None else ""
                # Style header
                for paragraph in header_cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True

            # Add data rows
            for row_idx in range(1, num_rows):
                for col_idx, cell in enumerate(rows[row_idx]):
                    table_cell = table.rows[row_idx].cells[col_idx]
                    table_cell.text = str(cell.value) if cell.value is not None else ""

            # Add a page break after each sheet except the last one
            if sheet_name != wb.sheetnames[-1]:
                doc.add_page_break()

        # Save the Word document
        doc.save(docx_path)
        print(f"Word document saved to: {docx_path}")
        return True

    except Exception as e:
        print(f"Error converting Excel to Word: {str(e)}")
        return False

def excel_to_pdf(excel_path, pdf_path):
    """
    Convert Excel file to PDF.

    Args:
        excel_path: Path to the Excel file
        pdf_path: Path to save the PDF

    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Converting Excel to PDF: {excel_path} -> {pdf_path}")

        # Load the Excel workbook
        wb = openpyxl.load_workbook(excel_path)

        # Create a PDF document
        doc = SimpleDocTemplate(pdf_path, pagesize=letter)
        elements = []

        # Add styles
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        heading_style = styles['Heading2']
        normal_style = styles['Normal']

        # Add title
        elements.append(Paragraph('Payroll Audit Report', title_style))
        elements.append(Paragraph(f"Generated from: {os.path.basename(excel_path)}", normal_style))
        elements.append(Spacer(1, 0.25*inch))

        # Process each worksheet
        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]

            # Add sheet name as heading
            elements.append(Paragraph(sheet_name, heading_style))
            elements.append(Spacer(1, 0.1*inch))

            # Get all rows and columns with data
            data = []
            for row in sheet.rows:
                row_data = []
                for cell in row:
                    row_data.append(str(cell.value) if cell.value is not None else "")
                data.append(row_data)

            if not data:
                elements.append(Paragraph("No data in this sheet", normal_style))
                continue

            # Create table with pagination for large datasets
            # Split data into chunks of 500 rows for better PDF rendering
            chunk_size = 500
            data_chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]

            for chunk_index, chunk in enumerate(data_chunks):
                # Add page number if multiple chunks
                if len(data_chunks) > 1 and chunk_index > 0:
                    elements.append(Paragraph(f"Page {chunk_index + 1} of {len(data_chunks)}", normal_style))
                    elements.append(Spacer(1, 0.1*inch))

                # Create table for this chunk
                table = Table(chunk if chunk_index == 0 else [chunk[0]] + chunk[1:])  # Include header row for first chunk

                # Style the table
                style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ])
                table.setStyle(style)

                # Add table to elements
                elements.append(table)
                elements.append(Spacer(1, 0.5*inch))

        # Build the PDF
        doc.build(elements)
        print(f"PDF saved to: {pdf_path}")
        return True

    except Exception as e:
        print(f"Error converting Excel to PDF: {str(e)}")
        return False

def main():
    """Main function to handle command-line arguments."""
    if len(sys.argv) < 4:
        print("Usage: python file_converter.py <format> <input_file> <output_file>")
        print("Formats: docx, pdf")
        print("Example: python file_converter.py docx input.xlsx output.docx")
        print("Example: python file_converter.py pdf input.xlsx output.pdf")
        return

    format_type = sys.argv[1].lower()
    input_file = sys.argv[2]
    output_file = sys.argv[3]

    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist")
        return

    if format_type == "docx":
        if input_file.endswith(".xlsx") or input_file.endswith(".xls"):
            excel_to_docx(input_file, output_file)
        else:
            print("Error: Input file must be an Excel file (.xlsx or .xls)")

    elif format_type == "pdf":
        if input_file.endswith(".xlsx") or input_file.endswith(".xls"):
            excel_to_pdf(input_file, output_file)
        else:
            print("Error: Input file must be an Excel or Word file")

    else:
        print(f"Error: Unsupported format '{format_type}'")
        print("Supported formats: docx, pdf")

if __name__ == "__main__":
    main()`;

                    fs.writeFileSync(converterScript, scriptContent);
                    console.log(`Created file_converter.py script at ${converterScript}`);
                  } catch (writeErr) {
                    console.error(`Error creating converter script: ${writeErr.message}`);
                    resolve(false);
                    return;
                  }
                }

                console.log(`Converting ${format} with Python: ${inputPath} -> ${outputPath}`);

                // Execute the Python script
                const args = [
                  converterScript,
                  format.toLowerCase(),
                  inputPath,
                  outputPath
                ];

                // Add sheet name if provided
                if (sheetName) {
                  args.push(sheetName);
                }

                const process = spawn('python', args);

                let stdoutData = '';
                let stderrData = '';

                process.stdout.on('data', (data) => {
                  const message = data.toString();
                  stdoutData += message;
                  console.log(`Python conversion stdout: ${message}`);
                });

                process.stderr.on('data', (data) => {
                  const message = data.toString();
                  stderrData += message;
                  console.error(`Python conversion stderr: ${message}`);
                });

                process.on('close', (code) => {
                  if (code === 0) {
                    console.log(`Successfully converted to ${format}`);
                    resolve(true);
                  } else {
                    console.error(`Conversion to ${format} failed with code ${code}`);
                    console.error(`Error details: ${stderrData}`);

                    // Check if we need to install dependencies
                    if (stderrData.includes("No module named")) {
                      const missingModule = stderrData.match(/No module named '([^']+)'/);
                      if (missingModule && missingModule[1]) {
                        console.log(`Installing missing module: ${missingModule[1]}`);

                        // Install the missing module
                        const installProcess = spawn('pip', ['install', missingModule[1]]);

                        installProcess.stdout.on('data', (data) => {
                          console.log(`pip install stdout: ${data.toString()}`);
                        });

                        installProcess.stderr.on('data', (data) => {
                          console.error(`pip install stderr: ${data.toString()}`);
                        });

                        installProcess.on('close', (installCode) => {
                          if (installCode === 0) {
                            console.log(`Successfully installed ${missingModule[1]}`);
                            console.log(`Retrying conversion...`);

                            // Retry the conversion
                            const retryArgs = [
                              converterScript,
                              format.toLowerCase(),
                              inputPath,
                              outputPath
                            ];

                            // Add sheet name if provided
                            if (sheetName) {
                              retryArgs.push(sheetName);
                            }

                            const retryProcess = spawn('python', retryArgs);

                            retryProcess.on('close', (retryCode) => {
                              if (retryCode === 0) {
                                console.log(`Successfully converted to ${format} after installing dependencies`);
                                resolve(true);
                              } else {
                                console.error(`Conversion to ${format} failed after installing dependencies`);
                                resolve(false);
                              }
                            });
                          } else {
                            console.error(`Failed to install ${missingModule[1]}`);
                            resolve(false);
                          }
                        });
                      } else {
                        resolve(false);
                      }
                    } else {
                      resolve(false);
                    }
                  }
                });

                process.on('error', (err) => {
                  console.error(`Error executing Python conversion: ${err.message}`);
                  resolve(false);
                });
              });
            };

            // Convert to Word using Python if it doesn't exist
            if (!wordMatch) {
              convertWithPython(excelPath, wordPath, 'docx', 'Changes Only')
                .then(wordSuccess => {
                  if (wordSuccess) {
                    console.log(`Generated Word report: ${wordPath}`);
                    links.push({ type: 'Word', path: wordPath });
                  } else {
                    console.error('Failed to generate Word report');
                    // Notify the user about the failure
                    mainWindow.webContents.send('backend-progress', 'Warning: Failed to generate Word report. Only Excel report will be available.');
                  }
                })
                .catch(wordErr => {
                  console.error('Error generating Word report:', wordErr);
                  mainWindow.webContents.send('backend-progress', 'Error generating Word report: ' + wordErr.message);
                });
            }

            // Convert to PDF using Python if it doesn't exist
            if (!pdfMatch) {
              convertWithPython(excelPath, pdfPath, 'pdf', 'Changes Only')
                .then(pdfSuccess => {
                  if (pdfSuccess) {
                    console.log(`Generated PDF report: ${pdfPath}`);
                    links.push({ type: 'PDF', path: pdfPath });
                  } else {
                    console.error('Failed to generate PDF report');
                    // Notify the user about the failure
                    mainWindow.webContents.send('backend-progress', 'Warning: Failed to generate PDF report. Only Excel report will be available.');
                  }
                })
                .catch(pdfErr => {
                  console.error('Error generating PDF report:', pdfErr);
                  mainWindow.webContents.send('backend-progress', 'Error generating PDF report: ' + pdfErr.message);
                });
            }

          } catch (err) {
            console.error('Error with reports:', err);
          }
        }

        // Keep these for backward compatibility but don't show to user
        if (csvMatch) links.push({ type: 'CSV', path: csvMatch[1], hidden: true });
        if (jsonMatch) links.push({ type: 'JSON', path: jsonMatch[1], hidden: true });

        console.log('Report links:', links);

        // No need to parse JSON data anymore as we're not using it
        let comparisonData = null;

        resolve({
          success: true,
          links,
          comparisonData
        });
      });

      process.on('error', (err) => {
        console.error(`Failed to start Python process: ${err.message}`);
        activeProcesses.comparison = null;
        resolve({ success: false, error: err.message });
      });
    } catch (error) {
      console.error('Error in process-pdfs handler:', error);
      resolve({ success: false, error: error.message });
    }
  });
});

// Sort PDF handler
ipcMain.handle('sort-pdf', async (event, data) => {
  console.log('sort-pdf called with data:', data);

  return new Promise((resolve, reject) => {
    try {
      const { pdfFile, outputFile, idField } = data;

      // Validate inputs
      if (!pdfFile) {
        console.error('Missing PDF file');
        resolve({ success: false, error: 'Missing PDF file' });
        return;
      }

      if (!outputFile) {
        console.error('Missing output file');
        resolve({ success: false, error: 'Missing output file' });
        return;
      }

      // Use the improved parser
      const pythonScript = app.isPackaged
        ? path.join(process.resourcesPath, 'backend', 'improved_payroll_parser.py')
        : path.join(__dirname, '..', 'backend', 'improved_payroll_parser.py');

      console.log(`Python script path: ${pythonScript}`);

      if (!fs.existsSync(pythonScript)) {
        console.error(`Backend script not found: ${pythonScript}`);
        resolve({ success: false, error: 'Backend script not found.' });
        return;
      }

      console.log(`Running Python script: ${pythonScript}`);
      console.log(`Parameters: ${pdfFile}, ${outputFile}, ${idField || 'employee_id'}`);

      // Build command arguments for the sort command
      const args = [
        pythonScript,
        'sort',
        pdfFile,
        outputFile
      ];

      // Add the ID field if specified
      if (idField) {
        args.push('--id-field', idField);
      }

      // Get the Python executable path
      const pythonPath = getPythonPath();
      console.log(`Using Python executable: ${pythonPath}`);

      const process = spawn(pythonPath, args);

      // Store the active process
      activeProcesses.sort = process;

      let output = '';

      process.stdout.on('data', (data) => {
        const message = data.toString();
        console.log(`Python stdout: ${message}`);
        output += message;
        mainWindow.webContents.send('backend-progress', message);
      });

      process.stderr.on('data', (data) => {
        const errorMsg = data.toString();
        console.error(`Python stderr: ${errorMsg}`);
        mainWindow.webContents.send('backend-progress', 'Error: ' + errorMsg);
      });

      process.on('close', (code) => {
        console.log(`Python process exited with code ${code}`);
        mainWindow.webContents.send('backend-progress', 'Done. Exit code: ' + code);

        // Clear the active process reference
        activeProcesses.sort = null;

        if (code === 0 && fs.existsSync(outputFile)) {
          resolve({
            success: true,
            outputFile: outputFile
          });
        } else {
          resolve({
            success: false,
            error: 'Failed to sort PDF file'
          });
        }
      });

      process.on('error', (err) => {
        console.error(`Failed to start Python process: ${err.message}`);
        activeProcesses.sort = null;
        resolve({ success: false, error: err.message });
      });
    } catch (error) {
      console.error('Error in sort-pdf handler:', error);
      resolve({ success: false, error: error.message });
    }
  });
});

// Build data table handler
ipcMain.handle('build-data-table', async (event, data) => {
  console.log('build-data-table called with data:', data);

  return new Promise((resolve, reject) => {
    try {
      const { pdfFile, outputFile, fields } = data;

      // Validate inputs
      if (!pdfFile) {
        console.error('Missing PDF file');
        resolve({ success: false, error: 'Missing PDF file' });
        return;
      }

      if (!outputFile) {
        console.error('Missing output file');
        resolve({ success: false, error: 'Missing output file' });
        return;
      }

      if (!fields || !fields.length) {
        console.error('Missing fields');
        resolve({ success: false, error: 'Missing fields to include in the table' });
        return;
      }

      // Use the improved parser
      const pythonScript = app.isPackaged
        ? path.join(process.resourcesPath, 'backend', 'improved_payroll_parser.py')
        : path.join(__dirname, '..', 'backend', 'improved_payroll_parser.py');

      console.log(`Python script path: ${pythonScript}`);

      if (!fs.existsSync(pythonScript)) {
        console.error(`Backend script not found: ${pythonScript}`);
        resolve({ success: false, error: 'Backend script not found.' });
        return;
      }

      console.log(`Running Python script: ${pythonScript}`);
      console.log(`Parameters: ${pdfFile}, ${outputFile}, ${fields.join(' ')}`);

      // Build command arguments for the build command
      const args = [
        pythonScript,
        'build',
        pdfFile,
        outputFile,
        '--fields',
        ...fields
      ];

      // Get the Python executable path
      const pythonPath = getPythonPath();
      console.log(`Using Python executable: ${pythonPath}`);

      const process = spawn(pythonPath, args);

      // Store the active process
      activeProcesses.build = process;

      let output = '';

      process.stdout.on('data', (data) => {
        const message = data.toString();
        console.log(`Python stdout: ${message}`);
        output += message;
        mainWindow.webContents.send('backend-progress', message);
      });

      process.stderr.on('data', (data) => {
        const errorMsg = data.toString();
        console.error(`Python stderr: ${errorMsg}`);
        mainWindow.webContents.send('backend-progress', 'Error: ' + errorMsg);
      });

      process.on('close', (code) => {
        console.log(`Python process exited with code ${code}`);
        mainWindow.webContents.send('backend-progress', 'Done. Exit code: ' + code);

        // Clear the active process reference
        activeProcesses.build = null;

        if (code === 0 && fs.existsSync(outputFile)) {
          resolve({
            success: true,
            outputFile: outputFile
          });
        } else {
          resolve({
            success: false,
            error: 'Failed to build data table'
          });
        }
      });

      process.on('error', (err) => {
        console.error(`Failed to start Python process: ${err.message}`);
        activeProcesses.build = null;
        resolve({ success: false, error: err.message });
      });
    } catch (error) {
      console.error('Error in build-data-table handler:', error);
      resolve({ success: false, error: error.message });
    }
  });
});

// Bank Adviser handler
ipcMain.handle('process-bank-advice', async (event, data) => {
  console.log('process-bank-advice called with data:', data);

  return new Promise((resolve, reject) => {
    try {
      const { bankAdviceFile, payslipFiles, allowancesFile, awardsFile, columnMapping, excelSettings } = data;

      // Validate inputs
      if (!bankAdviceFile) {
        console.error('Missing Bank Advice file');
        resolve({ success: false, error: 'Missing Bank Advice file' });
        return;
      }

      if (!payslipFiles || payslipFiles.length === 0) {
        console.error('Missing Payslip files');
        resolve({ success: false, error: 'Missing Payslip files' });
        return;
      }

      // Check allowances and awards files - now optional
      if (allowancesFile && !fs.existsSync(allowancesFile)) {
        console.warn('Invalid ALL ALLOWANCES file path provided, will be treated as not provided');
        allowancesFile = ''; // Reset to empty string if file doesn't exist
      }

      if (awardsFile && !fs.existsSync(awardsFile)) {
        console.warn('Invalid AWARDS & GRANTS file path provided, will be treated as not provided');
        awardsFile = ''; // Reset to empty string if file doesn't exist
      }

      // Log the status of optional files
      if (!allowancesFile) {
        console.log('No ALL ALLOWANCES file provided. Allowances will be treated as zero.');
      }

      if (!awardsFile) {
        console.log('No AWARDS & GRANTS file provided. Awards will be treated as zero.');
      }

      if (!columnMapping) {
        console.error('Missing column mapping');
        resolve({ success: false, error: 'Missing column mapping' });
        return;
      }

      // Create a temporary directory for payslip files
      const tempDir = path.join(app.getPath('temp'), 'payroll-auditor', 'payslips');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Log the path for debugging
      console.log(`Temporary directory: ${tempDir}`);

      // Copy payslip files to the temporary directory
      console.log(`Copying ${payslipFiles.length} payslip files to temporary directory...`);

      // Clear the temporary directory first
      const existingFiles = fs.readdirSync(tempDir);
      for (const file of existingFiles) {
        fs.unlinkSync(path.join(tempDir, file));
      }

      // Copy the payslip files
      let payslipFilesCopied = 0;
      if (Array.isArray(payslipFiles)) {
        for (const file of payslipFiles) {
          try {
            const fileName = path.basename(file);
            const destPath = path.join(tempDir, fileName);
            fs.copyFileSync(file, destPath);
            payslipFilesCopied++;
            console.log(`Copied payslip file: ${fileName}`);
          } catch (error) {
            console.error(`Error copying payslip file ${file}: ${error.message}`);
          }
        }
      } else if (typeof payslipFiles === 'string') {
        try {
          const fileName = path.basename(payslipFiles);
          const destPath = path.join(tempDir, fileName);
          fs.copyFileSync(payslipFiles, destPath);
          payslipFilesCopied++;
          console.log(`Copied payslip file: ${fileName}`);
        } catch (error) {
          console.error(`Error copying payslip file ${payslipFiles}: ${error.message}`);
        }
      }

      console.log(`Copied ${payslipFilesCopied} payslip files to temporary directory`);

      // Get the Python executable path
      const pythonPath = getPythonPath();

      // Get the script path
      const scriptPath = path.join(__dirname, 'backend-dist', 'bank_adviser.py');

      // Check if the script exists
      if (!fs.existsSync(scriptPath)) {
        console.error(`Bank Adviser script not found: ${scriptPath}`);
        resolve({ success: false, error: 'Bank Adviser script not found' });
        return;
      }

      // Convert column mapping to JSON string
      const columnMappingJson = JSON.stringify(columnMapping);

      // Convert Excel settings to JSON string
      const excelSettingsJson = JSON.stringify(excelSettings || {
        headerRow: 3, // Default to row 4 (0-based index)
        dataStartRow: 4, // Default to row 5 (0-based index)
        firstColumn: 'A'
      });

      // Run the script to process the bank advice
      console.log('Processing bank advice with the following arguments:');
      console.log('Bank Advice File:', bankAdviceFile);
      console.log('Payslip Files Directory:', tempDir);
      console.log('Allowances File:', allowancesFile);
      console.log('Awards File:', awardsFile);
      console.log('Column Mapping:', columnMappingJson);
      console.log('Excel Settings:', excelSettingsJson);

      const args = [
        scriptPath,
        bankAdviceFile,
        tempDir,  // We'll use a temporary directory for now
        allowancesFile,
        awardsFile,
        columnMappingJson,
        excelSettingsJson
      ];

      console.log(`Running Python script: ${pythonPath} ${args.join(' ')}`);

      const process = spawn(pythonPath, args);

      // Store the active process
      activeProcesses.bankAdviser = process;

      let output = '';

      process.stdout.on('data', (data) => {
        const message = data.toString();
        console.log(`Python stdout: ${message}`);
        output += message;
        mainWindow.webContents.send('backend-progress', message);
      });

      let errorOutput = '';

      process.stderr.on('data', (data) => {
        const errorMsg = data.toString();
        errorOutput += errorMsg;
        console.error(`Python stderr: ${errorMsg}`);
        mainWindow.webContents.send('backend-progress', 'Error: ' + errorMsg);
      });

      process.on('close', (code) => {
        console.log(`Python process exited with code ${code}`);
        mainWindow.webContents.send('backend-progress', 'Done. Exit code: ' + code);

        // Clear the active process reference
        activeProcesses.bankAdviser = null;

        // Log the full output and error output for debugging
        console.log('Full output:', output);
        console.log('Full error output:', errorOutput);

        if (code === 0) {
          try {
            // Try to extract JSON from the output
            // Look for a JSON object in the output - more robust pattern
            // This will find the last complete JSON object in the output
            const jsonMatches = output.match(/(\{[\s\S]*?\})/g);

            if (jsonMatches && jsonMatches.length > 0) {
              // Get the last match which is likely the final result
              const jsonStr = jsonMatches[jsonMatches.length - 1];
              console.log('Extracted JSON string:', jsonStr);

              try {
                // Parse the JSON
                const results = JSON.parse(jsonStr);

                // Check if the results object has the expected structure
                console.log('Checking results structure:', JSON.stringify(results));

                // Make sure banks array exists
                if (!results.banks) {
                  console.error('Missing banks array in results:', results);
                  results.banks = [];
                }

                // Make sure summary object exists
                if (!results.summary) {
                  console.error('Missing summary object in results:', results);
                  results.summary = {
                    totalEmployees: 0,
                    preAudited: 0,
                    notPreAudited: 0,
                    totalNetPay: 0.0,
                    totalAllowances: 0.0,
                    totalAwards: 0.0,
                    grandTotal: 0.0
                  };
                }

                // Check if the results object has the expected structure now
                if (results.banks && results.summary) {
                  // Calculate percentages for display
                  if (results.summary.totalEmployees > 0) {
                    results.summary.preAuditedPercent = Math.round((results.summary.preAudited / results.summary.totalEmployees) * 100);
                    results.summary.notPreAuditedPercent = Math.round((results.summary.notPreAudited / results.summary.totalEmployees) * 100);
                  } else {
                    results.summary.preAuditedPercent = 0;
                    results.summary.notPreAuditedPercent = 0;
                  }

                  resolve({
                    success: true,
                    message: 'Bank Advice processed successfully',
                    data: results
                  });
                } else if (results.error) {
                  // Handle error response from Python
                  console.error('Error in Python results:', results.error);
                  resolve({
                    success: false,
                    error: results.error
                  });
                } else {
                  console.error('Invalid results structure:', results);
                  resolve({
                    success: false,
                    error: 'Invalid results structure from Bank Adviser'
                  });
                }
              } catch (parseError) {
                console.error('Error parsing JSON string:', parseError);
                console.error('JSON string:', jsonStr);

                // Try to find another valid JSON object
                for (let i = jsonMatches.length - 2; i >= 0; i--) {
                  try {
                    const alternativeJson = jsonMatches[i];
                    const alternativeResults = JSON.parse(alternativeJson);
                    console.log('Found alternative valid JSON:', alternativeResults);

                    if (alternativeResults.banks && alternativeResults.summary) {
                      resolve({
                        success: true,
                        message: 'Bank Advice processed successfully (alternative JSON)',
                        data: alternativeResults
                      });
                      return;
                    }
                  } catch (e) {
                    console.error(`Failed to parse alternative JSON at index ${i}:`, e);
                  }
                }

                resolve({
                  success: false,
                  error: 'Error parsing Bank Adviser output JSON'
                });
              }
            } else {
              console.error('No JSON object found in output');
              console.error('Output:', output);
              resolve({ success: false, error: 'No JSON object found in Bank Adviser output' });
            }
          } catch (error) {
            console.error('Error processing Bank Adviser output:', error);
            console.error('Output:', output);
            resolve({ success: false, error: 'Error processing Bank Adviser output' });
          }
        } else {
          console.error(`Bank Adviser process exited with code ${code}`);
          console.error('Error output:', errorOutput);

          // Check if there's any error output
          let errorMessage = 'Failed to process Bank Advice. Check the terminal for details.';

          // Try to extract error JSON from the output or error output
          try {
            // First try to find JSON in error output
            let jsonMatch = errorOutput.match(/\{[\s\S]*\}/);

            // If not found in error output, try in standard output
            if (!jsonMatch) {
              jsonMatch = output.match(/\{[\s\S]*\}/);
            }

            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              console.log('Extracted error JSON string:', jsonStr);

              // Parse the JSON
              const errorJson = JSON.parse(jsonStr);

              if (errorJson.error) {
                errorMessage = errorJson.error;
              }
            } else if (errorOutput) {
              // If no JSON found but we have error output, use that
              errorMessage = errorOutput.trim();
            }
          } catch (parseError) {
            console.error('Error parsing error output as JSON:', parseError);
            // If we can't parse JSON but have error output, use that
            if (errorOutput) {
              errorMessage = errorOutput.trim();
            }
          }

          console.error('Final error message:', errorMessage);

          resolve({
            success: false,
            error: errorMessage
          });
        }
      });

      process.on('error', (err) => {
        console.error(`Failed to start Python process: ${err.message}`);
        activeProcesses.bankAdviser = null;
        resolve({ success: false, error: err.message });
      });
    } catch (error) {
      console.error('Error in process-bank-advice handler:', error);
      resolve({ success: false, error: error.message });
    }
  });
});

// Get desktop path
ipcMain.handle('get-desktop-path', async (event) => {
  return app.getPath('desktop');
});

// Check if path exists
ipcMain.handle('check-path-exists', async (event, path) => {
  return fs.existsSync(path);
});

// Export Bank Adviser results
ipcMain.handle('export-bank-adviser', async (event, data) => {
  console.log('export-bank-adviser called with data:', data);

  try {
    // Validate the data structure
    if (!data || !data.banks || !Array.isArray(data.banks)) {
      console.error('Invalid data structure: missing or invalid banks array');
      return {
        success: false,
        error: 'Invalid data structure: missing or invalid banks array. Please process the data first.'
      };
    }

    if (!data.summary || typeof data.summary !== 'object') {
      console.error('Invalid data structure: missing or invalid summary object');
      return {
        success: false,
        error: 'Invalid data structure: missing or invalid summary object. Please process the data first.'
      };
    }

    // Show file dialog to save Excel file
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'Export Bank Adviser Results',
      defaultPath: 'Bank_Adviser_Results.xlsx',
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (result.canceled || !result.filePath) {
      return { success: false, error: 'Export cancelled' };
    }

    const filePath = result.filePath;

    // Create a temporary JSON file with the data
    const tempJsonPath = path.join(app.getPath('temp'), 'bank_adviser_export.json');
    console.log('Writing data to temporary JSON file:', tempJsonPath);
    console.log('Data structure:', JSON.stringify(data, null, 2));
    fs.writeFileSync(tempJsonPath, JSON.stringify(data));

    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Create a simple Python script to export the data to Excel
    const exportScript = `
import json
import pandas as pd
import sys
import os
import re

# Load the data from the JSON file
with open('${tempJsonPath.replace(/\\/g, '\\\\')}', 'r') as f:
    data = json.load(f)

# Create a new Excel writer
writer = pd.ExcelWriter('${filePath.replace(/\\/g, '\\\\')}', engine='xlsxwriter')

# Get the xlsxwriter workbook object
workbook = writer.book

# Create a DataFrame for each bank
for bank_idx, bank in enumerate(data['banks']):
    # Extract employee data
    employees = bank['employees']

    if not employees:
        print(f"Warning: No employees found for bank {bank.get('bank', f'Bank {bank_idx+1}')}")
        continue

    # Create a DataFrame
    df = pd.DataFrame(employees)

    # Check if DataFrame is empty
    if df.empty:
        print(f"Warning: Empty DataFrame for bank {bank.get('bank', f'Bank {bank_idx+1}')}")
        continue

    # Print the columns for debugging
    print(f"Columns in DataFrame for bank {bank.get('bank', f'Bank {bank_idx+1}')}:")
    print(df.columns.tolist())

    # Rename columns to match the standard format
    df = df.rename(columns={
        'employeeNo': 'EMPLOYEE NO.',
        'employeeName': 'EMPLOYEE NAME',
        'accountNo': 'ACCOUNT NO.',
        'netPay': 'NET PAY',
        'allowances': 'ALL ALLOWANCES',
        'awards': 'AWARDS & GRANTS',
        'total': 'TOTAL',
        'branch': 'BRANCH',
        'verified': 'VERIFIED',
        'breakdown': 'BREAKDOWN'
    })

    # Add a REMARKS column
    df['REMARKS'] = df['VERIFIED'].apply(lambda x: 'PRE-AUDITED' if x else 'NOT-PREAUDITED')

    # Drop the VERIFIED column
    df = df.drop(columns=['VERIFIED'])

    # Write the DataFrame to the Excel file
    bank_name = bank.get('bank', f'BANK {chr(65 + bank_idx)}')
    # Sanitize the bank name for use as a sheet name (max 31 chars, no special chars)
    sheet_name = re.sub(r'[\\/*?:\[\]]', '', bank_name)[:31]
    print(f"Writing sheet: {sheet_name} with {len(df)} rows")
    df.to_excel(writer, sheet_name=sheet_name, index=False)

    # Get the worksheet object
    worksheet = writer.sheets[sheet_name]

    # Add formats for the worksheet
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'top',
        'fg_color': '#D7E4BC',
        'border': 1
    })

    # Write the column headers with the header format
    for col_num, value in enumerate(df.columns.values):
        worksheet.write(0, col_num, value, header_format)

    # Set column widths
    worksheet.set_column('A:A', 15)  # EMPLOYEE NO.
    worksheet.set_column('B:B', 25)  # EMPLOYEE NAME
    worksheet.set_column('C:C', 15)  # ACCOUNT NO.
    worksheet.set_column('D:D', 12)  # NET PAY
    worksheet.set_column('E:E', 15)  # ALL ALLOWANCES
    worksheet.set_column('F:F', 15)  # AWARDS & GRANTS
    worksheet.set_column('G:G', 12)  # TOTAL
    worksheet.set_column('H:H', 15)  # BRANCH
    worksheet.set_column('I:I', 15)  # REMARKS
    worksheet.set_column('J:J', 40)  # BREAKDOWN

# Create a summary sheet
summary = data.get('summary', {})
summary_df = pd.DataFrame({
    'Metric': [
        'Total Employees',
        'Pre-Audited',
        'Not Pre-Audited',
        'Total Net Pay',
        'Total Allowances',
        'Total Awards',
        'Grand Total'
    ],
    'Value': [
        summary.get('totalEmployees', 0),
        summary.get('preAudited', 0),
        summary.get('notPreAudited', 0),
        summary.get('totalNetPay', 0),
        summary.get('totalAllowances', 0),
        summary.get('totalAwards', 0),
        summary.get('grandTotal', 0)
    ]
})

# Write the summary to the Excel file
summary_df.to_excel(writer, sheet_name='Summary', index=False)

# Get the worksheet object for the summary sheet
worksheet = writer.sheets['Summary']

# Add formats for the summary sheet (using the workbook object defined earlier)
header_format = workbook.add_format({
    'bold': True,
    'text_wrap': True,
    'valign': 'top',
    'fg_color': '#D7E4BC',
    'border': 1
})

# Write the column headers with the header format
for col_num, value in enumerate(summary_df.columns.values):
    worksheet.write(0, col_num, value, header_format)

# Set column widths for the summary sheet
worksheet.set_column('A:A', 20)  # Metric
worksheet.set_column('B:B', 15)  # Value

# Save the Excel file
writer.close()

print("Exported data to " + os.path.basename('${filePath.replace(/\\/g, '\\\\')}') + " successfully")
`;

    // Write the export script to a temporary file
    const tempScriptPath = path.join(app.getPath('temp'), 'bank_adviser_export.py');
    fs.writeFileSync(tempScriptPath, exportScript);

    // Run the export script
    const process = spawn(pythonPath, [tempScriptPath]);

    return new Promise((resolve, reject) => {
      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        // Clean up temporary files
        try {
          fs.unlinkSync(tempJsonPath);
          fs.unlinkSync(tempScriptPath);
        } catch (error) {
          console.error('Error cleaning up temporary files:', error);
        }

        if (code === 0) {
          resolve({
            success: true,
            message: 'Bank Adviser results exported successfully',
            filePath: filePath
          });
        } else {
          console.error(`Export process exited with code ${code}`);
          console.error(`Error output: ${errorOutput}`);
          resolve({
            success: false,
            error: `Export failed with code ${code}: ${errorOutput}`
          });
        }
      });

      process.on('error', (err) => {
        console.error('Failed to start export process:', err);
        resolve({
          success: false,
          error: `Failed to start export process: ${err.message}`
        });
      });
    });
  } catch (error) {
    console.error('Error in export-bank-adviser handler:', error);
    return { success: false, error: error.message };
  }
});

// Stop process handler
ipcMain.handle('stop-process', async (event, processType) => {
  console.log(`stop-process called for process type: ${processType}`);

  const process = activeProcesses[processType];
  if (process) {
    try {
      console.log(`Killing process: ${processType}`);
      process.kill();
      activeProcesses[processType] = null;
      mainWindow.webContents.send('backend-progress', `Process ${processType} stopped by user.`);
      return { success: true };
    } catch (error) {
      console.error(`Error stopping process ${processType}:`, error);
      return { success: false, error: error.message };
    }
  } else {
    console.log(`No active process found for type: ${processType}`);
    return { success: false, error: 'No active process found' };
  }
});

// Open file handler
ipcMain.handle('open-file', async (event, filePath) => {
  console.log(`open-file called for ${filePath}`);

  try {
    // Check if it's a URL
    if (filePath.startsWith('http')) {
      // Open URL in default browser
      const { shell } = require('electron');
      await shell.openExternal(filePath);
      return { success: true };
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return { success: false, error: 'File not found' };
    }

    // Special handling for Word documents
    if (filePath.toLowerCase().endsWith('.docx') || filePath.toLowerCase().endsWith('.doc')) {
      console.log('Opening Word document with special handling');

      try {
        // Use the child_process module to open Word documents
        const { exec } = require('child_process');

        // Use the Windows command to open the file with its associated application
        // Wrap in a Promise to properly handle errors
        await new Promise((resolve, reject) => {
          exec(`start "" "${filePath}"`, (error) => {
            if (error) {
              console.error(`Error opening Word document: ${error}`);
              reject(error);
            } else {
              resolve();
            }
          });
        });

        console.log(`Successfully opened Word document: ${filePath}`);
        return { success: true };
      } catch (error) {
        console.error(`Error opening Word document: ${error}`);
        return { success: false, error: error.message };
      }
    } else {
      // For other file types, use the standard shell.openPath
      const { shell } = require('electron');
      await shell.openPath(filePath);
      return { success: true };
    }
  } catch (error) {
    console.error('Error opening file:', error);
    return { success: false, error: error.message };
  }
});

// Check if a file exists
ipcMain.handle('check-file-exists', async (event, filePath) => {
  try {
    console.log(`Checking if file exists: ${filePath}`);
    const exists = fs.existsSync(filePath);
    console.log(`File exists: ${exists}`);
    return exists;
  } catch (error) {
    console.error(`Error checking if file exists: ${error.message}`);
    return false;
  }
});

// List files in a directory
ipcMain.handle('list-files', async (event, dirPath) => {
  try {
    console.log(`Listing files in directory: ${dirPath}`);
    const files = await fs.promises.readdir(dirPath);
    console.log(`Found ${files.length} files`);
    return files;
  } catch (error) {
    console.error(`Error listing files in ${dirPath}: ${error.message}`);
    return [];
  }
});

// Get the application directory path
ipcMain.handle('get-app-dir', async (event) => {
  try {
    console.log('Getting application directory path');
    const appDir = app.isPackaged
      ? path.dirname(app.getPath('exe'))
      : __dirname;
    console.log(`Application directory: ${appDir}`);
    return appDir;
  } catch (error) {
    console.error(`Error getting application directory: ${error.message}`);
    return __dirname;
  }
});

// Show save file dialog
ipcMain.handle('save-file-dialog', async (event, options) => {
  try {
    console.log('Showing save file dialog with options:', options);
    const result = await dialog.showSaveDialog(BrowserWindow.getFocusedWindow(), {
      title: 'Save File',
      defaultPath: options.defaultPath || '',
      filters: options.filters || [{ name: 'All Files', extensions: ['*'] }],
      properties: ['createDirectory']
    });

    console.log('Save dialog result:', result);
    if (!result.canceled) {
      return result.filePath;
    }
    return null;
  } catch (error) {
    console.error(`Error showing save file dialog: ${error.message}`);
    return null;
  }
});

// Copy file
ipcMain.handle('copy-file', async (event, source, destination) => {
  try {
    console.log(`Copying file from ${source} to ${destination}`);
    await fs.promises.copyFile(source, destination);
    console.log('File copied successfully');
    return true;
  } catch (error) {
    console.error(`Error copying file: ${error.message}`);
    return false;
  }
});

// Delete file
ipcMain.handle('delete-file', async (event, filePath) => {
  try {
    console.log(`Deleting file: ${filePath}`);
    await fs.promises.unlink(filePath);
    console.log('File deleted successfully');
    return true;
  } catch (error) {
    console.error(`Error deleting file: ${error.message}`);
    return false;
  }
});

// Get Excel columns
ipcMain.handle('get-excel-columns', async (event, filePath, headerRow = undefined, firstColumn = undefined) => {
  try {
    console.log(`Getting Excel columns from: ${filePath} (Header Row: ${headerRow !== undefined ? headerRow : 'auto'}, First Column: ${firstColumn || 'A'})`);

    // Get the Python executable path
    const pythonPath = getPythonPath();

    // Create a simple Python script to read Excel columns
    const tempScriptPath = path.join(app.getPath('temp'), 'read_excel_columns.py');
    const scriptContent = `
import pandas as pd
import sys
import json
import os

try:
    # Use a raw string to avoid backslash issues
    file_path = r'${filePath.replace(/\\/g, '\\\\')}'
    print(f"Reading Excel file: {os.path.basename(file_path)}")

    # Get the specified header row and first column
    specified_header_row = ${headerRow !== undefined ? headerRow : 'None'}
    specified_first_column = "${firstColumn || 'A'}"

    print(f"Using specified header row: {specified_header_row if specified_header_row is not None else 'auto'}")
    print(f"Using specified first column: {specified_first_column}")

    # Function to get column index from letter (A=0, B=1, etc.)
    def col_letter_to_index(letter):
        letter = letter.upper()
        result = 0
        for i, char in enumerate(reversed(letter)):
            result += (ord(char) - ord('A') + 1) * (26 ** i)
        return result - 1  # 0-based index

    # Calculate the first column index
    first_col_index = col_letter_to_index(specified_first_column)
    print(f"First column index: {first_col_index}")

    # Try to read the Excel file with the specified settings first
    columns = []

    if specified_header_row is not None:
        try:
            print(f"Trying to read Excel with specified header_row={specified_header_row}")

            # Try with different engines
            for engine in [None, 'openpyxl', 'xlrd']:
                try:
                    engine_name = engine if engine else 'default'
                    print(f"Trying with {engine_name} engine")

                    # Read the Excel file
                    kwargs = {'header': specified_header_row}
                    if engine:
                        kwargs['engine'] = engine

                    df = pd.read_excel(file_path, **kwargs)

                    # If first column is specified, slice the dataframe
                    if first_col_index > 0:
                        df = df.iloc[:, first_col_index:]

                    # Get the column names
                    cols = df.columns.tolist()

                    # Check if we got meaningful column names
                    unnamed_count = sum(1 for col in cols if 'unnamed' in str(col).lower())
                    if unnamed_count < len(cols) / 2:  # Less than half are unnamed
                        columns = cols
                        print(f"Found good columns with {engine_name} engine at header_row={specified_header_row}")
                        break
                except Exception as e:
                    print(f"Error with {engine_name} engine at header_row={specified_header_row}: {str(e)}")

            # If we found columns, we're done
            if columns:
                print(f"Successfully read columns with specified settings")
            else:
                print(f"Failed to read columns with specified settings, will try auto-detection")
        except Exception as e:
            print(f"Error trying specified header_row={specified_header_row}: {str(e)}")

    # If we still don't have columns, try auto-detection
    if not columns:
        # Try header rows from 0 to 5 (1st row to 6th row in Excel)
        for header_row in range(6):
            try:
                print(f"Auto-detecting: Trying to read Excel with header_row={header_row}")

                # Try with different engines
                for engine in [None, 'openpyxl', 'xlrd']:
                    try:
                        engine_name = engine if engine else 'default'
                        print(f"Trying with {engine_name} engine")

                        # Read the Excel file
                        kwargs = {'header': header_row}
                        if engine:
                            kwargs['engine'] = engine

                        df = pd.read_excel(file_path, **kwargs)

                        # If first column is specified, slice the dataframe
                        if first_col_index > 0:
                            df = df.iloc[:, first_col_index:]

                        # Get the column names
                        cols = df.columns.tolist()

                        # Check if we got meaningful column names
                        unnamed_count = sum(1 for col in cols if 'unnamed' in str(col).lower())
                        if unnamed_count < len(cols) / 2:  # Less than half are unnamed
                            columns = cols
                            print(f"Found good columns with {engine_name} engine at header_row={header_row}")
                            break
                    except Exception as e:
                        print(f"Error with {engine_name} engine at header_row={header_row}: {str(e)}")

                # If we found columns, we're done
                if columns:
                    break
            except Exception as e:
                print(f"Error trying header_row={header_row}: {str(e)}")

    # If we still don't have columns, try one more approach - read the first few rows and look for header-like content
    if not columns:
        try:
            print("Trying to read Excel file without specifying header row")

            # Read the first 10 rows
            df = pd.read_excel(file_path, header=None, nrows=10)

            # If first column is specified, slice the dataframe
            if first_col_index > 0:
                df = df.iloc[:, first_col_index:]

            # Enhanced header detection - look for rows that might contain headers
            # Print the first few rows for debugging
            print("First 5 rows of the Excel file:")
            for i in range(min(5, len(df))):
                row_values = [str(val).strip() if pd.notna(val) else "" for val in df.iloc[i]]
                print(f"Row {i}: {row_values}")

            # Score each row based on how likely it is to be a header row
            header_scores = []
            for i in range(min(10, len(df))):
                row = df.iloc[i]
                row_values = [str(val).strip() if pd.notna(val) else "" for val in row]

                # Count text values (header rows typically have text)
                text_count = sum(1 for val in row_values if val and not val.replace('.', '').isdigit())

                # Check for common header keywords
                keyword_count = sum(1 for val in row_values if any(kw in val.upper() for kw in
                                                                ['NO', 'NAME', 'EMPLOYEE', 'ACCOUNT', 'SALARY', 'TOTAL', 'BRANCH']))

                # Check for consistent formatting (headers often have consistent capitalization)
                caps_count = sum(1 for val in row_values if val and val.isupper())
                title_count = sum(1 for val in row_values if val and val.istitle())

                # Calculate a score for this row
                score = (text_count / max(1, len(row))) * 0.5  # Weight for text values
                score += (keyword_count / max(1, len(row))) * 0.3  # Weight for keywords
                score += (max(caps_count, title_count) / max(1, len(row))) * 0.2  # Weight for formatting

                header_scores.append((i, score, row_values))
                print(f"Row {i} header score: {score:.2f}")

            # Sort by score and use the highest scoring row as headers
            header_scores.sort(key=lambda x: x[1], reverse=True)

            if header_scores:
                best_row_idx, best_score, best_row_values = header_scores[0]
                if best_score > 0.3:  # Threshold for a good header row
                    columns = [val if val else f"Column_{j+1}" for j, val in enumerate(best_row_values)]
                    print(f"Using row {best_row_idx} as headers (score: {best_score:.2f}): {columns}")
                else:
                    print(f"No row had a high enough header score, using first non-empty row")
                    # Find first non-empty row
                    for i, score, row_values in header_scores:
                        if any(val for val in row_values):
                            columns = [val if val else f"Column_{j+1}" for j, val in enumerate(row_values)]
                            print(f"Using row {i} as headers (first non-empty): {columns}")
                            break
        except Exception as e:
            print(f"Error trying to find headers in first 10 rows: {str(e)}")

    # If we still don't have columns, create generic ones based on the number of columns in the file
    if not columns:
        try:
            # Just read the first row to determine number of columns
            df = pd.read_excel(file_path, header=None, nrows=1)

            # If first column is specified, slice the dataframe
            if first_col_index > 0:
                df = df.iloc[:, first_col_index:]

            num_cols = len(df.columns)
            columns = [f"Column_{i+1}" for i in range(num_cols)]
            print(f"Created generic column names for {num_cols} columns")
        except Exception as e:
            print(f"Error creating generic column names: {str(e)}")
            # Last resort - use default column names
            columns = ['EMP_NO', 'NAME', 'ACCOUNT', 'NETPAY', 'LV', 'LSTG', 'TOTAL', 'BRANCH']
            print("Using default column names as last resort")

    # Convert any non-string columns to strings
    columns = [str(col).strip() if pd.notna(col) else f"Column_{i+1}" for i, col in enumerate(columns)]

    # Replace any empty or whitespace-only column names
    columns = [col if col.strip() else f"Column_{i+1}" for i, col in enumerate(columns)]

    print(f"Found {len(columns)} columns: {', '.join(columns)}")

    # Print the columns as JSON
    print(json.dumps(columns))
except Exception as e:
    print(f"Error: {str(e)}", file=sys.stderr)
    sys.exit(1)
`;

    // Write the script to a temporary file
    fs.writeFileSync(tempScriptPath, scriptContent);

    // Run the script
    return new Promise((resolve, reject) => {
      const process = spawn(pythonPath, [tempScriptPath]);
      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;
        console.log(`Excel column extraction stdout: ${dataStr.trim()}`);
      });

      process.stderr.on('data', (data) => {
        const dataStr = data.toString();
        errorOutput += dataStr;
        console.error(`Excel column extraction stderr: ${dataStr.trim()}`);
      });

      process.on('close', (code) => {
        // Clean up the temporary file
        try {
          fs.unlinkSync(tempScriptPath);
        } catch (error) {
          console.error('Error cleaning up temporary file:', error);
        }

        if (code === 0) {
          try {
            // Extract the JSON part from the output
            // The output might contain debug prints before the JSON
            const jsonMatch = output.match(/\[.*\]/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              const columns = JSON.parse(jsonStr);
              console.log(`Found ${columns.length} columns in Excel file`);

              if (columns.length === 0) {
                console.error('No columns found in Excel file');
                resolve(['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']); // Fallback
              } else {
                resolve(columns);
              }
            } else {
              console.error('No JSON found in output');
              console.error('Raw output:', output);
              resolve(['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']); // Fallback
            }
          } catch (error) {
            console.error('Error parsing Excel columns:', error);
            console.error('Raw output:', output);
            resolve(['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']); // Fallback
          }
        } else {
          console.error(`Excel column extraction process exited with code ${code}`);
          console.error(`Error output: ${errorOutput}`);
          resolve(['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']); // Fallback
        }
      });

      process.on('error', (err) => {
        console.error('Failed to start Excel column extraction process:', err);
        resolve(['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']); // Fallback
      });
    });
  } catch (error) {
    console.error(`Error getting Excel columns: ${error.message}`);
    return ['No', 'Employee Name', 'Account No', 'Net Salary', 'LV ', 'TOTAL', 'Branch']; // Fallback
  }
});

// Function to analyze Excel file structure and detect header row, data start row, etc.
ipcMain.handle('analyze-excel-structure', async (event, filePath) => {
  try {
    console.log(`Analyzing Excel file structure: ${filePath}`);

    // Create a temporary Python script to analyze the Excel file
    const tempScriptPath = path.join(app.getPath('temp'), `analyze_excel_${Date.now()}.py`);

    // Get the Python path
    const pythonPath = getPythonPath();

    // Create the Python script content
    const scriptContent = `
import pandas as pd
import numpy as np
import sys
import json
import re

try:
    # Use a raw string to avoid backslash issues
    file_path = r'${filePath.replace(/\\/g, '\\\\')}'
    # Read the first 20 rows of the Excel file
    df = pd.read_excel(file_path, header=None, nrows=20)

    # Print the first few rows for debugging
    print("First 5 rows of the Excel file:")
    for i in range(min(5, len(df))):
        row_values = [str(val).strip() if pd.notna(val) else "" for val in df.iloc[i]]
        print(f"Row {i}: {row_values}")

    # Analyze each row to determine if it's a header row
    header_scores = []
    for i in range(min(15, len(df))):
        row = df.iloc[i]
        row_values = [str(val).strip() if pd.notna(val) else "" for val in row]

        # Count text values (header rows typically have text)
        text_count = sum(1 for val in row_values if val and not val.replace('.', '').isdigit())

        # Check for common header keywords
        keyword_count = sum(1 for val in row_values if any(kw in val.upper() for kw in
                                                        ['NO', 'NAME', 'EMPLOYEE', 'ACCOUNT', 'SALARY', 'TOTAL', 'BRANCH']))

        # Check for consistent formatting (headers often have consistent capitalization)
        caps_count = sum(1 for val in row_values if val and val.isupper())
        title_count = sum(1 for val in row_values if val and val.istitle())

        # Calculate a score for this row
        score = (text_count / max(1, len(row))) * 0.5  # Weight for text values
        score += (keyword_count / max(1, len(row))) * 0.3  # Weight for keywords
        score += (max(caps_count, title_count) / max(1, len(row))) * 0.2  # Weight for formatting

        header_scores.append((i, score, row_values))
        print(f"Row {i} header score: {score:.2f}")

    # Sort by score and use the highest scoring row as the header row
    header_scores.sort(key=lambda x: x[1], reverse=True)

    # Default values
    header_row = 4  # 1-based index for UI
    data_start_row = 5  # 1-based index for UI
    first_column = 'A'

    if header_scores:
        best_row_idx, best_score, best_row_values = header_scores[0]
        if best_score > 0.3:  # Threshold for a good header row
            header_row = best_row_idx + 1  # Convert to 1-based index for UI
            data_start_row = header_row + 1  # Data starts after header
            print(f"Detected header row: {header_row} (score: {best_score:.2f})")

            # Determine the first column with data
            for j, val in enumerate(best_row_values):
                if val.strip():
                    first_column = chr(65 + j)  # Convert to column letter (A, B, C, etc.)
                    print(f"Detected first column: {first_column}")
                    break

    # Create the result object
    result = {
        "headerRow": header_row,
        "dataStartRow": data_start_row,
        "firstColumn": first_column
    }

    # Print the result as JSON
    print(json.dumps(result))
except Exception as e:
    print(f"Error analyzing Excel structure: {str(e)}", file=sys.stderr)
    # Return default values in case of error
    print(json.dumps({"headerRow": 4, "dataStartRow": 5, "firstColumn": "A"}))
    sys.exit(1)
`;

    // Write the script to a temporary file
    fs.writeFileSync(tempScriptPath, scriptContent);

    // Run the script
    return new Promise((resolve, reject) => {
      const process = spawn(pythonPath, [tempScriptPath]);
      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;
        console.log(`Excel structure analysis stdout: ${dataStr.trim()}`);
      });

      process.stderr.on('data', (data) => {
        const dataStr = data.toString();
        errorOutput += dataStr;
        console.error(`Excel structure analysis stderr: ${dataStr.trim()}`);
      });

      process.on('close', (code) => {
        // Clean up the temporary file
        try {
          fs.unlinkSync(tempScriptPath);
        } catch (error) {
          console.error('Error cleaning up temporary file:', error);
        }

        if (code === 0) {
          try {
            // Extract the JSON part from the output
            const jsonMatch = output.match(/\{.*\}/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              const structure = JSON.parse(jsonStr);
              console.log(`Detected Excel structure:`, structure);
              resolve(structure);
            } else {
              console.error('No JSON found in output');
              console.error('Raw output:', output);
              resolve({ headerRow: 4, dataStartRow: 5, firstColumn: 'A' }); // Default values
            }
          } catch (error) {
            console.error('Error parsing Excel structure:', error);
            console.error('Raw output:', output);
            resolve({ headerRow: 4, dataStartRow: 5, firstColumn: 'A' }); // Default values
          }
        } else {
          console.error(`Excel structure analysis process exited with code ${code}`);
          console.error(`Error output: ${errorOutput}`);
          resolve({ headerRow: 4, dataStartRow: 5, firstColumn: 'A' }); // Default values
        }
      });

      process.on('error', (err) => {
        console.error('Failed to start Excel structure analysis process:', err);
        resolve({ headerRow: 4, dataStartRow: 5, firstColumn: 'A' }); // Default values
      });
    });
  } catch (error) {
    console.error(`Error analyzing Excel structure: ${error.message}`);
    return { headerRow: 4, dataStartRow: 5, firstColumn: 'A' }; // Default values
  }
});