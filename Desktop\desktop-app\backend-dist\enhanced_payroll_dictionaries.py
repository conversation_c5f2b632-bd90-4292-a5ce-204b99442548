"""
Enhanced Payroll Dictionaries Module

This module provides standardized dictionaries for payslip sections and items
to improve extraction accuracy and consistency when processing payslips.

The enhanced dictionary structure includes:
- Sections (PERSONAL DETAILS, EARNINGS, DEDUCTIONS, etc.)
- Line items with their exact formatting
- Value formats for each item
- Option to include/exclude items from reports
- Standardization support with variations for each item
"""

import os
import json
import re
from datetime import datetime

# Define the path for storing dictionaries
DICT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dictionaries')
DICTIONARY_PATH = os.path.join(DICT_DIR, 'payslip_dictionary.json')

# Ensure the dictionaries directory exists
os.makedirs(DICT_DIR, exist_ok=True)

# Define the default dictionary structure
DEFAULT_DICTIONARY = {
    "PERSONAL DETAILS": {
        "items": {
            "Employee No.": {
                "format": "[TC][.]",
                "value_format": "Alphanumeric",
                "include_in_report": True,
                "standardized_name": "Employee No.",
                "variations": []
            },
            "Employee Name": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            },
            "SSF No.": {
                "format": "[TC][.]",
                "value_format": "Numeric",
                "include_in_report": True
            },
            "Ghana Card ID": {
                "format": "[TC]",
                "value_format": "Alphanumeric with hyphens",
                "include_in_report": True
            },
            "Section": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            },
            "Department": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            },
            "Job Title": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            }
        }
    },
    "EARNINGS": {
        "items": {
            "EARNINGS": {
                "format": "[UC]",
                "value_format": "Section Header",
                "include_in_report": False
            },
            "GROSS SALARY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True
            },
            "NET PAY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True
            }
        }
    },
    "DEDUCTIONS": {
        "items": {
            "DEDUCTIONS": {
                "format": "[UC]",
                "value_format": "Section Header",
                "include_in_report": False
            },
            "TOTAL DEDUCTIONS": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True
            },
            "TAXABLE SALARY": {
                "format": "[UC]",
                "value_format": "Numeric with decimal places",
                "include_in_report": True
            }
        }
    },
    "EMPLOYERS CONTRIBUTION": {
        "items": {
            "Employer's Contributions": {
                "format": "[TC][']",
                "value_format": "Section Header",
                "include_in_report": False
            }
        }
    },
    "LOANS": {
        "items": {
            "LOANS": {
                "format": "[UC]",
                "value_format": "Section Header",
                "include_in_report": False
            },
            "LOAN": {
                "format": "[UC]",
                "value_format": "Column Header",
                "include_in_report": False
            },
            "BALANCE B/F": {
                "format": "[UC][/]",
                "value_format": "Column Header",
                "include_in_report": False
            },
            "CURRENT DEDUCTION": {
                "format": "[UC]",
                "value_format": "Column Header",
                "include_in_report": False
            },
            "OUST. BALANCE": {
                "format": "[UC][.]",
                "value_format": "Column Header",
                "include_in_report": False
            }
        }
    },
    "EMPLOYEE BANK DETAILS": {
        "items": {
            "EMPLOYEE BANK DETAILS": {
                "format": "[UC]",
                "value_format": "Section Header",
                "include_in_report": False
            },
            "Bank": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            },
            "Account No.": {
                "format": "[TC][.]",
                "value_format": "Alphanumeric",
                "include_in_report": True
            },
            "Branch": {
                "format": "[TC]",
                "value_format": "Text",
                "include_in_report": True
            }
        }
    }
}

def load_dictionary():
    """Load the dictionary from file or create it with defaults if it doesn't exist."""
    try:
        if os.path.exists(DICTIONARY_PATH):
            with open(DICTIONARY_PATH, 'r') as f:
                return json.load(f)
        else:
            # Create the dictionary with defaults
            save_dictionary(DEFAULT_DICTIONARY)
            return DEFAULT_DICTIONARY
    except Exception as e:
        print(f"Error loading dictionary {DICTIONARY_PATH}: {e}")
        return DEFAULT_DICTIONARY

def save_dictionary(dictionary):
    """Save the dictionary to file."""
    try:
        with open(DICTIONARY_PATH, 'w') as f:
            json.dump(dictionary, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving dictionary {DICTIONARY_PATH}: {e}")
        return False

def get_section_items(section_name):
    """Get all items for a specific section."""
    dictionary = load_dictionary()
    if section_name in dictionary:
        return dictionary[section_name]["items"]
    return {}

def add_item(section_name, item_name, format_str, value_format, include_in_report=True, standardized_name="", variations=None):
    """Add or update an item in the dictionary."""
    dictionary = load_dictionary()

    # Create section if it doesn't exist
    if section_name not in dictionary:
        dictionary[section_name] = {"items": {}}

    # Use item_name as standardized_name if not provided
    if not standardized_name:
        standardized_name = item_name

    # Initialize variations as empty list if not provided
    if variations is None:
        variations = []

    # Add or update the item
    dictionary[section_name]["items"][item_name] = {
        "format": format_str,
        "value_format": value_format,
        "include_in_report": include_in_report,
        "standardized_name": standardized_name,
        "variations": variations
    }

    return save_dictionary(dictionary)

def remove_item(section_name, item_name):
    """Remove an item from the dictionary."""
    dictionary = load_dictionary()

    if section_name in dictionary and item_name in dictionary[section_name]["items"]:
        del dictionary[section_name]["items"][item_name]
        return save_dictionary(dictionary)

    return False

def get_all_sections():
    """Get all section names."""
    dictionary = load_dictionary()
    return list(dictionary.keys())

def import_from_excel(excel_data):
    """
    Import dictionary data from Excel format.

    Args:
        excel_data: List of dictionaries with keys: SECTION, LINE ITEM, FORMAT, VALUE FORMAT,
                   INCLUDE_IN_REPORT, STANDARDIZED_NAME, VARIATIONS

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create a new dictionary structure
        new_dictionary = {}

        for row in excel_data:
            section = row.get("SECTION", "")
            item = row.get("LINE ITEM", "")
            format_str = row.get("FORMAT", "")
            value_format = row.get("VALUE FORMAT", "")
            include_in_report = row.get("INCLUDE_IN_REPORT", "YES") == "YES"
            standardized_name = row.get("STANDARDIZED_NAME", item)
            variations_str = row.get("VARIATIONS", "")

            # Parse variations from comma-separated string
            variations = [v.strip() for v in variations_str.split(",")] if variations_str else []

            # Skip empty rows
            if not section or not item:
                continue

            # Create section if it doesn't exist
            if section not in new_dictionary:
                new_dictionary[section] = {"items": {}}

            # Add the item
            new_dictionary[section]["items"][item] = {
                "format": format_str,
                "value_format": value_format,
                "include_in_report": include_in_report,
                "standardized_name": standardized_name,
                "variations": variations
            }

        # Save the new dictionary
        return save_dictionary(new_dictionary)
    except Exception as e:
        print(f"Error importing from Excel: {e}")
        return False

def export_to_excel_format():
    """
    Export dictionary data to Excel-compatible format.

    Returns:
        List of dictionaries with keys: SECTION, LINE ITEM, FORMAT, VALUE FORMAT,
        INCLUDE_IN_REPORT, STANDARDIZED_NAME, VARIATIONS
    """
    dictionary = load_dictionary()
    excel_data = []

    for section, section_data in dictionary.items():
        for item, item_data in section_data["items"].items():
            # Convert variations list to comma-separated string
            variations = item_data.get("variations", [])
            variations_str = ", ".join(variations) if variations else ""

            excel_data.append({
                "SECTION": section,
                "LINE ITEM": item,
                "FORMAT": item_data.get("format", ""),
                "VALUE FORMAT": item_data.get("value_format", ""),
                "INCLUDE_IN_REPORT": "YES" if item_data.get("include_in_report", True) else "NO",
                "STANDARDIZED_NAME": item_data.get("standardized_name", item),
                "VARIATIONS": variations_str
            })

    return excel_data

def get_format_pattern(format_str):
    """
    Convert a format string to a regex pattern.

    Args:
        format_str: Format string like "[UC]", "[TC][.]", etc.

    Returns:
        A regex pattern that matches text with the specified format
    """
    pattern = ""

    # Handle capitalization patterns
    if "[UC]" in format_str:  # ALL UPPERCASE
        pattern = r"[A-Z0-9\s\-\.\/]+"
    elif "[TC]" in format_str:  # Title Case
        pattern = r"[A-Z][a-z0-9\s\-\.\/]*"
    elif "[LC]" in format_str:  # lowercase
        pattern = r"[a-z0-9\s\-\.\/]+"
    elif "[MC]" in format_str:  # Mixed Case
        pattern = r"[A-Za-z0-9\s\-\.\/]+"

    # Add special character patterns
    if "[.]" in format_str:  # Contains period
        pattern = pattern.replace(r"\.", r"\.")
    if "[-]" in format_str:  # Contains hyphen
        pattern = pattern.replace(r"\-", r"\-")
    if "[_]" in format_str:  # Contains underscore
        pattern += r".*_.*"
    if "[/]" in format_str:  # Contains slash
        pattern = pattern.replace(r"\/", r"\/")
    if "[#]" in format_str:  # Contains numbers
        pattern += r".*\d.*"
    if "[']" in format_str:  # Contains apostrophe
        pattern += r".*'.*"
    if "[()]" in format_str:  # Contains parentheses
        pattern += r".*\(.*\).*"

    return pattern

def find_item_by_variation(section_name, item_text):
    """
    Find an item in a section by matching against its variations.

    Args:
        section_name: The name of the section to search in
        item_text: The text to match against item names and variations

    Returns:
        The item name and data if found, or (None, None) if not found
    """
    dictionary = load_dictionary()

    # Check if section exists
    if section_name not in dictionary:
        return None, None

    section_items = dictionary[section_name]["items"]

    # First, try exact match with item names
    if item_text in section_items:
        return item_text, section_items[item_text]

    # Then, try matching against variations
    for item_name, item_data in section_items.items():
        variations = item_data.get("variations", [])
        if item_text in variations:
            return item_name, item_data

    # No match found
    return None, None

def get_standardized_name(section_name, item_text):
    """
    Get the standardized name for an item in a section.

    Args:
        section_name: The name of the section to search in
        item_text: The text to match against item names and variations

    Returns:
        The standardized name if found, or the original item_text if not found
    """
    item_name, item_data = find_item_by_variation(section_name, item_text)

    if item_data:
        return item_data.get("standardized_name", item_name)

    return item_text

def add_variation(section_name, item_name, variation):
    """
    Add a variation to an item.

    Args:
        section_name: The name of the section
        item_name: The name of the item
        variation: The variation to add

    Returns:
        True if successful, False otherwise
    """
    dictionary = load_dictionary()

    # Check if section and item exist
    if section_name not in dictionary or item_name not in dictionary[section_name]["items"]:
        return False

    # Get the item data
    item_data = dictionary[section_name]["items"][item_name]

    # Add the variation if it doesn't already exist
    if "variations" not in item_data:
        item_data["variations"] = []

    if variation not in item_data["variations"]:
        item_data["variations"].append(variation)

    # Save the dictionary
    return save_dictionary(dictionary)

def extract_item_value(item_name, format_str, value_format, text):
    """
    Extract the value for a specific item from text.

    Args:
        item_name: The name of the item to extract
        format_str: The format string for the item
        value_format: The expected format of the value
        text: The text to extract from

    Returns:
        The extracted value or None if not found
    """
    # Escape special regex characters in item_name
    escaped_item_name = re.escape(item_name)

    # Create pattern based on value format
    if "Numeric" in value_format:
        value_pattern = r"([\d,\.]+)"
    elif "Alphanumeric" in value_format:
        value_pattern = r"([A-Za-z0-9\-\.\/]+)"
    elif "Text" in value_format:
        value_pattern = r"([A-Za-z0-9\s\-\.\/]+)"
    elif "Section Header" in value_format or "Column Header" in value_format:
        # Headers don't have values to extract
        return None
    else:
        value_pattern = r"(.+)"

    # Create the full pattern
    pattern = f"{escaped_item_name}\\s+{value_pattern}"

    # Search for the pattern
    match = re.search(pattern, text)
    if match:
        return match.group(1).strip()

    return None

# Enhanced Pattern Generation Functions

def generate_regex_pattern(item_name: str, format_str: str, value_format: str) -> str:
    """
    Generate a regex pattern for extracting an item from payslip text.

    Args:
        item_name: The name of the item (e.g., "Employee No.")
        format_str: Format specification (e.g., "[TC][.]")
        value_format: Value format (e.g., "Alphanumeric")

    Returns:
        A regex pattern string for extraction
    """
    # Escape special regex characters in item name
    escaped_name = re.escape(item_name)

    # Replace common variations in spacing
    pattern = escaped_name.replace(r'\ ', r'\s*')

    # Add value capture group based on value format
    value_pattern = get_value_pattern(value_format)

    # Combine into full pattern
    full_pattern = f"{pattern}\\s*:?\\s*{value_pattern}"

    return full_pattern

def get_value_pattern(value_format: str) -> str:
    """
    Get regex pattern for different value formats.

    Args:
        value_format: The value format specification

    Returns:
        Regex pattern for capturing the value
    """
    patterns = {
        'Text': r'([A-Za-z\s]+)',
        'Numeric': r'([\d,.]+)',
        'Alphanumeric': r'([A-Za-z0-9]+)',
        'Alphanumeric with hyphens': r'([A-Za-z0-9-]+)',
        'Date': r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
        'Currency': r'([\d,.]+)',
        'Percentage': r'([\d,.]+%?)',
        'Email': r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
        'Phone': r'([\d\s\-\+\(\)]+)'
    }

    return patterns.get(value_format, r'([^\s]+)')

def validate_format_string(format_str: str) -> tuple:
    """
    Validate a format string against known format codes.

    Args:
        format_str: Format string to validate (e.g., "[TC][.]")

    Returns:
        Tuple of (is_valid, description/error_message)
    """
    valid_codes = {
        '[TC]': 'Title Case',
        '[UC]': 'Upper Case',
        '[LC]': 'Lower Case',
        '[.]': 'Contains Period',
        '[:]': 'Contains Colon',
        '[-]': 'Contains Hyphen',
        '[N]': 'Numeric Only',
        '[AN]': 'Alphanumeric',
        '[D]': 'Date Format'
    }

    if not format_str:
        return False, "Format string cannot be empty"

    # Find all format codes in the string
    codes = re.findall(r'\[[^\]]+\]', format_str)

    if not codes:
        return False, "No valid format codes found"

    invalid_codes = []
    descriptions = []

    for code in codes:
        if code in valid_codes:
            descriptions.append(valid_codes[code])
        else:
            invalid_codes.append(code)

    if invalid_codes:
        return False, f"Invalid format codes: {', '.join(invalid_codes)}"

    return True, f"Valid format: {' + '.join(descriptions)}"

def test_pattern_against_text(pattern: str, test_text: str) -> list:
    """
    Test a regex pattern against sample text.

    Args:
        pattern: Regex pattern to test
        test_text: Sample text to test against

    Returns:
        List of matches found
    """
    try:
        matches = re.findall(pattern, test_text, re.IGNORECASE | re.MULTILINE)
        return matches if isinstance(matches, list) else [matches] if matches else []
    except re.error as e:
        return [f"Regex error: {str(e)}"]
