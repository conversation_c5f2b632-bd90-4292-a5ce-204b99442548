@echo off
setlocal EnableDelayedExpansion

:: Check for admin privileges
NET SESSION >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click on the script and select "Run as administrator".

    :: Create a VBS script to request elevation
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    del "%temp%\getadmin.vbs"
    exit /B
)

echo Moving Tesseract from vendor directory to standard location...

:: Define source and destination paths
set "SOURCE_DIR=%~dp0vendor\tesseract"
set "DEST_DIR=C:\Program Files\Tesseract-OCR"

:: Check if source directory exists
if not exist "%SOURCE_DIR%" (
    echo Source directory not found: %SOURCE_DIR%
    echo Please make sure Tesseract files are in the vendor\tesseract directory.
    pause
    exit /b 1
)

:: Create destination directory if it doesn't exist
if not exist "%DEST_DIR%" (
    echo Creating destination directory: %DEST_DIR%
    mkdir "%DEST_DIR%"
)

:: Copy all files from source to destination
echo Copying files from %SOURCE_DIR% to %DEST_DIR%...
:: Use /Q for quiet mode to suppress file names while copying
xcopy "%SOURCE_DIR%\*" "%DEST_DIR%\" /E /I /H /Y /Q

:: Check if copy was successful
if %errorlevel% neq 0 (
    echo Failed to copy files. Error code: %errorlevel%
    pause
    exit /b 1
)

:: Add Tesseract to PATH
echo Adding Tesseract to PATH...
set "PATH_TO_ADD=%DEST_DIR%"
set "BIN_PATH_TO_ADD=%DEST_DIR%\bin"

:: Check if main directory exists in PATH
echo %PATH% | findstr /C:"%PATH_TO_ADD%" >nul
if %errorlevel% neq 0 (
    echo Adding %PATH_TO_ADD% to PATH...
    setx PATH "%PATH%;%PATH_TO_ADD%" /M
) else (
    echo %PATH_TO_ADD% is already in PATH.
)

:: Check if bin directory exists and add it to PATH if needed
if exist "%BIN_PATH_TO_ADD%" (
    echo %PATH% | findstr /C:"%BIN_PATH_TO_ADD%" >nul
    if %errorlevel% neq 0 (
        echo Adding %BIN_PATH_TO_ADD% to PATH...
        setx PATH "%PATH%;%BIN_PATH_TO_ADD%" /M
    ) else (
        echo %BIN_PATH_TO_ADD% is already in PATH.
    )
)

echo Tesseract has been successfully moved to %DEST_DIR% and added to PATH.
echo You may need to restart your computer for the PATH changes to take effect.
pause
