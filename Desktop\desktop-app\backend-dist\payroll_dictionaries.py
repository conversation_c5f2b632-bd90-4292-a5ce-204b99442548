"""
Payroll Dictionaries Module

This module provides standardized dictionaries for earnings, deductions, and loan types
to improve extraction accuracy and consistency when processing payslips.
"""

import os
import json
from datetime import datetime

# Define the path for storing dictionaries
DICT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dictionaries')
EARNINGS_DICT_PATH = os.path.join(DICT_DIR, 'earnings_dictionary.json')
DEDUCTIONS_DICT_PATH = os.path.join(DICT_DIR, 'deductions_dictionary.json')
LOANS_DICT_PATH = os.path.join(DICT_DIR, 'loans_dictionary.json')

# Ensure the dictionaries directory exists
os.makedirs(DICT_DIR, exist_ok=True)

# Default dictionaries with common variations
DEFAULT_EARNINGS = {
    "BASIC SALARY": ["BASIC", "BASE SALARY", "BASE PAY", "BASIC PAY"],
    "HOUSING ALLOWANCE": ["HOUSING", "HOUSE ALLOWANCE", "ACCOMMODATION", "HOUSING ALLWNCE"],
    "TRANSPORT ALLOWANCE": ["TRANSPORT", "TRANSPORTATION", "TRANSPORT ALLWNCE"],
    "RESPONSIBILITY ALLOWANCE": ["RESPONSIBILITY", "RESP ALLOWANCE", "RESP ALLWNCE"],
    "OVERTIME": ["OT", "OVER TIME", "O/T", "OVERTIME PAYMENT"],
    "BONUS": ["PERFORMANCE BONUS", "ANNUAL BONUS", "QUARTERLY BONUS"],
    "MEDICAL ALLOWANCE": ["MEDICAL", "HEALTH ALLOWANCE", "MEDICAL ALLWNCE"],
    "UTILITY ALLOWANCE": ["UTILITY", "UTILITIES", "UTILITY ALLWNCE"],
    "ACTING ALLOWANCE": ["ACTING", "ACTING ALLWNCE"],
    "SPECIAL ALLOWANCE": ["SPECIAL", "SPECIAL ALLWNCE"]
}

DEFAULT_DEDUCTIONS = {
    "INCOME TAX": ["PAYE", "TAX", "INCOME TAX DEDUCTION", "PAY AS YOU EARN"],
    "SSF EMPLOYEE": ["SSF", "SOCIAL SECURITY", "SSNIT", "PENSION", "SSF EEMPLOYEE"],
    "PROVIDENT FUND": ["PF", "PROVIDENT", "PROV FUND", "TIER 3"],
    "WELFARE FUND": ["WELFARE", "WELFARE CONTRIBUTION", "PENT. MIN WELFARE FUND"],
    "TITHES": ["TITHE", "CHURCH CONTRIBUTION", "CHURCH TITHE"],
    "LOAN REPAYMENT": ["LOAN", "LOAN DEDUCTION", "LOAN PAYMENT"],
    "HEALTH INSURANCE": ["HEALTH", "MEDICAL INSURANCE", "NHIS", "HEALTH INS"],
    "MINISTERS PENSION": ["MINISTERS-PENSION", "MIN PENSION", "PENSION CONTRIBUTION"],
    "UNION DUES": ["UNION", "UNION CONTRIBUTION", "ASSOCIATION DUES"]
}

DEFAULT_LOANS = {
    "PERSONAL LOAN": ["PERSONAL", "STAFF LOAN", "EMPLOYEE LOAN"],
    "CAR LOAN": ["CAR", "VEHICLE LOAN", "AUTO LOAN", "MOTOR LOAN"],
    "HOUSING LOAN": ["HOUSE", "MORTGAGE", "ACCOMMODATION LOAN", "BUILDING LOAN"],
    "EDUCATION LOAN": ["EDUCATION", "SCHOOL FEES LOAN", "TUITION LOAN", "STUDY LOAN"],
    "EMERGENCY LOAN": ["EMERGENCY", "URGENT LOAN", "SHORT TERM LOAN"],
    "SALARY ADVANCE": ["ADVANCE", "SALARY ADV", "PAY ADVANCE"]
}

def load_dictionary(dict_path, default_dict):
    """Load a dictionary from file or create it with defaults if it doesn't exist."""
    try:
        if os.path.exists(dict_path):
            with open(dict_path, 'r') as f:
                return json.load(f)
        else:
            # Create the dictionary with defaults
            save_dictionary(dict_path, default_dict)
            return default_dict
    except Exception as e:
        print(f"Error loading dictionary {dict_path}: {e}")
        return default_dict

def save_dictionary(dict_path, dictionary):
    """Save a dictionary to file."""
    try:
        with open(dict_path, 'w') as f:
            json.dump(dictionary, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving dictionary {dict_path}: {e}")
        return False

def get_earnings_dictionary():
    """Get the earnings dictionary."""
    return load_dictionary(EARNINGS_DICT_PATH, DEFAULT_EARNINGS)

def get_deductions_dictionary():
    """Get the deductions dictionary."""
    return load_dictionary(DEDUCTIONS_DICT_PATH, DEFAULT_DEDUCTIONS)

def get_loans_dictionary():
    """Get the loans dictionary."""
    return load_dictionary(LOANS_DICT_PATH, DEFAULT_LOANS)

def save_earnings_dictionary(dictionary):
    """Save the earnings dictionary."""
    return save_dictionary(EARNINGS_DICT_PATH, dictionary)

def save_deductions_dictionary(dictionary):
    """Save the deductions dictionary."""
    return save_dictionary(DEDUCTIONS_DICT_PATH, dictionary)

def save_loans_dictionary(dictionary):
    """Save the loans dictionary."""
    return save_dictionary(LOANS_DICT_PATH, dictionary)

def standardize_item_name(item_name, dictionary):
    """
    Standardize an item name using the dictionary.
    
    Args:
        item_name: The item name to standardize
        dictionary: The dictionary to use for standardization
        
    Returns:
        The standardized item name or the original if no match is found
    """
    # First check if the item is already a standard name
    if item_name in dictionary:
        return item_name
        
    # Check if the item matches any variation
    for standard_name, variations in dictionary.items():
        if any(variation.upper() == item_name.upper() for variation in variations):
            return standard_name
            
    # If no match is found, return the original
    return item_name

def standardize_earnings(earnings_dict):
    """
    Standardize earnings dictionary keys using the earnings dictionary.
    
    Args:
        earnings_dict: Dictionary of earnings items
        
    Returns:
        Dictionary with standardized keys
    """
    dictionary = get_earnings_dictionary()
    standardized = {}
    
    for key, value in earnings_dict.items():
        std_key = standardize_item_name(key, dictionary)
        standardized[std_key] = value
        
    return standardized

def standardize_deductions(deductions_dict):
    """
    Standardize deductions dictionary keys using the deductions dictionary.
    
    Args:
        deductions_dict: Dictionary of deduction items
        
    Returns:
        Dictionary with standardized keys
    """
    dictionary = get_deductions_dictionary()
    standardized = {}
    
    for key, value in deductions_dict.items():
        std_key = standardize_item_name(key, dictionary)
        standardized[std_key] = value
        
    return standardized

def standardize_loan_type(loan_type):
    """
    Standardize a loan type using the loans dictionary.
    
    Args:
        loan_type: The loan type to standardize
        
    Returns:
        The standardized loan type or the original if no match is found
    """
    dictionary = get_loans_dictionary()
    return standardize_item_name(loan_type, dictionary)
