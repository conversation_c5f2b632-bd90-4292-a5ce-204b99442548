import os
import sys
import json
import re
import logging
import pandas as pd

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('PayslipExtractor')

# Print immediate feedback
print("Script started. Testing payslip data extraction for Bank Adviser...")

class PayslipExtractor:
    """
    Class for testing extraction of data from payslip PDFs for Bank Adviser.
    """

    def __init__(self):
        """Initialize the PayslipExtractor class."""
        self.payslip_data = {}
        self.sections_found = set()
        self.section_data_counts = {}

    def extract_payslip_data(self, payslip_files):
        """
        Extract data from payslip PDFs using direct access to the improved parser functions.

        Args:
            payslip_files (list): List of paths to payslip PDF files
        """
        logger.info(f"Extracting data from {len(payslip_files)} payslip files")
        print(f"Extracting data from {len(payslip_files)} payslip files")

        # Initialize payslip data dictionary
        self.payslip_data = {}
        self.sections_found = set()
        self.section_data_counts = {}

        # If no payslip files, return early
        if not payslip_files:
            logger.warning("No payslip files provided")
            print("No payslip files provided")
            return

        try:
            # Import required modules for PDF extraction
            print("Importing PDF processing modules...")
            import PyPDF2
            import pdfplumber
            
            # Process each payslip file
            for payslip_file in payslip_files:
                try:
                    print(f"Processing payslip file: {payslip_file}")
                    
                    # Extract text from the PDF
                    with open(payslip_file, 'rb') as file:
                        # Create a PDF reader object
                        pdf_reader = PyPDF2.PdfReader(file)
                        
                        # Get the number of pages
                        num_pages = len(pdf_reader.pages)
                        print(f"PDF has {num_pages} pages")
                        
                        # Extract text from all pages
                        text = ""
                        for page_num in range(num_pages):
                            page = pdf_reader.pages[page_num]
                            page_text = page.extract_text()
                            text += page_text
                            
                            # Log a sample of the extracted text (first 500 chars) for debugging
                            sample_text = page_text[:500] + "..." if len(page_text) > 500 else page_text
                            print(f"Page {page_num+1} extracted text sample: {sample_text}")
                    
                    # Parse the text to extract structured data
                    payslip_data = self._parse_payslip_text(text)
                    
                    # Extract the employee number from personal details
                    employee_no = None
                    personal_details = payslip_data.get('PERSONAL DETAILS', {})
                    
                    # Try different possible field names for employee number
                    for field in ['EMPLOYEE NO.', 'EMPLOYEE NUMBER', 'STAFF ID', 'ID', 'EMPLOYEE ID', 'STAFF NUMBER']:
                        if field in personal_details:
                            employee_no = personal_details[field]
                            # Clean up the employee number (remove spaces, etc.)
                            if employee_no:
                                employee_no = re.sub(r'\s+', '', str(employee_no))
                            break
                    
                    # If not found in personal details, try to extract from filename
                    if not employee_no:
                        filename = os.path.basename(payslip_file)
                        # Look for patterns like COP1234 or just numbers
                        match = re.search(r'([A-Z]+\d+|\d+)', filename)
                        if match:
                            employee_no = match.group(1)
                    
                    # Extract net pay from earnings section
                    net_pay = 0.0
                    earnings = payslip_data.get('EARNINGS', {})
                    
                    # Try different possible field names for net pay
                    net_pay_fields = ['NET PAY', 'NETPAY', 'NET SALARY', 'TAKE HOME', 'NETT PAY', 'TOTAL NET PAY']
                    for field in net_pay_fields:
                        if field in earnings:
                            try:
                                net_pay_str = str(earnings[field])
                                # Remove currency symbols, commas, and other non-numeric characters
                                net_pay_str = re.sub(r'[^\d.]', '', net_pay_str)
                                if net_pay_str:
                                    net_pay = float(net_pay_str)
                                    print(f"Found net pay in earnings section: {field} = {net_pay}")
                                    break
                            except (ValueError, TypeError):
                                print(f"Could not convert net pay value to float: {earnings[field]}")
                    
                    # If not found in earnings, try other sections
                    if net_pay == 0.0:
                        print(f"Net pay not found in earnings section for employee {employee_no}, trying other sections")
                        for section_name, section_data in payslip_data.items():
                            print(f"Checking section {section_name} for net pay")
                            for field_name, field_value in section_data.items():
                                if any(keyword in field_name.upper() for keyword in ['NET PAY', 'NETPAY', 'NET SALARY', 'TAKE HOME']):
                                    print(f"Found potential net pay field: {field_name} = {field_value}")
                                    try:
                                        net_pay_str = str(field_value)
                                        # Remove currency symbols, commas, and other non-numeric characters
                                        net_pay_str = re.sub(r'[^\d.]', '', net_pay_str)
                                        if net_pay_str:
                                            net_pay = float(net_pay_str)
                                            print(f"Found net pay in {section_name} section: {field_name} = {net_pay}")
                                            break
                                    except (ValueError, TypeError):
                                        print(f"Could not convert net pay value to float: {field_value}")
                            if net_pay > 0:
                                break
                    
                    # Extract bank account details
                    bank_account = None
                    bank_details = payslip_data.get('EMPLOYEE BANK DETAILS', {})
                    
                    # Try different possible field names for bank account
                    bank_account_fields = ['ACCOUNT NO.', 'ACCOUNT NUMBER', 'BANK ACCOUNT', 'ACCOUNT']
                    for field in bank_account_fields:
                        if field in bank_details:
                            bank_account = bank_details[field]
                            print(f"Found bank account: {field} = {bank_account}")
                            break
                    
                    # If not found in bank details, try to extract from text
                    if not bank_account:
                        account_match = re.search(r'(?:Account\s*(?:No|Number)|Bank\s*Account)[:\s.-]*(\d+[-\s]?\d+[-\s]?\d+)', text, re.IGNORECASE)
                        if account_match:
                            bank_account = account_match.group(1).strip()
                            print(f"Found bank account from text: {bank_account}")
                    
                    # Store the data if employee number is found
                    if employee_no:
                        self.payslip_data[employee_no] = {
                            'EMPLOYEE NO.': employee_no,
                            'NET PAY': net_pay,
                            'ACCOUNT NO.': bank_account if bank_account else '',
                            'SECTIONS': list(payslip_data.keys())
                        }
                        print(f"Extracted data for employee {employee_no}: NET PAY = {net_pay}, ACCOUNT NO. = {bank_account}")
                        
                        # Update sections found
                        for section in payslip_data.keys():
                            self.sections_found.add(section)
                            
                            # Count items in each section
                            if section not in self.section_data_counts:
                                self.section_data_counts[section] = 0
                            self.section_data_counts[section] += len(payslip_data[section])
                    else:
                        print(f"Could not extract employee number from {payslip_file}")
                
                except Exception as e:
                    print(f"Error extracting data from {payslip_file}: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    continue

        except Exception as e:
            print(f"Error initializing payslip extraction: {str(e)}")
            import traceback
            print(traceback.format_exc())

        print(f"Extracted data for {len(self.payslip_data)} employees from payslip files")
        
        # Print section statistics
        print("\nSections found in payslips:")
        for section in self.sections_found:
            count = self.section_data_counts.get(section, 0)
            print(f"  {section}: {count} data items")
        
        # Print the extracted data for debugging
        if self.payslip_data:
            print("\nSample of extracted data:")
            for i, (employee_no, data) in enumerate(list(self.payslip_data.items())[:5]):
                print(f"  Employee {employee_no}: {data}")
            
            if len(self.payslip_data) > 5:
                print(f"  ... and {len(self.payslip_data) - 5} more entries")
        else:
            print("No data was extracted!")

    def _parse_payslip_text(self, text):
        """
        Parse payslip text to extract structured data.
        
        Args:
            text (str): The text to parse
            
        Returns:
            dict: Dictionary containing structured payslip data
        """
        # Initialize the payslip data structure
        payslip_data = {
            'PERSONAL DETAILS': {},
            'EARNINGS': {},
            'DEDUCTIONS': {},
            'EMPLOYERS CONTRIBUTION': {},
            'LOANS': {},
            'EMPLOYEE BANK DETAILS': {}
        }
        
        # Split the text into lines
        lines = text.split('\n')
        
        # Extract personal details
        for i, line in enumerate(lines):
            # Look for employee number
            employee_match = re.search(r'(?:Employee\s*(?:No|Number|ID)|Staff\s*ID)[:\s.-]*([A-Z0-9-]+)', line, re.IGNORECASE)
            if employee_match:
                payslip_data['PERSONAL DETAILS']['EMPLOYEE NO.'] = employee_match.group(1).strip()
                print(f"Found employee number: {payslip_data['PERSONAL DETAILS']['EMPLOYEE NO.']}")
            
            # Look for employee name
            name_match = re.search(r'(?:Employee\s*Name)[:\s.-]*([A-Za-z\s.-]+)', line, re.IGNORECASE)
            if name_match:
                payslip_data['PERSONAL DETAILS']['EMPLOYEE NAME'] = name_match.group(1).strip()
                print(f"Found employee name: {payslip_data['PERSONAL DETAILS']['EMPLOYEE NAME']}")
            
            # Look for bank account
            account_match = re.search(r'(?:Account\s*(?:No|Number)|Bank\s*Account)[:\s.-]*(\d+[-\s]?\d+[-\s]?\d+)', line, re.IGNORECASE)
            if account_match:
                payslip_data['EMPLOYEE BANK DETAILS']['ACCOUNT NO.'] = account_match.group(1).strip()
                print(f"Found bank account: {payslip_data['EMPLOYEE BANK DETAILS']['ACCOUNT NO.']}")
            
            # Look for bank name
            bank_match = re.search(r'(?:Bank\s*Name)[:\s.-]*([A-Za-z\s.-]+)', line, re.IGNORECASE)
            if bank_match:
                payslip_data['EMPLOYEE BANK DETAILS']['BANK'] = bank_match.group(1).strip()
                print(f"Found bank name: {payslip_data['EMPLOYEE BANK DETAILS']['BANK']}")
            
            # Look for branch
            branch_match = re.search(r'(?:Branch)[:\s.-]*([A-Za-z\s.-]+)', line, re.IGNORECASE)
            if branch_match:
                payslip_data['EMPLOYEE BANK DETAILS']['BRANCH'] = branch_match.group(1).strip()
                print(f"Found branch: {payslip_data['EMPLOYEE BANK DETAILS']['BRANCH']}")
        
        # Extract earnings and deductions
        current_section = None
        
        for line in lines:
            # Check for section headers
            if re.search(r'EARNINGS', line, re.IGNORECASE):
                current_section = 'EARNINGS'
                continue
            elif re.search(r'DEDUCTIONS', line, re.IGNORECASE):
                current_section = 'DEDUCTIONS'
                continue
            elif re.search(r'EMPLOYER[\'S]?\s*CONTRIBUTION', line, re.IGNORECASE):
                current_section = 'EMPLOYERS CONTRIBUTION'
                continue
            elif re.search(r'LOANS', line, re.IGNORECASE):
                current_section = 'LOANS'
                continue
            elif re.search(r'BANK\s*DETAILS', line, re.IGNORECASE):
                current_section = 'EMPLOYEE BANK DETAILS'
                continue
            
            # Process line based on current section
            if current_section and current_section in payslip_data:
                # Look for item and amount pattern
                item_amount_match = re.search(r'([A-Za-z\s.-]+)[\s:.-]*(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                if item_amount_match:
                    item = item_amount_match.group(1).strip()
                    amount_str = item_amount_match.group(2).replace(',', '')
                    try:
                        amount = float(amount_str)
                        payslip_data[current_section][item] = amount
                        print(f"Found {current_section} item: {item} = {amount}")
                    except ValueError:
                        print(f"Could not convert amount to float: {amount_str}")
        
        # Look specifically for NET PAY
        for line in lines:
            net_pay_match = re.search(r'(?:NET\s*PAY|NETPAY|NET\s*SALARY|TAKE\s*HOME)[:\s.-]*(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line, re.IGNORECASE)
            if net_pay_match:
                try:
                    net_pay_str = net_pay_match.group(1).replace(',', '')
                    net_pay = float(net_pay_str)
                    payslip_data['EARNINGS']['NET PAY'] = net_pay
                    print(f"Found NET PAY: {net_pay}")
                except ValueError:
                    print(f"Could not convert NET PAY to float: {net_pay_match.group(1)}")
        
        return payslip_data
        
    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.
        
        Args:
            output_file (str): Path to the output Excel file
        """
        try:
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.payslip_data.items():
                data_list.append(data)
                
            df = pd.DataFrame(data_list)
            
            # Save to Excel
            df.to_excel(output_file, index=False)
            
            print(f"Successfully saved data to Excel file: {output_file}")
            print(f"Total records saved: {len(df)}")
            
        except Exception as e:
            print(f"Error saving data to Excel file: {str(e)}")
            import traceback
            print(traceback.format_exc())

def main():
    if len(sys.argv) < 2:
        print("Usage: python direct_payslip_extraction.py <payslip_pdf_file1> [payslip_pdf_file2 ...] [output_excel_file]")
        sys.exit(1)
    
    # Get payslip files and optional output file
    payslip_files = sys.argv[1:-1] if len(sys.argv) > 2 else [sys.argv[1]]
    
    # Default output file is payslip_extract.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "payslip_extract.xlsx")
    
    # If last argument ends with .xlsx, use it as output file
    if sys.argv[-1].endswith('.xlsx'):
        output_file = sys.argv[-1]
        payslip_files = sys.argv[1:-1]
    
    print(f"Payslip files: {payslip_files}")
    print(f"Output file: {output_file}")
    
    extractor = PayslipExtractor()
    extractor.extract_payslip_data(payslip_files)
    
    # Save to Excel if data was extracted
    if extractor.payslip_data:
        extractor.save_to_excel(output_file)
    else:
        print("No data to save to Excel.")

if __name__ == "__main__":
    main()
