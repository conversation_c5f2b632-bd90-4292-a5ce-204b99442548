# Payslip Extraction System - Change Request Document

## Project Overview
This document tracks all change requests for the Payslip Extraction System. The system is a desktop application designed to extract and analyze payslip data, primarily used by Auditors.

## Change Request Log

### CR-001: Payslip Dictionary Template Creation
**Date Requested:** Current Date  
**Status:** Implemented  
**Priority:** High  
**Requested By:** Client  

**Description:**  
Create a comprehensive dictionary template to standardize payslip extraction, focusing on capturing the exact formatting of sections, line items, and values as they appear on payslips.

**Implementation Details:**
1. Created an Excel template with two sheets:
   - Format Legend: Explaining all shorthand notations for text formatting
   - Payslip Dictionary: Main dictionary with all sections and line items

2. Included columns for:
   - SECTION: Main payslip section
   - LINE ITEM: Specific line item within the section
   - FORMAT: Text formatting shorthand
   - VALUE FORMAT: Format of the value (numeric, text, etc.)
   - INCLUDE_IN_REPORT: Whether to include in reports

3. Structured all major payslip sections:
   - PERSONAL DETAILS SECTION
   - EARNINGS SECTION
   - DEDUCTIONS SECTION
   - EMPLOYERS CONTRIBUTION SECTION
   - LOANS SECTION
   - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> BANK DETAILS SECTION

4. Special handling for LOANS section to distinguish between column headers and their values

**Completion Date:** Current Date  
**Implemented By:** System Developer

### CR-002: Dictionary Manager Redesign
**Date Requested:** Previous Conversation  
**Status:** Planned  
**Priority:** High  
**Requested By:** Client  

**Description:**  
Redesign the Dictionary Manager to improve extraction accuracy and reporting.

**Implementation Details:**
1. Clean all default dictionary items from the app and keep only user-entered ones
2. Focus on DEDUCTIONS and EARNINGS section line items when analyzing payslip extraction issues
3. Include a feature to exclude specific line items from the final report even when they have changes
4. Include a feature to capture the exact formatting of sections, line items and values as they appear on the payslip
5. Ensure GROSS SALARY and NETPAY are under EARNINGS SECTION, while TOTAL DEDUCTION and TAXABLE SALARY are under DEDUCTIONS SECTION

**Target Completion Date:** TBD  
**Assigned To:** TBD

### CR-003: Value Format Specification
**Date Requested:** Current Date  
**Status:** Implemented  
**Priority:** Medium  
**Requested By:** Client  

**Description:**  
Replace the general "DESCRIPTION" column with a more specific "VALUE FORMAT" column to better capture the nature of values.

**Implementation Details:**
1. Changed column name from "DESCRIPTION" to "VALUE FORMAT"
2. Focused on capturing the format of values (numeric, text, etc.) rather than general descriptions
3. Added specific value formats for all line items:
   - "Numeric with decimal places" for monetary values
   - "Text" for names and text fields
   - "Alphanumeric" for IDs and codes
   - "Section Header" and "Column Header" for headers

**Completion Date:** Current Date  
**Implemented By:** System Developer

### CR-004: Line Item Terminology Update
**Date Requested:** Current Date  
**Status:** Implemented  
**Priority:** Low  
**Requested By:** Client  

**Description:**  
Change column header from "ITEM" to "LINE ITEM" for clarity and consistency.

**Implementation Details:**
1. Updated column header in the dictionary template
2. Ensured all references to items are now "line items"

**Completion Date:** Current Date  
**Implemented By:** System Developer

## Additional Notes
- Implementation for dictionary manager redesign should take hours, not weeks, as it's modifying an existing system rather than building from scratch.
- The dictionary template should include all shorthand keys for guidance and all items from the payslip with their assigned shorthand keys.
- The dictionary template should include one definite representation for each item, not multiple possibilities.
