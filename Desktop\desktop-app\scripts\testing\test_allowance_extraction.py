import os
import sys
import json
import re
import logging

# Configure logging to output to console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AllowanceExtractor')

# Print immediate feedback
print("<PERSON>ript started. Attempting to extract data from ALL ALLOWANCES PDF...")

class AllowanceExtractor:
    """
    Class for testing extraction of data from ALL ALLOWANCES PDF.
    """

    def __init__(self):
        """Initialize the AllowanceExtractor class."""
        self.allowances_data = {}

    def extract_allowances_data(self, allowances_file):
        """
        Extract data from ALL ALLOWANCES PDF.

        Args:
            allowances_file (str): Path to the ALL ALLOWANCES PDF file
        """
        logger.info(f"Extracting data from allowances file: {allowances_file}")

        # Initialize allowances data dictionary
        self.allowances_data = {}

        # Check if the file exists
        if not allowances_file or not os.path.exists(allowances_file):
            logger.warning(f"Allowances file not found or not provided: {allowances_file}")
            logger.info("Using empty allowances data - no mock data will be generated")
            # Return empty data structure - no mock data
            return

        try:
            # Import PyPDF2 for PDF extraction
            import PyPDF2

            # Open the PDF file
            with open(allowances_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)

                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                logger.info(f"Allowances PDF has {num_pages} pages")

                # Initialize variables for tracking the current section
                current_section = None

                # Process each page
                for page_num in range(num_pages):
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()

                    # Split the text into lines
                    lines = text.split('\n')

                    # Process each line
                    print(f"\nProcessing page {page_num+1} for section detection")
                    for i, line in enumerate(lines):
                        print(f"Checking line {i+1} for section: {line}")

                        # Look for section headers (focus areas and subtitles)
                        # More comprehensive pattern to match various allowance types
                        section_match = re.search(r'^([\w\s\-\&\(\)]+(?:ALLOWANCE|SUBSIDY|BENEFIT|BONUS|INCENTIVE|PAYMENT|REIMBURSEMENT|STIPEND|GRANT|COMPENSATION))', line, re.IGNORECASE)
                        if section_match:
                            current_section = section_match.group(1).strip()
                            logger.info(f"Found allowance section: {current_section}")
                            print(f"  MATCH! Found allowance section: {current_section}")
                            continue

                        # Also check for lines that might be section headers without specific keywords
                        if line.isupper() and len(line.strip()) > 5 and len(line.strip()) < 50 and not re.search(r'\d', line):
                            current_section = line.strip()
                            logger.info(f"Found potential allowance section (uppercase line): {current_section}")
                            print(f"  MATCH! Found potential allowance section (uppercase line): {current_section}")
                            continue

                        # Look for lines with "LV" or similar that might indicate allowances
                        lv_match = re.search(r'\b(LV|LSTG|LIVING|ALLOWANCE)\b', line, re.IGNORECASE)
                        if lv_match and current_section is None:
                            current_section = "LV ALLOWANCE"
                            logger.info(f"Found LV indicator, using section: {current_section}")
                            print(f"  MATCH! Found LV indicator, using section: {current_section}")
                            continue

                        # Look for "EDUCATIONAL SUBSIDY" specifically
                        if "EDUCATIONAL SUBSIDY" in line.upper():
                            current_section = "EDUCATIONAL SUBSIDY"
                            logger.info(f"Found EDUCATIONAL SUBSIDY section")
                            print(f"  MATCH! Found EDUCATIONAL SUBSIDY section")
                            continue

                    # After processing all lines for sections, extract employee data
                    # This is more efficient than trying to extract data line by line
                    employee_data = self._extract_employee_data_from_text(text, current_section, "allowance")

                    # Process the extracted employee data
                    for employee_no, amount, section in employee_data:
                        # Store the data
                        if employee_no not in self.allowances_data:
                            self.allowances_data[employee_no] = {
                                'ALL ALLOWANCES': 0.0,
                                'BREAKDOWN': []
                            }

                        # Add the amount to the total
                        self.allowances_data[employee_no]['ALL ALLOWANCES'] += amount

                        # Add the allowance type to the breakdown
                        if section and amount > 0:
                            # Check if this allowance type is already in the breakdown
                            if section not in self.allowances_data[employee_no]['BREAKDOWN']:
                                self.allowances_data[employee_no]['BREAKDOWN'].append(section)

                        logger.info(f"Found allowance for employee {employee_no}: {section} = {amount}")
                        logger.info(f"Updated allowances for employee {employee_no}: Total = {self.allowances_data[employee_no]['ALL ALLOWANCES']}, Breakdown = {self.allowances_data[employee_no]['BREAKDOWN']}")

            # Convert breakdown lists to strings
            for employee_no, data in self.allowances_data.items():
                if isinstance(data['BREAKDOWN'], list):
                    data['BREAKDOWN'] = ', '.join(data['BREAKDOWN'])

        except Exception as e:
            logger.error(f"Error extracting data from allowances file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

        logger.info(f"Extracted allowances data for {len(self.allowances_data)} employees")

        # Print the extracted data for debugging
        logger.info("Extracted allowances data:")
        for employee_no, data in self.allowances_data.items():
            logger.info(f"  Employee {employee_no}: {data}")

    def _extract_employee_data_from_text(self, text, current_section, data_type="allowance"):
        """
        Extract employee data from text using improved patterns.

        Args:
            text (str): The text to extract data from
            current_section (str): The current section being processed
            data_type (str): The type of data being extracted ("allowance" or "award")

        Returns:
            list: List of tuples containing (employee_no, amount, section)
        """
        results = []

        # Split text into lines
        lines = text.split('\n')

        # Print debugging information
        print(f"\nExtracting employee data from text with current section: {current_section}")
        print(f"Text has {len(lines)} lines")

        # Print a sample of lines for debugging
        print("Sample of lines:")
        for i, line in enumerate(lines[:10]):
            print(f"  Line {i+1}: {line}")

        # Process each line
        for i, line in enumerate(lines):
            # Print every line for debugging
            print(f"Processing line {i+1}: {line}")

            # Look for employee information - more comprehensive pattern
            # This pattern looks for employee IDs in various formats:
            # - Alpha followed by numbers (e.g., COP1234, EMP001)
            # - Just numbers with optional prefix/suffix (e.g., 12345, #12345)
            employee_match = re.search(r'([A-Z]{2,4}\d{3,6}|\d{4,8}[-/]?\d*)\s+([\w\s\.\-\']+)', line)
            if employee_match:
                print(f"  Found potential employee match: {employee_match.group(0)}")

                if current_section:
                    # Extract employee number
                    employee_no = employee_match.group(1).strip()
                    logger.info(f"Found employee in {data_type}: {employee_no} in line: {line}")
                    print(f"  Confirmed employee: {employee_no} (current section: {current_section})")
                else:
                    print(f"  Skipping employee match because no current section is set")

                # For the EDUCATIONAL SUBSIDY section, we know the amount is in a specific position
                # The format is: "COP1337 AYISI DAVID 4,111.45 6,691.86"
                # Where 4,111.45 is the amount we want

                # Split the line by spaces
                parts = line.split()

                # Check if we have enough parts and the employee code is at the beginning
                if len(parts) >= 4 and employee_no in parts[0]:
                    try:
                        # The amount should be the third element from the end
                        amount_str = parts[-2].replace(',', '')
                        amount = float(amount_str)
                        logger.info(f"Found amount for employee {employee_no}: {amount}")
                        print(f"  Extracted amount: {amount} from position {-2} in line parts: {parts}")
                        results.append((employee_no, amount, current_section))
                    except (ValueError, IndexError) as e:
                        logger.warning(f"Could not extract amount for {employee_no}: {str(e)}")
                        print(f"  ERROR extracting amount: {str(e)}, parts: {parts}")

                        # Fallback to regex pattern
                        amount_match = re.search(r'(?:[$€£¥])?(\d{1,3}(?:,\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)', line)
                        if amount_match:
                            try:
                                # Remove commas and other non-numeric characters before converting to float
                                amount_str = amount_match.group(1).replace(',', '')
                                amount_str = re.sub(r'[^\d.]', '', amount_str)
                                amount = float(amount_str)
                                logger.info(f"Found amount for employee {employee_no} using regex: {amount}")
                                print(f"  Extracted amount using regex: {amount}")
                                results.append((employee_no, amount, current_section))
                            except ValueError:
                                logger.warning(f"Could not convert amount to float: {amount_match.group(1)}")
                else:
                    logger.warning(f"Line format not as expected for {employee_no}: {line}")
                    print(f"  Line format not as expected. Parts: {parts}")

        return results

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_allowance_extraction.py <allowances_pdf_file>")
        sys.exit(1)

    allowances_file = sys.argv[1]
    print(f"File path: {allowances_file}")
    print(f"File exists: {os.path.exists(allowances_file)}")

    try:
        # Import PyPDF2 for PDF extraction
        import PyPDF2
        print("PyPDF2 imported successfully")

        # Try to open the file to verify it's a valid PDF
        with open(allowances_file, 'rb') as file:
            try:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)
                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                print(f"Successfully opened PDF. It has {num_pages} pages.")

                # Try to extract text from the first page
                if num_pages > 0:
                    page = pdf_reader.pages[0]
                    text = page.extract_text()
                    print(f"First page text sample: {text[:100]}...")

                    # Print the full text of the first page for analysis
                    print("\nFull text of first page:")
                    print(text)

                    # Print the first few lines to analyze the format
                    print("\nFirst few lines:")
                    lines = text.split('\n')
                    for i, line in enumerate(lines[:20]):
                        print(f"Line {i+1}: {line}")
            except Exception as e:
                print(f"Error reading PDF: {str(e)}")
                import traceback
                print(traceback.format_exc())
    except Exception as e:
        print(f"Error importing or using PyPDF2: {str(e)}")
        import traceback
        print(traceback.format_exc())

    print("Starting extraction...")
    extractor = AllowanceExtractor()
    extractor.extract_allowances_data(allowances_file)

    # Print the results in JSON format
    print("\nResults:")
    print(json.dumps(extractor.allowances_data, indent=2))

    # Print summary
    print(f"\nExtracted allowances data for {len(extractor.allowances_data)} employees")

if __name__ == "__main__":
    main()
