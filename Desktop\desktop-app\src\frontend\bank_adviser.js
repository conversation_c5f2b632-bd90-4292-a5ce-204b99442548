// Bank Adviser functionality
let bankAdviceFilePath = '';
let payslipFilesPath = '';
let allowancesFilePath = '';
let awardsFilePath = '';
let bankAdviceColumns = [];
// Standard columns that should be mapped to Excel columns
let standardColumns = [
  'EMPLOYEE NO.',
  'EMPLOYEE NAME',
  'ACCOUNT NO.',
  'NET PAY',
  'ALL ALLOWANCES', // Map to LV in Excel
  'AWARDS & GRANTS', // Map to LSTG in Excel
  'TOTAL',
  'BRANCH'
];

// Required columns that must be mapped
let requiredColumns = [
  'EMPLOYEE NO.',
  'EMPLOYEE NAME',
  'ACCOUNT NO.',
  'NET PAY',
  'TOTAL',
  'BRANCH'
];

// Recommended columns that should be mapped if available
let recommendedColumns = [
  'ALL ALLOWANCES', // Map to LV in Excel
  'AWARDS & GRANTS' // Map to LSTG in Excel
];

// Columns that are extracted from PDF files for verification
let pdfExtractedColumns = [
  'ALL ALLOWANCES',
  'AWARDS & GRANTS'
];

// Additional columns that are generated by the system, not from Excel
let generatedColumns = [
  'REMARKS',
  'BREAKDOWN'
];
let columnMapping = {};
let bankAdviceData = [];
let verificationResults = {};

// No placeholder data generation - system must use real data

// Initialize the Bank Adviser
function initBankAdviser() {
  // Set up file selection buttons
  setupFileSelectionButtons();

  // Set up the process button
  setupProcessButton();
}

// Set up file selection buttons
function setupFileSelectionButtons() {
  const bankAdviceBrowseBtn = document.getElementById('bank-advice-browse-btn');
  const payslipFilesBrowseBtn = document.getElementById('payslip-files-browse-btn');
  const allowancesBrowseBtn = document.getElementById('allowances-browse-btn');
  const awardsBrowseBtn = document.getElementById('awards-browse-btn');
  const reloadColumnsBtn = document.getElementById('reload-columns-btn');

  if (bankAdviceBrowseBtn) {
    bankAdviceBrowseBtn.addEventListener('click', selectBankAdviceFile);
  }

  if (payslipFilesBrowseBtn) {
    payslipFilesBrowseBtn.addEventListener('click', selectPayslipFiles);
  }

  if (allowancesBrowseBtn) {
    allowancesBrowseBtn.addEventListener('click', selectAllowancesFile);
  }

  if (awardsBrowseBtn) {
    awardsBrowseBtn.addEventListener('click', selectAwardsFile);
  }

  if (reloadColumnsBtn) {
    reloadColumnsBtn.addEventListener('click', reloadColumnsWithSettings);
  }
}

// Set up the process button
function setupProcessButton() {
  const startBankAdviserBtn = document.getElementById('start-bank-adviser-btn');

  if (startBankAdviserBtn) {
    startBankAdviserBtn.addEventListener('click', processBankAdvice);
  }
}

// Function to select the Bank Advice Excel file
async function selectBankAdviceFile() {
  try {
    const options = {
      properties: ['openFile'],
      filters: [{ name: 'Excel Files', extensions: ['xlsx', 'xls'] }]
    };

    const filePath = await window.api.selectFile(options);
    if (filePath) {
      bankAdviceFilePath = filePath;

      const bankAdviceFilePath_input = document.getElementById('bank-advice-file-path');
      const bankAdviceFileError = document.getElementById('bank-advice-file-error');

      if (bankAdviceFilePath_input) {
        bankAdviceFilePath_input.value = filePath;
      }

      // Show loading message
      if (bankAdviceFileError) {
        bankAdviceFileError.textContent = 'Analyzing Excel file structure...';
        bankAdviceFileError.style.color = '#1976d2'; // Blue color for info
      }

      // First, analyze the Excel file to detect the structure
      try {
        console.log(`Analyzing Excel file structure: ${filePath}`);

        // Call the backend to analyze the Excel file structure
        window.api.analyzeExcelStructure(filePath)
          .then(structure => {
            console.log('Detected Excel structure:', structure);

            // Update the Excel structure settings with the detected values
            const headerRowInput = document.getElementById('header-row');
            const dataStartRowInput = document.getElementById('data-start-row');
            const firstColumnInput = document.getElementById('first-column');

            if (headerRowInput && structure.headerRow) {
              headerRowInput.value = structure.headerRow;
            }

            if (dataStartRowInput && structure.dataStartRow) {
              dataStartRowInput.value = structure.dataStartRow;
            }

            if (firstColumnInput && structure.firstColumn) {
              firstColumnInput.value = structure.firstColumn;
            }

            // Show the Excel structure section
            const excelStructureSection = document.getElementById('excel-structure-section');
            if (excelStructureSection) {
              excelStructureSection.style.display = 'block';
            }

            // Now extract the columns using the detected structure
            const headerRow = parseInt(headerRowInput.value) || 4;
            const firstColumn = firstColumnInput.value || 'A';

            // Show loading message
            if (bankAdviceFileError) {
              bankAdviceFileError.textContent = 'Reading Excel columns...';
            }

            // Extract column headers from the Excel file
            console.log(`Getting Excel columns from: ${filePath} (Header Row: ${headerRow}, First Column: ${firstColumn})`);

            // Call the backend to get the actual columns from the Excel file with the specified settings
            window.api.getExcelColumns(filePath, headerRow - 1, firstColumn)
              .then(columns => {
                if (columns && columns.length > 0) {
                  bankAdviceColumns = columns;
                  console.log('Bank advice columns from Excel:', bankAdviceColumns);

                  // Clear any previous error
                  if (bankAdviceFileError) {
                    bankAdviceFileError.textContent = '';
                  }

                  // Show the column mapping section
                  const columnMappingSection = document.getElementById('column-mapping-section');
                  if (columnMappingSection) {
                    columnMappingSection.style.display = 'block';
                  }

                  // Update the column mapping UI with the actual columns
                  updateColumnMappingUI();

                  // Show success message
                  if (bankAdviceFileError) {
                    bankAdviceFileError.textContent = `Successfully loaded ${columns.length} columns from Excel file`;
                    bankAdviceFileError.style.color = '#4caf50'; // Green color for success

                    // Clear the message after 3 seconds
                    setTimeout(() => {
                      if (bankAdviceFileError) {
                        bankAdviceFileError.textContent = '';
                      }
                    }, 3000);
                  }

                  // Enable the process button if we have both Excel and payslip files
                  updateProcessButtonState();
                } else {
                  handleExcelColumnError('No columns returned from Excel file');
                }
              })
              .catch(err => {
                handleExcelColumnError(`Error getting Excel columns: ${err.message || 'Could not read Excel file'}`);
              });
          })
          .catch(err => {
            console.error('Error analyzing Excel structure:', err);

            // Show error message
            if (bankAdviceFileError) {
              bankAdviceFileError.textContent = `Error: ${err.message || 'Could not analyze Excel file structure'}`;
              bankAdviceFileError.style.color = '#f44336'; // Red color for error
            }

            // Still show the Excel structure section with default values
            const excelStructureSection = document.getElementById('excel-structure-section');
            if (excelStructureSection) {
              excelStructureSection.style.display = 'block';
            }
          });
      } catch (error) {
        console.error('Error analyzing Excel file structure:', error);

        // Show error message
        if (bankAdviceFileError) {
          bankAdviceFileError.textContent = `Error: ${error.message || 'Could not analyze Excel file structure'}`;
          bankAdviceFileError.style.color = '#f44336'; // Red color for error
        }

        // Still show the Excel structure section with default values
        const excelStructureSection = document.getElementById('excel-structure-section');
        if (excelStructureSection) {
          excelStructureSection.style.display = 'block';
        }
      }
    }
  } catch (error) {
    console.error('Error selecting Bank Advice file:', error);

    const bankAdviceFileError = document.getElementById('bank-advice-file-error');
    if (bankAdviceFileError) {
      bankAdviceFileError.textContent = `Error: ${error.message}`;
      bankAdviceFileError.style.color = '#f44336'; // Red color for error
    }
  }
}

// Helper function to handle Excel column extraction errors
function handleExcelColumnError(errorMessage) {
  console.error(errorMessage);

  const bankAdviceFileError = document.getElementById('bank-advice-file-error');
  if (bankAdviceFileError) {
    bankAdviceFileError.textContent = `Error: ${errorMessage}`;
    bankAdviceFileError.style.color = '#f44336'; // Red color for error
  }

  // Show the column mapping section
  const columnMappingSection = document.getElementById('column-mapping-section');
  if (columnMappingSection) {
    columnMappingSection.style.display = 'block';
  }

  // Use placeholder columns as fallback that match the actual Excel columns
  bankAdviceColumns = ['No', 'Employee Name', 'Account No', 'Net Salary', 'LV', 'LSTG', 'TOTAL', 'Branch'];
  updateColumnMappingUI();

  // Show a message about using fallback columns
  const columnMappingBody = document.getElementById('column-mapping-body');
  if (columnMappingBody) {
    columnMappingBody.innerHTML += '<tr><td colspan="2" style="color: #f44336;">Using fallback columns. Please check if these match your Excel file or try different row/column settings.</td></tr>';
  }
}

// Function to reload columns with the current Excel structure settings
async function reloadColumnsWithSettings() {
  if (!bankAdviceFilePath) {
    alert('Please select an Excel file first');
    return;
  }

  const bankAdviceFileError = document.getElementById('bank-advice-file-error');
  if (bankAdviceFileError) {
    bankAdviceFileError.textContent = 'Reloading Excel columns with new settings...';
    bankAdviceFileError.style.color = '#1976d2'; // Blue color for info
  }

  // Show a loading message in the column mapping area
  const columnMappingBody = document.getElementById('column-mapping-body');
  if (columnMappingBody) {
    columnMappingBody.innerHTML = '<tr><td colspan="2">Reloading columns with new settings...</td></tr>';
  }

  // Get the Excel structure settings
  const headerRow = parseInt(document.getElementById('header-row').value) || 4;
  const firstColumn = document.getElementById('first-column').value || 'A';

  try {
    console.log(`Reloading Excel columns from: ${bankAdviceFilePath} (Header Row: ${headerRow}, First Column: ${firstColumn})`);

    // Call the backend to get the actual columns from the Excel file with the specified settings
    window.api.getExcelColumns(bankAdviceFilePath, headerRow - 1, firstColumn)
      .then(columns => {
        if (columns && columns.length > 0) {
          bankAdviceColumns = columns;
          console.log('Extracted columns with new settings:', bankAdviceColumns);

          // Update the column mapping UI
          updateColumnMappingUI();

          if (bankAdviceFileError) {
            bankAdviceFileError.textContent = `Successfully loaded ${bankAdviceColumns.length} columns with new settings`;
            bankAdviceFileError.style.color = '#4caf50'; // Green color for success

            // Clear the message after 3 seconds
            setTimeout(() => {
              if (bankAdviceFileError) {
                bankAdviceFileError.textContent = '';
              }
            }, 3000);
          }
        } else {
          throw new Error('Failed to extract columns from Excel file with new settings');
        }
      })
      .catch(err => {
        console.error('Error extracting columns with new settings:', err);

        if (bankAdviceFileError) {
          bankAdviceFileError.textContent = `Error: ${err.message || 'Could not read Excel file with new settings'}`;
          bankAdviceFileError.style.color = '#f44336'; // Red color for error
        }

        if (columnMappingBody) {
          columnMappingBody.innerHTML = `
            <tr>
              <td colspan="2">
                <div class="error-message">Failed to read columns with new settings. Please try different settings or enter columns manually.</div>
                <div style="text-align: center; margin-top: 10px;">
                  <button id="manual-columns-btn" style="padding: 5px 10px;">Enter Columns Manually</button>
                </div>
              </td>
            </tr>
          `;

          // Add event listener for the manual columns button
          const manualColumnsBtn = document.getElementById('manual-columns-btn');
          if (manualColumnsBtn) {
            manualColumnsBtn.addEventListener('click', () => {
              showManualColumnEntryDialog();
            });
          }
        }
      });
  } catch (error) {
    console.error('Error reloading columns:', error);

    if (bankAdviceFileError) {
      bankAdviceFileError.textContent = `Error: ${error.message || 'Could not reload Excel columns'}`;
      bankAdviceFileError.style.color = '#f44336'; // Red color for error
    }

    if (columnMappingBody) {
      columnMappingBody.innerHTML = `
        <tr>
          <td colspan="2">
            <div class="error-message">Failed to reload columns. Please try again or enter columns manually.</div>
            <div style="text-align: center; margin-top: 10px;">
              <button id="manual-columns-btn" style="padding: 5px 10px;">Enter Columns Manually</button>
            </div>
          </td>
        </tr>
      `;

      // Add event listener for the manual columns button
      const manualColumnsBtn = document.getElementById('manual-columns-btn');
      if (manualColumnsBtn) {
        manualColumnsBtn.addEventListener('click', () => {
          showManualColumnEntryDialog();
        });
      }
    }
  }
}

// Function to select the Payslip PDF files
async function selectPayslipFiles() {
  try {
    const options = {
      properties: ['openFile', 'multiSelections'],
      filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
    };

    const filePaths = await window.api.selectFile(options);
    if (filePaths && filePaths.length > 0) {
      payslipFilesPath = Array.isArray(filePaths) ? filePaths : [filePaths];

      const payslipFilesPath_input = document.getElementById('payslip-files-path');
      const payslipFilesError = document.getElementById('payslip-files-error');

      if (payslipFilesPath_input) {
        payslipFilesPath_input.value = Array.isArray(filePaths)
          ? `${filePaths.length} files selected`
          : filePaths;
      }

      if (payslipFilesError) {
        payslipFilesError.textContent = '';
      }

      // Update the process button state
      updateProcessButtonState();
    }
  } catch (error) {
    console.error('Error selecting Payslip files:', error);

    const payslipFilesError = document.getElementById('payslip-files-error');
    if (payslipFilesError) {
      payslipFilesError.textContent = `Error: ${error.message}`;
    }
  }
}

// Function to select the ALL ALLOWANCES PDF file
async function selectAllowancesFile() {
  try {
    const options = {
      properties: ['openFile'],
      filters: [{ name: 'PDF Files', extensions: ['pdf'] }],
      defaultPath: await getMainOtherDirectory()
    };

    const filePath = await window.api.selectFile(options);
    if (filePath) {
      allowancesFilePath = filePath;

      const allowancesFilePath_input = document.getElementById('allowances-file-path');
      const allowancesFileError = document.getElementById('allowances-file-error');

      if (allowancesFilePath_input) {
        allowancesFilePath_input.value = filePath;
      }

      if (allowancesFileError) {
        allowancesFileError.textContent = '';
      }
    }
  } catch (error) {
    console.error('Error selecting ALL ALLOWANCES file:', error);

    const allowancesFileError = document.getElementById('allowances-file-error');
    if (allowancesFileError) {
      allowancesFileError.textContent = `Error: ${error.message}`;
    }
  }
}

// Function to select the AWARDS & GRANTS PDF file
async function selectAwardsFile() {
  try {
    const options = {
      properties: ['openFile'],
      filters: [{ name: 'PDF Files', extensions: ['pdf'] }],
      defaultPath: await getMainOtherDirectory()
    };

    const filePath = await window.api.selectFile(options);
    if (filePath) {
      awardsFilePath = filePath;

      const awardsFilePath_input = document.getElementById('awards-file-path');
      const awardsFileError = document.getElementById('awards-file-error');

      if (awardsFilePath_input) {
        awardsFilePath_input.value = filePath;
      }

      if (awardsFileError) {
        awardsFileError.textContent = '';
      }
    }
  } catch (error) {
    console.error('Error selecting AWARDS & GRANTS file:', error);

    const awardsFileError = document.getElementById('awards-file-error');
    if (awardsFileError) {
      awardsFileError.textContent = `Error: ${error.message}`;
    }
  }
}

// Helper function to get the desktop path
async function getMainOtherDirectory() {
  try {
    // Get the desktop path
    const desktopPath = await window.api.getDesktopPath();
    return desktopPath;
  } catch (error) {
    console.error('Error getting desktop path:', error);
    return '';
  }
}

// Function to update the process button state
function updateProcessButtonState() {
  const startBankAdviserBtn = document.getElementById('start-bank-adviser-btn');

  if (startBankAdviserBtn) {
    // Enable the button if we have both Excel and payslip files
    if (bankAdviceFilePath && payslipFilesPath && payslipFilesPath.length > 0) {
      startBankAdviserBtn.disabled = false;
    } else {
      startBankAdviserBtn.disabled = true;
    }
  }
}

// Function to update the column mapping UI
function updateColumnMappingUI() {
  const columnMappingBody = document.getElementById('column-mapping-body');

  if (!columnMappingBody) {
    console.error('Column mapping body element not found');
    return;
  }

  if (!bankAdviceColumns.length) {
    console.error('No bank advice columns found');
    columnMappingBody.innerHTML = '<tr><td colspan="2">No columns found in the Excel file. Please select a valid Excel file.</td></tr>';
    return;
  }

  // Clear existing rows
  columnMappingBody.innerHTML = '';

  // Add a header row to explain the mapping
  const headerInfoRow = document.createElement('tr');
  headerInfoRow.innerHTML = `
    <td colspan="2" style="background-color: #e3f2fd; padding: 10px; text-align: center;">
      <p><strong>Excel Columns</strong></p>
      <p>The following columns were detected in your Excel file:</p>
    </td>
  `;
  columnMappingBody.appendChild(headerInfoRow);

  // Create a table to display Excel columns with row numbers and column letters
  const excelColumnsTable = document.createElement('table');
  excelColumnsTable.className = 'excel-columns-table';
  excelColumnsTable.style.width = '100%';
  excelColumnsTable.style.borderCollapse = 'collapse';
  excelColumnsTable.style.marginTop = '10px';

  // Create header row with column letters
  const headerRowEl = document.createElement('tr');
  headerRowEl.style.backgroundColor = '#f5f5f5';

  // Add empty cell for row number column
  const emptyHeaderCell = document.createElement('th');
  emptyHeaderCell.style.border = '1px solid #ddd';
  emptyHeaderCell.style.padding = '8px';
  emptyHeaderCell.style.textAlign = 'center';
  headerRowEl.appendChild(emptyHeaderCell);

  // Add column letters
  for (let i = 0; i < bankAdviceColumns.length; i++) {
    const letterCell = document.createElement('th');
    letterCell.textContent = String.fromCharCode(65 + i); // A, B, C, etc.
    letterCell.style.border = '1px solid #ddd';
    letterCell.style.padding = '8px';
    letterCell.style.textAlign = 'center';
    headerRowEl.appendChild(letterCell);
  }

  excelColumnsTable.appendChild(headerRowEl);

  // Create row for column names
  const columnsRow = document.createElement('tr');

  // Add row number cell - use the actual header row from settings
  const headerRow = parseInt(document.getElementById('header-row').value) || 4;
  const rowNumberCell = document.createElement('td');
  rowNumberCell.textContent = headerRow.toString();
  rowNumberCell.style.backgroundColor = '#f5f5f5';
  rowNumberCell.style.fontWeight = 'bold';
  rowNumberCell.style.border = '1px solid #ddd';
  rowNumberCell.style.padding = '8px';
  rowNumberCell.style.textAlign = 'center';
  columnsRow.appendChild(rowNumberCell);

  // Add column name cells
  bankAdviceColumns.forEach(column => {
    const cell = document.createElement('td');
    cell.textContent = column;
    cell.style.border = '1px solid #ddd';
    cell.style.padding = '8px';
    cell.style.textAlign = 'center';
    columnsRow.appendChild(cell);
  });

  excelColumnsTable.appendChild(columnsRow);

  // Create a container for the table
  const tableContainer = document.createElement('div');
  tableContainer.style.overflowX = 'auto';
  tableContainer.appendChild(excelColumnsTable);

  // Create a row for the table
  const tableRow = document.createElement('tr');
  const tableCell = document.createElement('td');
  tableCell.colSpan = 2;
  tableCell.appendChild(tableContainer);
  tableRow.appendChild(tableCell);
  columnMappingBody.appendChild(tableRow);

  // Auto-populate the mappings based on the Excel columns
  const autoMappings = {};
  const usedColumns = new Set(); // Track which Excel columns have been used

  console.log("Starting auto-mapping process with Excel columns:", bankAdviceColumns);

  // First pass: Calculate scores for all standard columns against all Excel columns
  const allScores = {};

  standardColumns.forEach(standardColumn => {
    // Find the best match for this standard column among all Excel columns
    // We'll filter out used columns later
    const bestMatch = findBestMatchingColumn(standardColumn, bankAdviceColumns);
    if (bestMatch) {
      allScores[standardColumn] = bestMatch;
    }
  });

  console.log("All scores calculated:", allScores);

  // Second pass: Assign columns in order of importance, preventing duplicates
  // Define priority order for standard columns
  const columnPriority = [
    'EMPLOYEE NO.',
    'EMPLOYEE NAME',
    'ACCOUNT NO.',
    'NET PAY',
    'ALL ALLOWANCES',
    'AWARDS & GRANTS',
    'TOTAL',
    'BRANCH'
  ];

  // Assign columns in priority order
  columnPriority.forEach(standardColumn => {
    if (allScores[standardColumn] && !usedColumns.has(allScores[standardColumn])) {
      // This column has a match and it's not already used
      autoMappings[standardColumn] = allScores[standardColumn];
      columnMapping[standardColumn] = allScores[standardColumn];
      usedColumns.add(allScores[standardColumn]);
      console.log(`Assigned ${standardColumn} to ${allScores[standardColumn]} (priority order)`);
    } else if (allScores[standardColumn] && usedColumns.has(allScores[standardColumn])) {
      // The best match is already used, try to find another match
      console.log(`Best match for ${standardColumn} (${allScores[standardColumn]}) is already used, looking for alternatives`);

      // Filter out Excel columns that have already been used
      const availableColumns = bankAdviceColumns.filter(col => !usedColumns.has(col));

      if (availableColumns.length > 0) {
        // Find the best match among available columns
        const alternativeMatch = findBestMatchingColumn(standardColumn, availableColumns);
        if (alternativeMatch) {
          autoMappings[standardColumn] = alternativeMatch;
          columnMapping[standardColumn] = alternativeMatch;
          usedColumns.add(alternativeMatch);
          console.log(`Assigned ${standardColumn} to alternative match ${alternativeMatch}`);
        }
      }
    }
  });

  // Log the auto-mappings
  console.log('Auto-populated column mappings:', autoMappings);

  // Add a row to show the mappings
  const mappingsHeaderRow = document.createElement('tr');
  mappingsHeaderRow.innerHTML = `
    <td colspan="2" style="background-color: #e3f2fd; padding: 10px; text-align: center; margin-top: 20px;">
      <p><strong>Column Mappings</strong></p>
      <p>The following mappings will be used for processing:</p>
    </td>
  `;
  columnMappingBody.appendChild(mappingsHeaderRow);

  // Create a table for the mappings
  const mappingsTable = document.createElement('table');
  mappingsTable.style.width = '100%';
  mappingsTable.style.borderCollapse = 'collapse';
  mappingsTable.style.marginTop = '10px';

  // Add header row
  const mappingsHeaderRowEl = document.createElement('tr');
  mappingsHeaderRowEl.style.backgroundColor = '#f5f5f5';

  const standardColHeader = document.createElement('th');
  standardColHeader.textContent = 'Standard Column';
  standardColHeader.style.border = '1px solid #ddd';
  standardColHeader.style.padding = '8px';
  standardColHeader.style.width = '50%';
  mappingsHeaderRowEl.appendChild(standardColHeader);

  const excelColHeader = document.createElement('th');
  excelColHeader.textContent = 'Excel Column';
  excelColHeader.style.border = '1px solid #ddd';
  excelColHeader.style.padding = '8px';
  excelColHeader.style.width = '50%';
  mappingsHeaderRowEl.appendChild(excelColHeader);

  mappingsTable.appendChild(mappingsHeaderRowEl);

  // Add rows for each mapping
  standardColumns.forEach(standardColumn => {
    // Skip REMARKS and BREAKDOWN as they're generated by the system
    if (standardColumn === 'REMARKS' || standardColumn === 'BREAKDOWN') {
      return;
    }

    const row = document.createElement('tr');

    const standardColCell = document.createElement('td');
    standardColCell.textContent = standardColumn;
    standardColCell.style.border = '1px solid #ddd';
    standardColCell.style.padding = '8px';

    // Highlight required columns
    const isRequired = requiredColumns.includes(standardColumn);
    if (isRequired) {
      standardColCell.style.fontWeight = 'bold';
    }

    row.appendChild(standardColCell);

    const excelColCell = document.createElement('td');
    excelColCell.textContent = autoMappings[standardColumn] || 'Not mapped';
    excelColCell.style.border = '1px solid #ddd';
    excelColCell.style.padding = '8px';

    // Highlight missing required mappings
    if (isRequired && !autoMappings[standardColumn]) {
      excelColCell.style.color = '#f44336';
      excelColCell.style.fontWeight = 'bold';
    }

    row.appendChild(excelColCell);

    mappingsTable.appendChild(row);
  });

  // Create a container for the mappings table
  const mappingsContainer = document.createElement('div');
  mappingsContainer.style.overflowX = 'auto';
  mappingsContainer.appendChild(mappingsTable);

  // Create a row for the mappings table
  const mappingsRow = document.createElement('tr');
  const mappingsCell = document.createElement('td');
  mappingsCell.colSpan = 2;
  mappingsCell.appendChild(mappingsContainer);
  mappingsRow.appendChild(mappingsCell);
  columnMappingBody.appendChild(mappingsRow);

  // Add a note about PDF-extracted columns
  const noteRow = document.createElement('tr');
  noteRow.innerHTML = `
    <td colspan="2" style="background-color: #f5f5f5; padding: 10px; font-style: italic;">
      <p>Note: ALL ALLOWANCES typically maps to 'LV' in Excel, and AWARDS & GRANTS typically maps to 'LSTG' in Excel.</p>
      <p>These values are also verified against separate PDF files. If the PDF files are not provided, verification will be based on Excel values only.</p>
    </td>
  `;
  columnMappingBody.appendChild(noteRow);

  // Add a note about auto-mapping
  const autoMappingRow = document.createElement('tr');
  autoMappingRow.innerHTML = `
    <td colspan="2" style="background-color: #e8f5e9; padding: 10px; text-align: center;">
      <p><strong>Excel columns have been automatically mapped based on best matches.</strong></p>
      <p>If you need to adjust the mappings, please reload the Excel file with different settings or enter columns manually.</p>
    </td>
  `;
  columnMappingBody.appendChild(autoMappingRow);
}

// Function to find the best matching Excel column for each standard column
function findBestMatchingColumn(standardColumn, excelColumns) {
  console.log(`Finding best match for standard column: ${standardColumn}`);
  console.log(`Available Excel columns: ${excelColumns.join(', ')}`);

  // Define specific patterns for each standard column
  const columnPatterns = {
    'EMPLOYEE NO.': [
      /^(emp|employee|staff|personnel)[\s._-]*(no|num|number|id|code|#)/i,
      /^(no|num|number|id|code|#)$/i,
      /^(emp|employee|staff|personnel)$/i
    ],
    'EMPLOYEE NAME': [
      /^(emp|employee|staff|personnel)[\s._-]*(name|fullname|full[\s._-]*name)/i,
      /^(name|fullname|full[\s._-]*name)$/i
    ],
    'ACCOUNT NO.': [
      /^(acc|account|bank[\s._-]*acc|bank[\s._-]*account)[\s._-]*(no|num|number|#)/i,
      /^(acc|account)$/i
    ],
    'NET PAY': [
      /^(net|nett)[\s._-]*(pay|salary|amount|payment)/i,
      /^(salary|payment|pay)$/i,
      /^(net|nett)$/i
    ],
    'ALL ALLOWANCES': [
      /^(all[\s._-]*)?allowance(s)?$/i,
      /^(lv|living|subsidy|benefit)$/i,
      /^lv$/i
    ],
    'AWARDS & GRANTS': [
      /^(award|grant|bonus|incentive|prize|lstg)/i,
      /^lstg$/i
    ],
    'TOTAL': [
      /^total[\s._-]*(amount|payment|pay|salary)?$/i,
      /^(sum|gross[\s._-]*total)$/i
    ],
    'BRANCH': [
      /^(branch|bank[\s._-]*branch|location)$/i
    ]
  };

  // Score each Excel column for this standard column
  const scores = [];

  for (const excelCol of excelColumns) {
    let score = 0;
    const excelColUpper = excelCol.toUpperCase().replace(/\./g, ''); // Remove periods for comparison
    const stdColUpper = standardColumn.toUpperCase().replace(/\./g, ''); // Remove periods for comparison

    // Log the comparison
    console.log(`Comparing standard column "${standardColumn}" with Excel column "${excelCol}"`);

    // 1. Exact match (highest score)
    if (stdColUpper === excelColUpper) {
      console.log(`  - Exact match! (100 points)`);
      score += 100;
    }

    // 2. Pattern match (high score)
    if (columnPatterns[standardColumn]) {
      for (const pattern of columnPatterns[standardColumn]) {
        if (pattern.test(excelCol)) {
          console.log(`  - Pattern match with ${pattern}! (80 points)`);
          score += 80;
          break;
        }
      }
    }

    // 3. Contains match (medium score)
    if (excelColUpper.includes(stdColUpper)) {
      console.log(`  - Excel column contains standard column! (60 points)`);
      score += 60;
    } else if (stdColUpper.includes(excelColUpper)) {
      console.log(`  - Standard column contains Excel column! (50 points)`);
      score += 50;
    }

    // 4. Word match (lower score)
    const stdWords = stdColUpper.split(/[\s_\-\.]+/).filter(word => word.length > 2);
    const excelWords = excelColUpper.split(/[\s_\-\.]+/).filter(word => word.length > 2);

    for (const stdWord of stdWords) {
      for (const excelWord of excelWords) {
        if (stdWord === excelWord) {
          console.log(`  - Word exact match: ${stdWord} = ${excelWord}! (40 points)`);
          score += 40;
        } else if (stdWord.length > 3 && excelWord.includes(stdWord)) {
          console.log(`  - Excel word contains standard word: ${excelWord} contains ${stdWord}! (30 points)`);
          score += 30;
        } else if (excelWord.length > 3 && stdWord.includes(excelWord)) {
          console.log(`  - Standard word contains Excel word: ${stdWord} contains ${excelWord}! (20 points)`);
          score += 20;
        }
      }
    }

    // No special cases - rely purely on pattern matching and text similarity

    // Add to scores array
    scores.push({ column: excelCol, score });
  }

  // Sort by score (highest first)
  scores.sort((a, b) => b.score - a.score);

  // Log the scores
  console.log(`Scores for ${standardColumn}:`);
  scores.forEach(item => console.log(`  ${item.column}: ${item.score}`));

  // Return the highest scoring column if score is above threshold
  if (scores.length > 0 && scores[0].score > 0) {
    console.log(`Best match for ${standardColumn} is ${scores[0].column} with score ${scores[0].score}`);
    return scores[0].column;
  }

  // If no match found, return null
  console.log(`No match found for ${standardColumn}`);
  return null;
}

// Function to automatically map columns based on similarity
function autoMapColumn(standardColumn, excelColumn) {
  // Simple direct matching approach - no hardcoded patterns

  // 1. Try exact match (case-insensitive)
  const stdColUpper = standardColumn.toUpperCase().replace(/\./g, ''); // Remove periods for comparison
  const excelColUpper = excelColumn.toUpperCase().replace(/\./g, ''); // Remove periods for comparison

  // Direct match
  if (stdColUpper === excelColUpper) {
    console.log(`Auto-mapped ${standardColumn} to ${excelColumn} (exact match)`);
    return true;
  }

  // 2. Try partial match - if Excel column contains the standard column or vice versa
  if (excelColUpper.includes(stdColUpper) || stdColUpper.includes(excelColUpper)) {
    console.log(`Auto-mapped ${standardColumn} to ${excelColumn} (partial match)`);
    return true;
  }

  // 3. Try word-by-word match
  const stdWords = stdColUpper.split(/[\s_\-\.]+/).filter(word => word.length > 2);
  const excelWords = excelColUpper.split(/[\s_\-\.]+/).filter(word => word.length > 2);

  // Check if any significant word matches
  for (const stdWord of stdWords) {
    for (const excelWord of excelWords) {
      if (stdWord === excelWord ||
          (stdWord.length > 3 && excelWord.includes(stdWord)) ||
          (excelWord.length > 3 && stdWord.includes(excelWord))) {
        console.log(`Auto-mapped ${standardColumn} to ${excelColumn} (word match: ${stdWord} ~ ${excelWord})`);
        return true;
      }
    }
  }

  // No special cases in this function - we handle those in findBestMatchingColumn

  return false;
}

// Function to process the Bank Advice
async function processBankAdvice() {
  // Validate inputs
  if (!validateInputs()) {
    return;
  }

  // Show processing UI
  const bankAdviserResults = document.getElementById('bank-adviser-results');
  if (bankAdviserResults) {
    bankAdviserResults.innerHTML = `
      <div class="progress-container">
        <h4>Processing Bank Advice Data</h4>
        <div class="processing-details-container">
          <h5>Processing Details</h5>
          <div class="processing-details-box" id="processing-details-box"></div>
        </div>
      </div>
    `;
  }

  try {
    const processingDetailsBox = document.getElementById('processing-details-box');

    // Add initial processing message
    if (processingDetailsBox) {
      addProcessingMessage(processingDetailsBox, 'Starting Bank Advice processing...');
    }

    // Check if column mapping is valid
    const missingColumns = [];

    for (const col of requiredColumns) {
      if (!columnMapping[col] || columnMapping[col] === '') {
        missingColumns.push(col);
      }
    }

    if (missingColumns.length > 0) {
      throw new Error(`Missing required column mappings: ${missingColumns.join(', ')}. Please map all required columns.`);
    }

    // Log the column mapping for debugging
    console.log('Column mapping:', columnMapping);

    // Get the Excel structure settings
    const headerRow = parseInt(document.getElementById('header-row').value) || 4;
    const dataStartRow = parseInt(document.getElementById('data-start-row').value) || 5;
    const firstColumn = document.getElementById('first-column').value || 'A';

    // Prepare data for the backend
    const data = {
      bankAdviceFile: bankAdviceFilePath,
      payslipFiles: payslipFilesPath,
      allowancesFile: allowancesFilePath || '', // Empty string if not selected
      awardsFile: awardsFilePath || '', // Empty string if not selected
      columnMapping: columnMapping,
      excelSettings: {
        headerRow: headerRow - 1, // Convert to 0-based index
        dataStartRow: dataStartRow - 1, // Convert to 0-based index
        firstColumn: firstColumn
      }
    };

    // Add processing messages
    if (processingDetailsBox) {
      addProcessingMessage(processingDetailsBox, 'Reading Bank Advice Excel file...');
      addProcessingMessage(processingDetailsBox, 'Extracting data from Payslip PDFs...');
      addProcessingMessage(processingDetailsBox, 'Processing ALL ALLOWANCES PDF...');
      addProcessingMessage(processingDetailsBox, 'Processing AWARDS & GRANTS PDF...');
      addProcessingMessage(processingDetailsBox, 'Verifying data...');
      addProcessingMessage(processingDetailsBox, 'Generating results...');
    }

    // Call the backend to process the Bank Advice
    try {
      const result = await window.api.processBankAdvice(data);
      console.log('Bank Advice processing result:', result);

      if (result.success) {
        // Check if the result has the expected structure
        if (!result.data || !result.data.banks || !Array.isArray(result.data.banks)) {
          console.error('Invalid result structure: missing or invalid banks array');

          // Create a fallback structure with empty data
          console.log('Creating fallback data structure');
          result.data = {
            banks: [{
              bank: 'DEFAULT BANK',
              employees: []
            }],
            summary: {
              totalEmployees: 0,
              preAudited: 0,
              notPreAudited: 0,
              preAuditedPercent: 0,
              notPreAuditedPercent: 0,
              totalNetPay: 0,
              totalAllowances: 0,
              totalAwards: 0,
              grandTotal: 0
            }
          };

          // Show a warning to the user
          if (processingDetailsBox) {
            addProcessingMessage(processingDetailsBox, 'WARNING: No valid data structure returned from backend. Using empty data structure.');
          }
        }

        if (!result.data.summary || typeof result.data.summary !== 'object') {
          console.error('Invalid result structure: missing or invalid summary object');

          // Create a fallback summary
          console.log('Creating fallback summary object');
          result.data.summary = {
            totalEmployees: 0,
            preAudited: 0,
            notPreAudited: 0,
            preAuditedPercent: 0,
            notPreAuditedPercent: 0,
            totalNetPay: 0,
            totalAllowances: 0,
            totalAwards: 0,
            grandTotal: 0
          };

          // Show a warning to the user
          if (processingDetailsBox) {
            addProcessingMessage(processingDetailsBox, 'WARNING: No valid summary returned from backend. Using empty summary.');
          }
        }

        // Store the data for later use
        bankAdviceData = result.data.banks || [];
        verificationResults = result.data.summary || {};

        // Check if we have valid data
        if (!bankAdviceData || !Array.isArray(bankAdviceData) || bankAdviceData.length === 0) {
          console.error('No bank advice data received from backend');

          // Create a fallback bank with empty employees array
          console.log('Creating fallback bank with empty employees array');
          bankAdviceData = [{
            bank: 'DEFAULT BANK',
            employees: []
          }];

          // Show a warning to the user
          if (processingDetailsBox) {
            addProcessingMessage(processingDetailsBox, 'WARNING: No bank advice data received from backend. Using empty data.');
          }
        } else {
          console.log('Bank advice data stored:', bankAdviceData);
          console.log('Verification results stored:', verificationResults);
        }
      } else {
        // If the backend returned an error, create a fallback structure
        console.error('Backend returned error:', result.error);

        // Create a fallback structure with empty data
        bankAdviceData = [{
          bank: 'ERROR',
          employees: []
        }];

        verificationResults = {
          totalEmployees: 0,
          preAudited: 0,
          notPreAudited: 0,
          preAuditedPercent: 0,
          notPreAuditedPercent: 0,
          totalNetPay: 0,
          totalAllowances: 0,
          totalAwards: 0,
          grandTotal: 0,
          error: result.error
        };

        // Show an error message to the user
        if (processingDetailsBox) {
          addProcessingMessage(processingDetailsBox, `ERROR: ${result.error}`);
          addProcessingMessage(processingDetailsBox, 'Using empty data structure for display.');
        }

        // Don't throw an error, just continue with the empty data
        console.log('Using fallback data due to backend error');
      }
    } catch (apiError) {
      console.error('Error calling processBankAdvice API:', apiError);

      // Create a fallback structure with empty data
      bankAdviceData = [{
        bank: 'ERROR',
        employees: []
      }];

      verificationResults = {
        totalEmployees: 0,
        preAudited: 0,
        notPreAudited: 0,
        preAuditedPercent: 0,
        notPreAuditedPercent: 0,
        totalNetPay: 0,
        totalAllowances: 0,
        totalAwards: 0,
        grandTotal: 0,
        error: apiError.message
      };

      // Show an error message to the user
      if (processingDetailsBox) {
        addProcessingMessage(processingDetailsBox, `ERROR: ${apiError.message}`);
        addProcessingMessage(processingDetailsBox, 'Using empty data structure for display.');
      }

      // Don't throw an error, just continue with the empty data
      console.log('Using fallback data due to API error');
    }

    // Display results
    displayResults();
  } catch (error) {
    console.error('Error processing Bank Advice:', error);

    if (bankAdviserResults) {
      // Create a more detailed error message
      let errorDetails = '';

      if (error.message) {
        // Check for common error patterns
        if (error.message.includes('Missing required columns') || error.message.includes('Missing required column mappings')) {
          errorDetails = `
            <p>The Excel file is missing required columns. Please check your column mapping.</p>
            <p>Make sure all required columns are mapped:</p>
            <ul>
              <li>EMPLOYEE NO.</li>
              <li>EMPLOYEE NAME</li>
              <li>ACCOUNT NO.</li>
              <li>NET PAY</li>
              <li>TOTAL</li>
              <li>BRANCH</li>
            </ul>
            <p>Note: ALL ALLOWANCES typically maps to 'LV' in Excel, and AWARDS & GRANTS typically maps to 'LSTG' in Excel.</p>
            <p>These values are also verified against separate PDF files. If the PDF files are not provided, verification will be based on Excel values only.</p>
          `;
        } else if (error.message.includes('file not found') || error.message.includes('invalid')) {
          errorDetails = `
            <p>One or more required files could not be found or are invalid:</p>
            <ul>
              <li>Bank Advice Excel file: ${bankAdviceFilePath || 'Not selected'}</li>
              <li>Payslip PDF files: ${payslipFilesPath ? (Array.isArray(payslipFilesPath) ? `${payslipFilesPath.length} files selected` : payslipFilesPath) : 'Not selected'}</li>
              <li>ALL ALLOWANCES PDF file: ${allowancesFilePath || 'Not selected'}</li>
              <li>AWARDS & GRANTS PDF file: ${awardsFilePath || 'Not selected'}</li>
            </ul>
            <p>Please make sure all required files are selected and valid.</p>
          `;
        } else {
          errorDetails = `<p>${error.message}</p>`;
        }
      } else {
        errorDetails = '<p>An unexpected error occurred. Please try again.</p>';
      }

      bankAdviserResults.innerHTML = `
        <div class="error-container">
          <h4>Error Processing Bank Advice</h4>
          <div class="error-details">
            ${errorDetails}
          </div>
          <p class="error-help">If the problem persists, please contact support.</p>
        </div>
      `;
    }
  }
}

// Helper function to add processing messages
function addProcessingMessage(container, message) {
  const messageElement = document.createElement('div');
  messageElement.className = 'processing-detail-entry';
  messageElement.textContent = message;
  container.appendChild(messageElement);
  container.scrollTop = container.scrollHeight;
}

// Helper function to simulate delay
function simulateDelay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper function to display error messages
function displayError(message) {
  const bankAdviserResults = document.getElementById('bank-adviser-results');
  if (bankAdviserResults) {
    bankAdviserResults.innerHTML = `
      <div class="error-container">
        <h4>Error Processing Bank Advice</h4>
        <div class="error-details">
          <p>${message}</p>
        </div>
        <p class="error-help">Please check your inputs and try again.</p>
      </div>
    `;
  }
}

// Function to show a dialog for manual column entry
function showManualColumnEntryDialog() {
  // Create a modal dialog
  const modalHtml = `
    <div id="manual-columns-modal" style="
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.4);
    ">
      <div style="
        background-color: white;
        margin: 10% auto;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        width: 80%;
        max-width: 600px;
      ">
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 10px;
          border-bottom: 1px solid #ddd;
        ">
          <h3 style="margin: 0;">Enter Excel Columns Manually</h3>
          <span id="close-manual-columns-modal" style="
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
          ">&times;</span>
        </div>

        <p>Enter the column names from your Excel file, separated by commas:</p>
        <textarea id="manual-columns-input" style="
          width: 100%;
          height: 100px;
          padding: 10px;
          margin-bottom: 20px;
          border: 1px solid #ddd;
          border-radius: 4px;
        " placeholder="e.g., No, Employee Name, Account No, Net Salary, LV, LSTG, TOTAL, Branch"></textarea>

        <p>Example column names:</p>
        <pre style="
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
        ">No, Employee Name, Account No, Net Salary, LV, LSTG, TOTAL, Branch</pre>

        <div style="
          display: flex;
          justify-content: flex-end;
          margin-top: 20px;
        ">
          <button id="cancel-manual-columns-btn" style="
            padding: 8px 16px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f5f5f5;
            cursor: pointer;
          ">Cancel</button>
          <button id="save-manual-columns-btn" style="
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #4caf50;
            color: white;
            cursor: pointer;
          ">Save Columns</button>
        </div>
      </div>
    </div>
  `;

  // Add the modal to the document
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Get the modal elements
  const modal = document.getElementById('manual-columns-modal');
  const closeBtn = document.getElementById('close-manual-columns-modal');
  const cancelBtn = document.getElementById('cancel-manual-columns-btn');
  const saveBtn = document.getElementById('save-manual-columns-btn');
  const input = document.getElementById('manual-columns-input');

  // Set default value to current columns if available
  if (bankAdviceColumns.length > 0) {
    input.value = bankAdviceColumns.join(', ');
  }

  // Function to close the modal
  const closeModal = () => {
    modal.remove();
  };

  // Add event listeners
  closeBtn.addEventListener('click', closeModal);
  cancelBtn.addEventListener('click', closeModal);

  saveBtn.addEventListener('click', () => {
    // Get the input value and split by commas
    const columnsText = input.value.trim();
    if (columnsText) {
      // Split by commas and trim each column name
      const columns = columnsText.split(',').map(col => col.trim()).filter(col => col);

      if (columns.length > 0) {
        // Update the bankAdviceColumns array
        bankAdviceColumns = columns;
        console.log('Manually entered columns:', bankAdviceColumns);

        // Update the column mapping UI
        updateColumnMappingUI();

        // Show success message
        const bankAdviceFileError = document.getElementById('bank-advice-file-error');
        if (bankAdviceFileError) {
          bankAdviceFileError.textContent = `Successfully added ${columns.length} columns manually`;
          bankAdviceFileError.style.color = '#4caf50'; // Green color for success

          // Clear the message after 3 seconds
          setTimeout(() => {
            if (bankAdviceFileError) {
              bankAdviceFileError.textContent = '';
            }
          }, 3000);
        }
      } else {
        alert('Please enter at least one column name');
        return;
      }
    } else {
      alert('Please enter column names');
      return;
    }

    // Close the modal
    closeModal();
  });

  // Allow closing the modal by clicking outside
  modal.addEventListener('click', (event) => {
    if (event.target === modal) {
      closeModal();
    }
  });

  // Focus the input field
  input.focus();
}

// No placeholder data generation - system must use real data

// Function to display the results
function displayResults() {
  const bankAdviserResults = document.getElementById('bank-adviser-results');

  if (!bankAdviserResults) {
    return;
  }

  // Create results HTML
  let resultsHtml = `
    <div class="success-container">
      <h4>Bank Advice Verification Completed</h4>
      <p>Total Employees: ${verificationResults.totalEmployees}</p>
      <p>Pre-Audited: ${verificationResults.preAudited} (${verificationResults.preAuditedPercent}%)</p>
      <p>Not Pre-Audited: ${verificationResults.notPreAudited} (${verificationResults.notPreAuditedPercent}%)</p>
      <p>Total Net Pay: ${verificationResults.totalNetPay.toFixed(2)}</p>
      <p>Total Allowances: ${verificationResults.totalAllowances.toFixed(2)}</p>
      <p>Total Awards & Grants: ${verificationResults.totalAwards.toFixed(2)}</p>
      <p>Grand Total: ${verificationResults.grandTotal.toFixed(2)}</p>
    </div>

    <div class="bank-adviser-results-table-container">
      <table class="bank-adviser-results-table">
        <thead>
          <tr>
            <th>EMPLOYEE NO.</th>
            <th>EMPLOYEE NAME</th>
            <th>ACCOUNT NO.</th>
            <th>NET PAY</th>
            <th>ALL ALLOWANCES</th>
            <th>AWARDS & GRANTS</th>
            <th>TOTAL</th>
            <th>BRANCH</th>
            <th>REMARKS</th>
            <th>BREAKDOWN</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each bank and employee
  bankAdviceData.forEach(bank => {
    // Add bank header
    resultsHtml += `
      <tr class="bank-group-header">
        <td colspan="10">${bank.bank}</td>
      </tr>
    `;

    // Add employee rows
    bank.employees.forEach(employee => {
      resultsHtml += `
        <tr class="${employee.verified ? 'pre-audited' : 'not-pre-audited'}" data-employee-no="${employee.employeeNo}">
          <td>${employee.employeeNo}</td>
          <td>${employee.employeeName}</td>
          <td>${employee.accountNo}</td>
          <td>${employee.netPay.toFixed(2)}</td>
          <td>${employee.allowances.toFixed(2)}</td>
          <td>${employee.awards.toFixed(2)}</td>
          <td>${employee.total.toFixed(2)}</td>
          <td>${employee.branch}</td>
          <td>${employee.verified ? 'PRE-AUDITED' : 'NOT-PREAUDITED'}</td>
          <td>${employee.breakdown}</td>
        </tr>
      `;
    });

    // Calculate bank totals
    const bankNetPay = bank.employees.reduce((sum, emp) => sum + emp.netPay, 0);
    const bankAllowances = bank.employees.reduce((sum, emp) => sum + emp.allowances, 0);
    const bankAwards = bank.employees.reduce((sum, emp) => sum + emp.awards, 0);
    const bankTotal = bank.employees.reduce((sum, emp) => sum + emp.total, 0);

    // Add bank total row
    resultsHtml += `
      <tr class="bank-group-total">
        <td colspan="3">BANK TOTAL</td>
        <td>${bankNetPay.toFixed(2)}</td>
        <td>${bankAllowances.toFixed(2)}</td>
        <td>${bankAwards.toFixed(2)}</td>
        <td>${bankTotal.toFixed(2)}</td>
        <td colspan="3"></td>
      </tr>
    `;
  });

  // Add grand total row
  resultsHtml += `
      <tr class="grand-total">
        <td colspan="3">GRAND TOTAL</td>
        <td>${verificationResults.totalNetPay.toFixed(2)}</td>
        <td>${verificationResults.totalAllowances.toFixed(2)}</td>
        <td>${verificationResults.totalAwards.toFixed(2)}</td>
        <td>${verificationResults.grandTotal.toFixed(2)}</td>
        <td colspan="3"></td>
      </tr>
    </tbody>
  </table>
  </div>

  <div class="action-container">
    <button id="export-bank-adviser-btn" class="primary-button">Export to Excel</button>
  </div>
  `;

  // Set the results HTML
  bankAdviserResults.innerHTML = resultsHtml;

  // Add event listener for the export button
  const exportButton = document.getElementById('export-bank-adviser-btn');
  if (exportButton) {
    exportButton.addEventListener('click', exportBankAdviserResults);
  }

  // Add event listeners for employee rows
  document.querySelectorAll('.bank-adviser-results-table tr[data-employee-no]').forEach(row => {
    row.addEventListener('click', () => {
      const employeeNo = row.getAttribute('data-employee-no');
      showEmployeeDetails(employeeNo);
    });
  });
}

// Function to show employee details
function showEmployeeDetails(employeeNo) {
  // Find the employee
  let employee = null;
  for (const bank of bankAdviceData) {
    const found = bank.employees.find(emp => emp.employeeNo === employeeNo);
    if (found) {
      employee = found;
      break;
    }
  }

  if (!employee) {
    return;
  }

  // Get verification details if available
  const verificationDetails = employee.verification_details || {
    total_verified: employee.verified,
    net_pay_verified: employee.verified,
    allowances_verified: employee.verified,
    awards_verified: employee.verified,
    bank_total: employee.total,
    calculated_total: employee.total,
    payslip_net_pay: employee.netPay,
    allowances_amount: employee.allowances,
    awards_amount: employee.awards
  };

  // Create modal HTML
  const modalHtml = `
    <div class="employee-details-modal" id="employee-details-modal">
      <div class="employee-details-content">
        <div class="employee-details-header">
          <h3>${employee.employeeName} (${employee.employeeNo})</h3>
          <span class="close-details-modal">&times;</span>
        </div>

        <div class="employee-details-section">
          <h4>Employee Information</h4>
          <table class="details-table">
            <tr>
              <th>Employee No.</th>
              <td>${employee.employeeNo}</td>
            </tr>
            <tr>
              <th>Employee Name</th>
              <td>${employee.employeeName}</td>
            </tr>
            <tr>
              <th>Account No.</th>
              <td>${employee.accountNo}</td>
            </tr>
            <tr>
              <th>Branch</th>
              <td>${employee.branch}</td>
            </tr>
          </table>
        </div>

        <div class="employee-details-section">
          <h4>Financial Information</h4>
          <table class="details-table">
            <tr>
              <th>NET PAY</th>
              <td>${employee.netPay.toFixed(2)}</td>
              <td><span class="verification-status ${verificationDetails.net_pay_verified ? 'verified' : 'not-verified'}">${verificationDetails.net_pay_verified ? 'Verified' : 'Not Verified'}</span></td>
            </tr>
            <tr>
              <th>ALL ALLOWANCES</th>
              <td>${employee.allowances.toFixed(2)}</td>
              <td><span class="verification-status ${verificationDetails.allowances_verified ? 'verified' : 'not-verified'}">${verificationDetails.allowances_verified ? 'Verified' : 'Not Verified'}</span></td>
            </tr>
            <tr>
              <th>AWARDS & GRANTS</th>
              <td>${employee.awards.toFixed(2)}</td>
              <td><span class="verification-status ${verificationDetails.awards_verified ? 'verified' : 'not-verified'}">${verificationDetails.awards_verified ? 'Verified' : 'Not Verified'}</span></td>
            </tr>
            <tr>
              <th>TOTAL</th>
              <td>${employee.total.toFixed(2)}</td>
              <td><span class="verification-status ${verificationDetails.total_verified ? 'verified' : 'not-verified'}">${verificationDetails.total_verified ? 'Verified' : 'Not Verified'}</span></td>
            </tr>
          </table>
        </div>

        <div class="employee-details-section">
          <h4>Verification Details</h4>
          <table class="details-table">
            <tr>
              <th>Bank Total</th>
              <td>${verificationDetails.bank_total.toFixed(2)}</td>
            </tr>
            <tr>
              <th>Calculated Total</th>
              <td>${verificationDetails.calculated_total.toFixed(2)}</td>
            </tr>
            <tr>
              <th>Payslip Net Pay</th>
              <td>${verificationDetails.payslip_net_pay.toFixed(2)}</td>
            </tr>
            <tr>
              <th>Extracted Allowances</th>
              <td>${verificationDetails.allowances_amount.toFixed(2)}</td>
            </tr>
            <tr>
              <th>Extracted Awards</th>
              <td>${verificationDetails.awards_amount.toFixed(2)}</td>
            </tr>
          </table>
        </div>

        <div class="employee-details-section">
          <h4>Breakdown</h4>
          <p>${employee.breakdown}</p>
        </div>
      </div>
    </div>
  `;

  // Add modal to the document
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Show the modal
  const modal = document.getElementById('employee-details-modal');
  if (modal) {
    modal.style.display = 'block';

    // Add event listener for close button
    const closeButton = modal.querySelector('.close-details-modal');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        modal.style.display = 'none';
        modal.remove();
      });
    }

    // Add event listener for clicking outside the modal
    modal.addEventListener('click', (event) => {
      if (event.target === modal) {
        modal.style.display = 'none';
        modal.remove();
      }
    });
  }
}

// Function to export Bank Adviser results
async function exportBankAdviserResults() {
  try {
    // Prepare data for export
    console.log('Preparing data for export...');
    console.log('Bank advice data:', bankAdviceData);
    console.log('Verification results:', verificationResults);

    // Make sure we have the data to export
    if (!bankAdviceData || !Array.isArray(bankAdviceData) || bankAdviceData.length === 0) {
      console.error('No bank advice data available for export');
      throw new Error('No bank advice data available for export. Please process the data first.');
    }

    // Make sure we have verification results
    if (!verificationResults || typeof verificationResults !== 'object') {
      console.error('No verification results available for export');
      throw new Error('No verification results available for export. Please process the data first.');
    }

    // Prepare the data structure for export
    const data = {
      banks: bankAdviceData,
      summary: verificationResults
    };

    console.log('Data prepared for export:', data);

    // Call the backend to export the data
    const result = await window.api.exportBankAdviser(data);

    if (result.success) {
      // Show success message
      alert(`Bank Adviser results exported successfully to:\n${result.filePath}`);

      // Open the file
      await window.api.openFile(result.filePath);
    } else {
      throw new Error(result.error || 'Failed to export Bank Adviser results');
    }
  } catch (error) {
    console.error('Error exporting Bank Adviser results:', error);
    alert(`Error exporting Bank Adviser results: ${error.message || 'An unexpected error occurred'}`);
  }
}

// Function to validate inputs
function validateInputs() {
  let isValid = true;

  // Check Bank Advice file
  if (!bankAdviceFilePath) {
    const bankAdviceFileError = document.getElementById('bank-advice-file-error');
    if (bankAdviceFileError) {
      bankAdviceFileError.textContent = 'Please select a Bank Advice Excel file';
    }
    isValid = false;
  }

  // Check Payslip files
  if (!payslipFilesPath || (Array.isArray(payslipFilesPath) && payslipFilesPath.length === 0)) {
    const payslipFilesError = document.getElementById('payslip-files-error');
    if (payslipFilesError) {
      payslipFilesError.textContent = 'Please select at least one Payslip PDF file';
    }
    isValid = false;
  }

  // Check column mapping
  const requiredColumns = ['EMPLOYEE NO.', 'EMPLOYEE NAME', 'ACCOUNT NO.', 'NET PAY', 'TOTAL', 'BRANCH'];
  const recommendedColumns = ['ALL ALLOWANCES', 'AWARDS & GRANTS']; // These are now mapped from Excel but also verified with PDFs

  // Check if we have any column mapping at all
  if (Object.keys(columnMapping).length === 0) {
    const bankAdviceFileError = document.getElementById('bank-advice-file-error');
    if (bankAdviceFileError) {
      bankAdviceFileError.textContent = 'Please select a Bank Advice Excel file to generate column mappings';
      bankAdviceFileError.style.color = '#f44336'; // Red color for error
    }
    isValid = false;
    return isValid;
  }

  // Check required columns
  const missingMappings = requiredColumns.filter(col => !columnMapping[col] || columnMapping[col] === '');

  if (missingMappings.length > 0) {
    console.error('Missing required column mappings:', missingMappings);
    const bankAdviserResults = document.getElementById('bank-adviser-results');
    if (bankAdviserResults) {
      bankAdviserResults.innerHTML = `
        <div class="error-container">
          <h4>Column Mapping Error</h4>
          <p class="error">Please map all required columns:</p>
          <ul>
            ${missingMappings.map(col => `<li>${col}</li>`).join('')}
          </ul>
          <p>Note: ALL ALLOWANCES typically maps to 'LV' in Excel, and AWARDS & GRANTS typically maps to 'LSTG' in Excel.</p>
          <p>These values are also verified against separate PDF files. If the PDF files are not provided, verification will be based on Excel values only.</p>
        </div>
      `;
    }
    isValid = false;
  }

  // Log column mapping for debugging
  console.log('Column mapping validation:');
  console.log('Required columns:', requiredColumns);
  console.log('Recommended columns:', recommendedColumns);
  console.log('Current mapping:', columnMapping);

  // Check ALL ALLOWANCES file - now optional
  if (!allowancesFilePath) {
    const allowancesFileError = document.getElementById('allowances-file-error');
    if (allowancesFileError) {
      allowancesFileError.textContent = 'Note: No ALL ALLOWANCES PDF file selected. Allowances will be treated as zero.';
    }
    // Not required, so don't set isValid to false
  }

  // Check AWARDS & GRANTS file - now optional
  if (!awardsFilePath) {
    const awardsFileError = document.getElementById('awards-file-error');
    if (awardsFileError) {
      awardsFileError.textContent = 'Note: No AWARDS & GRANTS PDF file selected. Awards will be treated as zero.';
    }
    // Not required, so don't set isValid to false
  }

  return isValid;
}

// Initialize the Bank Adviser when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initialize the Bank Adviser
  initBankAdviser();
});
