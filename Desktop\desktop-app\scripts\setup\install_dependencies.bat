@echo off
setlocal EnableDelayedExpansion

:: Check for admin privileges
NET SESSION >nul 2>&1
if %errorlevel% neq 0 (
    echo This script requires administrator privileges.
    echo Please right-click on the script and select "Run as administrator".

    :: Create a VBS script to request elevation
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    del "%temp%\getadmin.vbs"
    exit /B
)

echo Installing dependencies for TEMPLAR PAYROLL AUDITOR...

REM Set installation paths
set "INSTALL_DIR=C:\TEMPLAR PAYROLL AUDITOR"
set "TESSERACT_DIR=C:\Program Files\Tesseract-OCR"

REM Check if Tesseract OCR is installed
where tesseract >nul 2>&1
if %errorlevel% neq 0 (
    echo Tesseract OCR is not installed. Installing now...

    REM Check if we have bundled Tesseract
    set "BUNDLED_TESSERACT=%~dp0resources\vendor\tesseract"
    if exist "%BUNDLED_TESSERACT%" (
        echo Found bundled Tesseract OCR. Installing from bundled package...

        REM Create Tesseract directory if it doesn't exist
        if not exist "%TESSERACT_DIR%" (
            echo Creating Tesseract directory...
            mkdir "%TESSERACT_DIR%" 2>nul
        )

        REM Copy Tesseract files from vendor directory to standard location
        echo Installing Tesseract OCR to %TESSERACT_DIR%...
        xcopy /E /I /Y "%BUNDLED_TESSERACT%\*" "%TESSERACT_DIR%"

        echo Tesseract OCR installation completed.
    ) else (
        echo No bundled Tesseract found. Downloading from internet...

        REM Create a temporary directory
        mkdir temp_install 2>nul
        cd temp_install

        REM Download Tesseract OCR installer
        echo Downloading Tesseract OCR installer...

        REM Use curl instead of PowerShell (curl is available in Windows 10 and later)
        curl -L "https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.3.20231005.exe" -o "tesseract-installer.exe"

        REM Fallback to bitsadmin if curl fails
        if %errorlevel% neq 0 (
            echo Curl failed, trying bitsadmin...
            bitsadmin /transfer "TesseractDownload" "https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.3.20231005.exe" "%CD%\tesseract-installer.exe"
        )

        REM Run the installer with silent options to ensure standard installation
        echo Installing Tesseract OCR...
        echo Please wait while Tesseract is being installed...

        REM Install Tesseract silently to the standard location
        start /B /WAIT tesseract-installer.exe /S /D=C:\Program Files\Tesseract-OCR

        REM Wait for installation to complete
        timeout /t 10 /nobreak > nul

        echo Tesseract installation should be complete.

        REM Clean up
        cd ..
        rmdir /s /q temp_install

        echo Tesseract OCR installation completed.
    )
) else (
    echo Tesseract OCR is already installed.
)

REM Add dependencies to PATH if not already there
echo Checking and adding dependencies to PATH...

REM Add Tesseract to PATH
set "TESSERACT_PATH=C:\Program Files\Tesseract-OCR"
set "TESSERACT_BIN_PATH=C:\Program Files\Tesseract-OCR\bin"

REM Check if Tesseract directory exists
if exist "%TESSERACT_PATH%" (
    echo Tesseract found at %TESSERACT_PATH%

    REM Check if main directory is in PATH
    echo %PATH% | findstr /C:"%TESSERACT_PATH%" >nul
    if %errorlevel% neq 0 (
        echo Adding Tesseract main directory to PATH...
        setx PATH "%PATH%;%TESSERACT_PATH%" /M
        echo Tesseract main directory added to PATH.
    ) else (
        echo Tesseract main directory is already in PATH.
    )

    REM Check if bin directory exists and add it to PATH if needed
    if exist "%TESSERACT_BIN_PATH%" (
        echo %PATH% | findstr /C:"%TESSERACT_BIN_PATH%" >nul
        if %errorlevel% neq 0 (
            echo Adding Tesseract bin directory to PATH...
            setx PATH "%PATH%;%TESSERACT_BIN_PATH%" /M
            echo Tesseract bin directory added to PATH.
        ) else (
            echo Tesseract bin directory is already in PATH.
        )
    )
) else (
    echo WARNING: Tesseract directory not found at %TESSERACT_PATH%
    echo Installation may have failed or used a different location.
)

REM Check if the application directory exists
if exist "%INSTALL_DIR%" (
    echo Application found at %INSTALL_DIR%

    REM Check if embedded Python exists
    set "EMBEDDED_PYTHON=%INSTALL_DIR%\resources\python-embedded"
    if exist "%EMBEDDED_PYTHON%" (
        echo Embedded Python found at %EMBEDDED_PYTHON%

        REM Check if embedded Python is in PATH
        echo %PATH% | findstr /C:"%EMBEDDED_PYTHON%" >nul
        if %errorlevel% neq 0 (
            echo Adding embedded Python to PATH...
            setx PATH "%PATH%;%EMBEDDED_PYTHON%" /M
            echo Embedded Python added to PATH.
        ) else (
            echo Embedded Python is already in PATH.
        )
    )
) else (
    echo WARNING: Application directory not found at %INSTALL_DIR%
    echo Installation may have failed or used a different location.
)

echo All dependencies installed successfully!
echo You may need to restart your computer for all changes to take effect.
pause
