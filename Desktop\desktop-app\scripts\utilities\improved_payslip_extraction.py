import os
import sys
import re
import pandas as pd

print("<PERSON><PERSON><PERSON> started. Extracting payslip data for Bank Adviser...")

class PayslipExtractor:
    """
    Class for extracting data from payslip PDFs for Bank Adviser.
    """

    def __init__(self):
        """Initialize the PayslipExtractor class."""
        self.payslip_data = {}

    def extract_payslip_data(self, payslip_file):
        """
        Extract data from payslip PDF using patterns specific to the format.

        Args:
            payslip_file (str): Path to the payslip PDF file
        """
        print(f"Extracting data from payslip file: {payslip_file}")

        # Initialize payslip data dictionary
        self.payslip_data = {}

        # If no payslip file, return early
        if not payslip_file or not os.path.exists(payslip_file):
            print(f"Payslip file not found: {payslip_file}")
            return

        try:
            # Import required modules for PDF extraction
            import PyPDF2
            
            # Extract text from the PDF
            with open(payslip_file, 'rb') as file:
                # Create a PDF reader object
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Get the number of pages
                num_pages = len(pdf_reader.pages)
                print(f"PDF has {num_pages} pages")
                
                # Process each page as a separate payslip
                for page_num in range(num_pages):
                    if page_num % 10 == 0:
                        print(f"Processing page {page_num + 1} of {num_pages}...")
                    
                    # Extract text from the page
                    page = pdf_reader.pages[page_num]
                    text = page.extract_text()
                    
                    # Extract employee number - specific pattern for this payslip format
                    # The pattern is "COP#### Employee No." or similar
                    employee_match = re.search(r'(COP\d{4})\s+Employee\s+No\.', text)
                    if not employee_match:
                        # Try alternative pattern
                        employee_match = re.search(r'(COP\d{4})\s+Empl+oyee\s+No\.', text)
                    
                    if employee_match:
                        employee_no = employee_match.group(1)
                        
                        # Extract net pay - specific pattern for this payslip format
                        # Look for "NET PAY" followed by a number
                        net_pay_match = re.search(r'NET\s+PAY\s+GROSS\s+SALARY\s+(\d{1,3}(?:,\d{3})*(?:\.\d+)?)', text)
                        if net_pay_match:
                            net_pay_str = net_pay_match.group(1).replace(',', '')
                            try:
                                net_pay = float(net_pay_str)
                            except ValueError:
                                net_pay = 0.0
                                print(f"Could not convert net pay to float: {net_pay_str}")
                        else:
                            # Try alternative pattern
                            net_pay_match = re.search(r'NET\s+PAY\s+(\d{1,3}(?:,\d{3})*(?:\.\d+)?)', text)
                            if net_pay_match:
                                net_pay_str = net_pay_match.group(1).replace(',', '')
                                try:
                                    net_pay = float(net_pay_str)
                                except ValueError:
                                    net_pay = 0.0
                                    print(f"Could not convert net pay to float: {net_pay_str}")
                            else:
                                net_pay = 0.0
                                print(f"Could not find net pay for employee {employee_no}")
                        
                        # Extract department - specific pattern for this payslip format
                        dept_match = re.search(r'([\w\s\.\-\&]+)\s+Department', text)
                        department = dept_match.group(1).strip() if dept_match else "Unknown"
                        
                        # Extract bank account details - look for SSF No. which is near the account number
                        ssf_match = re.search(r'(\d+)\s+SSF\s+No\.', text)
                        account_no = ssf_match.group(1) if ssf_match else ""
                        
                        # Extract employee name - specific pattern for this payslip format
                        name_match = re.search(r'([A-Z\s]+)\s+Employee\s+Name', text)
                        employee_name = name_match.group(1).strip() if name_match else ""
                        
                        # Store the extracted data
                        self.payslip_data[employee_no] = {
                            'EMPLOYEE NO.': employee_no,
                            'EMPLOYEE NAME': employee_name,
                            'DEPARTMENT': department,
                            'NET PAY': net_pay,
                            'ACCOUNT NO.': account_no
                        }
                        
                        print(f"Extracted data for employee {employee_no}: {employee_name}, NET PAY = {net_pay}")
                    else:
                        print(f"Could not find employee number on page {page_num + 1}")
            
            print(f"Extracted data for {len(self.payslip_data)} employees from payslip file")
            
            # Print sample of extracted data
            if self.payslip_data:
                print("\nSample of extracted data:")
                for i, (employee_no, data) in enumerate(list(self.payslip_data.items())[:5]):
                    print(f"  Employee {employee_no}: {data}")
                
                if len(self.payslip_data) > 5:
                    print(f"  ... and {len(self.payslip_data) - 5} more entries")
            else:
                print("No data was extracted!")
                
        except Exception as e:
            print(f"Error extracting data from payslip file: {str(e)}")
            import traceback
            print(traceback.format_exc())
    
    def save_to_excel(self, output_file):
        """
        Save the extracted data to an Excel file.
        
        Args:
            output_file (str): Path to the output Excel file
        """
        try:
            # Convert the dictionary to a DataFrame
            data_list = []
            for employee_no, data in self.payslip_data.items():
                data_list.append(data)
                
            df = pd.DataFrame(data_list)
            
            # Save to Excel
            df.to_excel(output_file, index=False)
            
            print(f"Successfully saved data to Excel file: {output_file}")
            print(f"Total records saved: {len(df)}")
            
        except Exception as e:
            print(f"Error saving data to Excel file: {str(e)}")
            import traceback
            print(traceback.format_exc())

def main():
    if len(sys.argv) < 2:
        print("Usage: python improved_payslip_extraction.py <payslip_pdf_file> [output_excel_file]")
        sys.exit(1)
    
    payslip_file = sys.argv[1]
    
    # Default output file is payslip_extract3.xlsx on the desktop
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    output_file = os.path.join(desktop_path, "payslip_extract3.xlsx")
    
    # If output file is provided, use it
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print(f"Payslip file: {payslip_file}")
    print(f"Output file: {output_file}")
    
    extractor = PayslipExtractor()
    extractor.extract_payslip_data(payslip_file)
    
    # Save to Excel if data was extracted
    if extractor.payslip_data:
        extractor.save_to_excel(output_file)
    else:
        print("No data to save to Excel.")

if __name__ == "__main__":
    main()
