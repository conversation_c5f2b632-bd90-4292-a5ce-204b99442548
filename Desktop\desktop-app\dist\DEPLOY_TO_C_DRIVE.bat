@echo off
echo ===================================================
echo    TEMPLAR PAYROLL AUDITOR DEPLOYMENT TOOL
echo ===================================================
echo.

REM Check for admin privileges
NET SESSION >nul 2>&1
if %errorlevel% neq 0 (
    echo This tool requires administrator privileges.
    echo Please right-click on this file and select "Run as administrator".
    echo.
    pause
    exit /B 1
)

echo This tool will deploy TEMPLAR PAYROLL AUDITOR to C:\TEMPLAR PAYROLL AUDITOR
echo.
echo WARNING: If the directory already exists, it will be overwritten.
echo.
set /p CONFIRM=Are you sure you want to continue? (Y/N): 

if /i "%CONFIRM%" neq "Y" (
    echo.
    echo Deployment cancelled.
    pause
    exit /B 0
)

echo.
echo Deploying TEMPLAR PAYROLL AUDITOR...

REM Set deployment path
set "DEPLOY_DIR=C:\TEMPLAR PAYROLL AUDITOR"

REM Create deployment directory if it doesn't exist
if not exist "%DEPLOY_DIR%" (
    echo Creating deployment directory...
    mkdir "%DEPLOY_DIR%"
) else (
    echo Cleaning existing deployment directory...
    rmdir /s /q "%DEPLOY_DIR%"
    mkdir "%DEPLOY_DIR%"
)

REM Copy application files
echo Copying application files...
xcopy /E /I /Y "TEMPLAR PAYROLL AUDITOR-win32-x64\*" "%DEPLOY_DIR%"

echo.
echo ===================================================
echo    DEPLOYMENT COMPLETE!
echo ===================================================
echo.
echo TEMPLAR PAYROLL AUDITOR has been deployed to:
echo %DEPLOY_DIR%
echo.
echo To create desktop shortcuts and install dependencies,
echo run the TEMPLAR_PAYROLL_AUDITOR_INSTALLER.bat file.
echo.
pause
