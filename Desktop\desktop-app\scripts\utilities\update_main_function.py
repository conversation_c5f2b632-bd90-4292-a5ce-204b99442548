#!/usr/bin/env python3
"""
<PERSON>ript to update the main function in improved_payroll_parser.py to use the month and year parameters
"""

import re
import os

# Path to the improved_payroll_parser.py file
parser_path = os.path.join('..', 'backend', 'improved_payroll_parser.py')

# Read the original file
with open(parser_path, 'r') as f:
    content = f.read()

# Find the main function
main_function_pattern = r"def main\(prev_file, curr_file, output_dir, id_field=\"employee_id\"\):(.*?)return comparison_data"
main_function_match = re.search(main_function_pattern, content, re.DOTALL)

if main_function_match:
    main_function = main_function_match.group(0)
    
    # Update the main function to pass month and year parameters to the report generator
    updated_main_function = main_function.replace(
        "def main(prev_file, curr_file, output_dir, id_field=\"employee_id\"):",
        "def main(prev_file, curr_file, output_dir, id_field=\"employee_id\", current_month=None, current_year=None, previous_month=None, previous_year=None):"
    )
    
    # Find where the report generator is called
    report_generator_pattern = r"(comparison_data = compare_payrolls\(prev_data, curr_data, id_field\))"
    updated_main_function = re.sub(
        report_generator_pattern,
        r"\1\n\n    # Pass month and year information to the report generator\n    if current_month and current_year and previous_month and previous_year:\n        print(f\"Using month/year info: Current: {current_month} {current_year}, Previous: {previous_month} {previous_year}\")",
        updated_main_function
    )
    
    # Update the content
    updated_content = content.replace(main_function, updated_main_function)
    
    # Update the if __name__ == "__main__" section to pass the command line arguments
    if_main_pattern = r"(args = parser\.parse_args\(\).*?if args\.command == 'compare':\s+)(main\(args\.prev_file, args\.curr_file, args\.output_dir, args\.id_field\))"
    updated_content = re.sub(
        if_main_pattern,
        r"\1main(args.prev_file, args.curr_file, args.output_dir, args.id_field, args.current_month, args.current_year, args.previous_month, args.previous_year)",
        updated_content,
        flags=re.DOTALL
    )
    
    # Write the updated content back to the file
    with open(parser_path, 'w') as f:
        f.write(updated_content)
    
    print(f"Successfully updated the main function in {parser_path}")
else:
    print("Could not find the main function in the file")
