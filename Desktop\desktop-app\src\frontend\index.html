<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>TEMPLAR SYSTEMS Payroll Auditor</title>
  <link rel="stylesheet" href="styles.css">
  <!-- Add Font Awesome for icons and animations -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Inline splash screen styles to ensure they load immediately */
    #splash-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #1a237e;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.5s ease-in-out;
    }

    .splash-content {
      text-align: center;
      color: white;
    }

    .splash-icon {
      width: 150px;
      height: 150px;
      margin-bottom: 30px;
      animation: pulse 2s infinite;
    }

    .splash-title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .splash-subtitle {
      font-size: 20px;
      margin-bottom: 30px;
    }

    .loading-bar {
      width: 300px;
      height: 20px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      margin: 20px auto;
      overflow: hidden;
    }

    .loading-progress {
      height: 100%;
      width: 0%;
      background-color: #4caf50;
      border-radius: 10px;
      transition: width 0.5s ease-in-out;
    }

    .loading-text {
      font-size: 18px;
      margin-top: 15px;
      opacity: 0.8;
    }

    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
  </style>
</head>
<body>
  <!-- Inline Splash Screen -->
  <div id="splash-screen">
    <div class="splash-content">
      <img src="assets/icon.png" alt="Templar Systems" class="splash-icon">
      <h1 class="splash-title">TEMPLAR SYSTEMS</h1>
      <h2 class="splash-subtitle">Payroll Auditor</h2>
      <div class="loading-bar">
        <div class="loading-progress" id="loading-progress"></div>
      </div>
      <p class="loading-text" id="loading-text">Starting application...</p>
    </div>
  </div>

  <script>
    // Immediately start animating the splash screen
    // Run this code immediately without waiting for DOMContentLoaded
    (function() {
      // Set initial progress immediately
      const loadingProgress = document.getElementById('loading-progress');
      const loadingText = document.getElementById('loading-text');

      if (loadingProgress) loadingProgress.style.width = '30%';
      if (loadingText) loadingText.textContent = 'Starting application...';

      // Animate the progress bar quickly
      setTimeout(() => {
        if (loadingProgress) loadingProgress.style.width = '60%';
        if (loadingText) loadingText.textContent = 'Loading interface...';
      }, 200);

      setTimeout(() => {
        if (loadingProgress) loadingProgress.style.width = '100%';
        if (loadingText) loadingText.textContent = 'Ready!';
      }, 400);

      // Hide the splash screen quickly
      setTimeout(() => {
        const splashScreen = document.getElementById('splash-screen');
        if (splashScreen) {
          splashScreen.style.opacity = '0';

          // Remove from DOM after fade out
          setTimeout(() => {
            if (splashScreen) splashScreen.style.display = 'none';
          }, 300);
        }
      }, 800);
    })();
  </script>

  <div class="app-container">
    <header class="app-header">
      <div class="title-container">
        <h1>TEMPLAR SYSTEMS</h1>
        <h2>Payroll Auditor</h2>
      </div>
    </header>

    <nav class="app-nav">
      <button class="nav-button" id="home-btn">Home</button>
      <button class="nav-button active" id="upload-btn">Upload Files</button>
      <button class="nav-button" id="comparison-btn">Payroll Audit</button>
      <button class="nav-button" id="reports-btn">Report Manager</button>
      <button class="nav-button" id="sorter-btn">PDF Sorter</button>
      <button class="nav-button" id="builder-btn">Data Builder</button>
      <button class="nav-button" id="dictionary-btn">Dictionary Manager</button>
      <button class="nav-button" id="bank-adviser-btn">Bank Adviser</button>
    </nav>

    <main class="app-content">
      <div class="content-container" id="upload-content">
        <div class="payroll-container">
          <div class="payroll-section">
            <h3>Current Payroll</h3>
            <div class="file-selector">
              <p>Select Current Payroll PDF</p>
              <div class="file-input-container">
                <input type="text" id="current-file-path" readonly>
                <button id="current-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="current-file-error"></p>
            </div>
            <div class="month-selector">
              <label for="current-month">Month:</label>
              <select id="current-month">
                <option value="January">January</option>
                <option value="February">February</option>
                <option value="March">March</option>
                <option value="April">April</option>
                <option value="May" selected>May</option>
                <option value="June">June</option>
                <option value="July">July</option>
                <option value="August">August</option>
                <option value="September">September</option>
                <option value="October">October</option>
                <option value="November">November</option>
                <option value="December">December</option>
              </select>
              <label for="current-year">Year:</label>
              <select id="current-year">
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025" selected>2025</option>
                <option value="2026">2026</option>
                <option value="2027">2027</option>
                <option value="2028">2028</option>
                <option value="2029">2029</option>
                <option value="2030">2030</option>
              </select>
            </div>
          </div>

          <div class="payroll-section">
            <h3>Previous Payroll</h3>
            <div class="file-selector">
              <p>Select Previous Payroll PDF</p>
              <div class="file-input-container">
                <input type="text" id="previous-file-path" readonly>
                <button id="previous-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="previous-file-error"></p>
            </div>
            <div class="month-selector">
              <label for="previous-month">Month:</label>
              <select id="previous-month">
                <option value="January">January</option>
                <option value="February">February</option>
                <option value="March">March</option>
                <option value="April" selected>April</option>
                <option value="May">May</option>
                <option value="June">June</option>
                <option value="July">July</option>
                <option value="August">August</option>
                <option value="September">September</option>
                <option value="October">October</option>
                <option value="November">November</option>
                <option value="December">December</option>
              </select>
              <label for="previous-year">Year:</label>
              <select id="previous-year">
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025" selected>2025</option>
                <option value="2026">2026</option>
                <option value="2027">2027</option>
                <option value="2028">2028</option>
                <option value="2029">2029</option>
                <option value="2030">2030</option>
              </select>
            </div>
          </div>
        </div>

        <div class="payroll-container">
          <div class="payroll-section">
            <h3>Report Signature</h3>
            <div class="signature-container compact">
              <div class="signature-row">
                <div class="signature-field">
                  <label for="report-name">Name:</label>
                  <input type="text" id="report-name" placeholder="Enter your full name">
                  <p class="error-message" id="report-name-error"></p>
                </div>
                <div class="signature-field">
                  <label for="report-designation">Designation:</label>
                  <input type="text" id="report-designation" placeholder="Enter your designation">
                  <p class="error-message" id="report-designation-error"></p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="action-container">
          <button id="start-audit-btn">Start Payroll Audit</button>
        </div>
      </div>

      <div class="content-container hidden" id="comparison-content">
        <h3>Payroll Audit Results</h3>
        <div id="comparison-results"></div>
      </div>

      <div class="content-container hidden" id="home-content">
        <h3>Welcome to Templar Payroll Auditor</h3>
        <p>Use the navigation buttons above to access different features of the application.</p>
      </div>

      <div class="content-container hidden" id="reports-content">
        <h2>Report Manager</h2>
        <p class="description">View and download reports from all operations.</p>

        <div class="reports-tabs">
          <button class="report-tab-button active" id="comparison-reports-tab">Payroll Audit Reports</button>
          <button class="report-tab-button" id="sorted-pdf-tab">Sorted PDFs</button>
          <button class="report-tab-button" id="data-tables-tab">Data Tables</button>
        </div>

        <div class="report-tab-content active" id="comparison-reports-content">
          <h4>Payroll Audit Reports</h4>
          <p class="description">Available formats: Excel, Word, PDF</p>
          <div id="comparison-reports-list" class="reports-list">
            <p>No payroll audit reports available yet.</p>
          </div>
        </div>

        <div class="report-tab-content" id="sorted-pdf-content">
          <h4>Sorted PDF Files</h4>
          <p class="description">Available format: PDF</p>
          <div id="sorted-pdf-list" class="reports-list">
            <p>No sorted PDF files available yet.</p>
          </div>
        </div>

        <div class="report-tab-content" id="data-tables-content">
          <h4>Data Tables</h4>
          <p class="description">Available format: Excel</p>
          <div id="data-tables-list" class="reports-list">
            <p>No data tables available yet.</p>
          </div>
        </div>

        <div id="reports-container" class="reports-container">
          <!-- Reports will be added here dynamically -->
        </div>
      </div>

      <div class="content-container hidden" id="sorter-content">
        <h2>PDF Sorter</h2>
        <p class="description">Sort and rearrange a PDF file based on a selected unique identifier.</p>

        <div class="payroll-container">
          <div class="payroll-section">
            <h3>PDF File to Sort</h3>
            <div class="file-selector">
              <p>Select PDF File</p>
              <div class="file-input-container">
                <input type="text" id="sort-input-file" readonly>
                <button id="sort-input-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="sort-input-file-error"></p>
            </div>
          </div>

          <div class="payroll-section">
            <h3>Sort By</h3>
            <div class="sort-selector">
              <select id="sort-id-field-select">
                <option value="employee_id" selected>Employee No.</option>
                <option value="name">Employee Name</option>
                <option value="department">Department</option>
                <option value="section">Section</option>
                <option value="job_title">Job Title</option>
              </select>
              <p class="field-description">Select the field to use for sorting the PDF.</p>
            </div>
          </div>
        </div>

        <div class="action-container">
          <button id="start-sort-btn">Sort PDF</button>
        </div>

        <div id="sort-results"></div>
      </div>

      <div class="content-container hidden" id="builder-content">
        <h2>Data Builder</h2>
        <p class="description">Build a data table from a PDF file with selected fields as columns.</p>

        <div class="payroll-container">
          <div class="payroll-section">
            <h3>PDF File to Process</h3>
            <div class="file-selector">
              <p>Select PDF File</p>
              <div class="file-input-container">
                <input type="text" id="build-input-file" readonly>
                <button id="build-input-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="build-input-file-error"></p>
            </div>
          </div>
        </div>

        <div class="fields-container">
          <h3>Select Fields to Include</h3>

          <div class="fields-tabs">
            <button class="fields-tab-button active" id="personal-details-fields-tab">Personal Details</button>
            <button class="fields-tab-button" id="earnings-fields-tab">Earnings</button>
            <button class="fields-tab-button" id="deductions-fields-tab">Deductions</button>
            <button class="fields-tab-button" id="employers-contribution-fields-tab">Employers Contribution</button>
            <button class="fields-tab-button" id="loans-fields-tab">Loans</button>
            <button class="fields-tab-button" id="bank-details-fields-tab">Bank Details</button>
            <button class="fields-tab-button" id="general-fields-tab">General</button>
          </div>

          <!-- Personal Details Section -->
          <div class="fields-tab-content active" id="personal-details-fields-content">
            <div class="section-header">
              <h4>PERSONAL DETAILS FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-personal-details" class="select-all-button">Select All</button>
                <button id="deselect-all-personal-details" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="personal-details-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Earnings Section -->
          <div class="fields-tab-content" id="earnings-fields-content">
            <div class="section-header">
              <h4>EARNINGS FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-earnings" class="select-all-button">Select All</button>
                <button id="deselect-all-earnings" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="earnings-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Deductions Section -->
          <div class="fields-tab-content" id="deductions-fields-content">
            <div class="section-header">
              <h4>DEDUCTIONS FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-deductions" class="select-all-button">Select All</button>
                <button id="deselect-all-deductions" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="deductions-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Employers Contribution Section -->
          <div class="fields-tab-content" id="employers-contribution-fields-content">
            <div class="section-header">
              <h4>EMPLOYERS CONTRIBUTION FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-employers-contribution" class="select-all-button">Select All</button>
                <button id="deselect-all-employers-contribution" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="employers-contribution-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Loans Section -->
          <div class="fields-tab-content" id="loans-fields-content">
            <div class="section-header">
              <h4>LOANS FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-loans" class="select-all-button">Select All</button>
                <button id="deselect-all-loans" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="loans-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- Bank Details Section -->
          <div class="fields-tab-content" id="bank-details-fields-content">
            <div class="section-header">
              <h4>EMPLOYEE BANK DETAILS FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-bank-details" class="select-all-button">Select All</button>
                <button id="deselect-all-bank-details" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="bank-details-fields">
                  <!-- Items will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <!-- General Fields Section -->
          <div class="fields-tab-content" id="general-fields-content">
            <div class="section-header">
              <h4>GENERAL FIELDS</h4>
              <div class="section-actions">
                <button id="select-all-general" class="select-all-button">Select All</button>
                <button id="deselect-all-general" class="deselect-all-button">Deselect All</button>
              </div>
            </div>
            <div class="fields-table-container">
              <table class="fields-table">
                <thead>
                  <tr>
                    <th>LINE ITEM</th>
                    <th>INCLUDE IN TABLE</th>
                  </tr>
                </thead>
                <tbody id="general-fields">
                  <tr>
                    <td>Month</td>
                    <td>
                      <label class="toggle-switch">
                        <input type="checkbox" class="toggle-input field-selector" data-field="month" data-section="general">
                        <span class="toggle-slider"></span>
                      </label>
                    </td>
                  </tr>
                  <tr>
                    <td>Year</td>
                    <td>
                      <label class="toggle-switch">
                        <input type="checkbox" class="toggle-input field-selector" data-field="year" data-section="general">
                        <span class="toggle-slider"></span>
                      </label>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div class="action-container">
          <button id="start-build-btn">Build Data Table</button>
        </div>

        <div id="build-results"></div>
      </div>

      <div class="content-container hidden" id="bank-adviser-content">
        <h2>Bank Adviser</h2>
        <p class="description">Standardize and verify bank advice data against payslips, allowances, and awards.</p>

        <div class="payroll-container">
          <div class="payroll-section">
            <h3>Bank Advice Excel File</h3>
            <div class="file-selector">
              <p>Select Bank Advice Excel File</p>
              <div class="file-input-container">
                <input type="text" id="bank-advice-file-path" readonly>
                <button id="bank-advice-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="bank-advice-file-error"></p>
            </div>
          </div>

          <div class="payroll-section">
            <h3>Payslip PDFs</h3>
            <div class="file-selector">
              <p>Select Payslip PDFs</p>
              <div class="file-input-container">
                <input type="text" id="payslip-files-path" readonly>
                <button id="payslip-files-browse-btn">Browse</button>
              </div>
              <p class="error-message" id="payslip-files-error"></p>
            </div>
          </div>
        </div>

        <div class="payroll-container">
          <div class="payroll-section">
            <h3>ALL ALLOWANCES PDF (Optional)</h3>
            <div class="file-selector">
              <p>Select ALL ALLOWANCES PDF if available</p>
              <div class="file-input-container">
                <input type="text" id="allowances-file-path" readonly>
                <button id="allowances-browse-btn">Browse</button>
              </div>
              <p class="helper-text">Only required if employees received allowances this month</p>
              <p class="error-message" id="allowances-file-error"></p>
            </div>
          </div>

          <div class="payroll-section">
            <h3>AWARDS & GRANTS PDF (Optional)</h3>
            <div class="file-selector">
              <p>Select AWARDS & GRANTS PDF if available</p>
              <div class="file-input-container">
                <input type="text" id="awards-file-path" readonly>
                <button id="awards-browse-btn">Browse</button>
              </div>
              <p class="helper-text">Only required if employees received awards or grants this month</p>
              <p class="error-message" id="awards-file-error"></p>
            </div>
          </div>
        </div>

        <!-- Excel Structure Settings Section (Initially Hidden) -->
        <div class="excel-structure-section" id="excel-structure-section" style="display: none; margin-top: 15px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; margin-bottom: 20px;">
          <h3 style="margin-top: 0;">Excel Structure Settings</h3>
          <p class="description">These settings were automatically detected from your Excel file. You can adjust them if needed.</p>

          <div style="display: flex; gap: 20px; margin-bottom: 15px; flex-wrap: wrap;">
            <div>
              <label for="header-row">Header Row Number:</label>
              <select id="header-row" style="width: 100px; padding: 5px;">
                <option value="1">Row 1</option>
                <option value="2">Row 2</option>
                <option value="3">Row 3</option>
                <option value="4" selected>Row 4</option>
                <option value="5">Row 5</option>
                <option value="6">Row 6</option>
                <option value="7">Row 7</option>
                <option value="8">Row 8</option>
              </select>
            </div>

            <div>
              <label for="data-start-row">Data Start Row:</label>
              <select id="data-start-row" style="width: 100px; padding: 5px;">
                <option value="2">Row 2</option>
                <option value="3">Row 3</option>
                <option value="4">Row 4</option>
                <option value="5" selected>Row 5</option>
                <option value="6">Row 6</option>
                <option value="7">Row 7</option>
                <option value="8">Row 8</option>
                <option value="9">Row 9</option>
              </select>
            </div>

            <div>
              <label for="first-column">First Column:</label>
              <select id="first-column" style="width: 100px; padding: 5px;">
                <option value="A" selected>Column A</option>
                <option value="B">Column B</option>
                <option value="C">Column C</option>
                <option value="D">Column D</option>
                <option value="E">Column E</option>
              </select>
            </div>
          </div>

          <button id="reload-columns-btn" style="padding: 8px 15px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Reload Columns with These Settings
          </button>
        </div>

        <!-- Column Mapping Section (Initially Hidden) -->
        <div class="column-mapping-section" id="column-mapping-section" style="display: none; margin-top: 15px;">
          <h3>Column Mapping</h3>
          <p class="description">Map columns from the Bank Advice Excel file to standardized column names.</p>
          <div id="column-mapping-table-container">
            <table class="column-mapping-table">
              <thead>
                <tr>
                  <th>Standard Column</th>
                  <th>Excel Column</th>
                </tr>
              </thead>
              <tbody id="column-mapping-body">
                <!-- Column mapping rows will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <div class="action-container">
          <button id="start-bank-adviser-btn">Process Bank Advice</button>
        </div>

        <div id="bank-adviser-results"></div>
      </div>

      <div class="content-container hidden" id="dictionary-content">
        <h2>Dictionary Manager</h2>
        <p class="description">Manage payslip dictionary for improved extraction accuracy.</p>

        <div class="dictionary-actions">
          <button id="import-dictionary-btn" class="action-button">
            <i class="fas fa-file-import"></i> Import from Excel
          </button>
          <button id="export-dictionary-btn" class="action-button">
            <i class="fas fa-file-export"></i> Export to Excel
          </button>
          <button id="reset-dictionary-btn" class="action-button warning">
            <i class="fas fa-undo"></i> Reset to Defaults
          </button>
        </div>

        <div class="dictionary-tabs">
          <button class="dictionary-tab-button active" id="personal-details-tab">Personal Details</button>
          <button class="dictionary-tab-button" id="earnings-tab">Earnings</button>
          <button class="dictionary-tab-button" id="deductions-tab">Deductions</button>
          <button class="dictionary-tab-button" id="employers-contribution-tab">Employers Contribution</button>
          <button class="dictionary-tab-button" id="loans-tab">Loans</button>
          <button class="dictionary-tab-button" id="bank-details-tab">Bank Details</button>
          <button class="dictionary-tab-button" id="add-section-tab">+ Add Section</button>
        </div>

        <!-- Personal Details Section -->
        <div class="dictionary-tab-content active" id="personal-details-content">
          <div class="section-header">
            <h3>PERSONAL DETAILS SECTION</h3>
            <div class="section-actions">
              <button id="add-personal-details-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="personal-details-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Earnings Section -->
        <div class="dictionary-tab-content" id="earnings-content">
          <div class="section-header">
            <h3>EARNINGS SECTION</h3>
            <div class="section-actions">
              <button id="add-earnings-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="earnings-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Deductions Section -->
        <div class="dictionary-tab-content" id="deductions-content">
          <div class="section-header">
            <h3>DEDUCTIONS SECTION</h3>
            <div class="section-actions">
              <button id="add-deductions-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="deductions-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Employers Contribution Section -->
        <div class="dictionary-tab-content" id="employers-contribution-content">
          <div class="section-header">
            <h3>EMPLOYERS CONTRIBUTION SECTION</h3>
            <div class="section-actions">
              <button id="add-employers-contribution-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="employers-contribution-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Loans Section -->
        <div class="dictionary-tab-content" id="loans-content">
          <div class="section-header">
            <h3>LOANS SECTION</h3>
            <div class="section-actions">
              <button id="add-loans-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="loans-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Bank Details Section -->
        <div class="dictionary-tab-content" id="bank-details-content">
          <div class="section-header">
            <h3>EMPLOYEE BANK DETAILS SECTION</h3>
            <div class="section-actions">
              <button id="add-bank-details-item-btn" class="add-item-button">
                <i class="fas fa-plus"></i> Add Item
              </button>
            </div>
          </div>

          <div class="dictionary-items-container">
            <table class="dictionary-items-table">
              <thead>
                <tr>
                  <th>LINE ITEM</th>
                  <th>FORMAT</th>
                  <th>VALUE FORMAT</th>
                  <th>INCLUDE IN REPORT</th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody id="bank-details-items">
                <!-- Items will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>

        <!-- Add Section Tab Content -->
        <div class="dictionary-tab-content" id="add-section-content">
          <div class="section-header">
            <h3>Add New Section</h3>
          </div>

          <div class="add-section-form">
            <div class="form-group">
              <label for="new-section-name">Section Name:</label>
              <input type="text" id="new-section-name" placeholder="e.g., ADDITIONAL INFORMATION">
            </div>
            <button id="create-section-btn" class="primary-button">Create Section</button>
          </div>
        </div>

        <!-- Item Edit Modal -->
        <div id="item-edit-modal" class="modal">
          <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h3 id="edit-modal-title">Edit Item</h3>

            <div class="form-group">
              <label for="edit-item-name">Line Item Name:</label>
              <input type="text" id="edit-item-name">
            </div>

            <div class="form-group">
              <label for="edit-item-format">Format:</label>
              <input type="text" id="edit-item-format" placeholder="e.g., [UC], [TC][.]">
              <div class="format-helper">
                <button id="format-helper-btn" class="small-button">Format Help</button>
                <div id="format-helper-content" class="format-helper-content hidden">
                  <p><strong>Capitalization:</strong> [UC] = ALL CAPS, [TC] = Title Case, [LC] = lowercase</p>
                  <p><strong>Special Characters:</strong> [.] = period, [-] = hyphen, [_] = underscore, [/] = slash, [#] = numbers</p>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label for="edit-item-value-format">Value Format:</label>
              <select id="edit-item-value-format">
                <option value="Section Header">Section Header</option>
                <option value="Column Header">Column Header</option>
                <option value="Text">Text</option>
                <option value="Numeric">Numeric</option>
                <option value="Numeric with decimal places">Numeric with decimal places</option>
                <option value="Alphanumeric">Alphanumeric</option>
                <option value="Alphanumeric with hyphens">Alphanumeric with hyphens</option>
                <option value="Date">Date</option>
              </select>
            </div>

            <div class="form-group">
              <label for="edit-item-standardized-name">Standardized Name:</label>
              <input type="text" id="edit-item-standardized-name" placeholder="e.g., BASIC SALARY">
              <div class="helper-text">Leave empty to use the line item name as the standardized name.</div>
            </div>

            <div class="form-group">
              <label for="edit-item-variations">Variations (comma-separated):</label>
              <input type="text" id="edit-item-variations" placeholder="e.g., BASIC, BASE SALARY, BASE PAY">
              <div class="helper-text">Alternative names that should be standardized to this item.</div>
            </div>

            <div class="form-group checkbox-group">
              <input type="checkbox" id="edit-item-include-in-report">
              <label for="edit-item-include-in-report">Include in Report</label>
            </div>

            <div class="modal-actions">
              <button id="save-item-btn" class="primary-button">Save</button>
              <button id="cancel-edit-btn" class="secondary-button">Cancel</button>
            </div>
          </div>
        </div>

        <div class="action-container">
          <button id="save-dictionary-btn" class="primary-button">Save All Changes</button>
        </div>
      </div>
    </main>

    <footer class="app-footer">
      <div class="footer-left">
        <p>TEMPLAR SYSTEMS Payroll Auditor</p>
      </div>
      <div class="footer-right">
        <p>CREATED BY: SAMUEL ASIEDU</p>
        <p>© 2025 All Rights Reserved</p>
      </div>
    </footer>
  </div>

  <script src="progress-updater.js"></script>
  <script src="renderer.js"></script>
  <script src="dictionary_manager.js"></script>
  <script src="data_builder.js"></script>
  <script src="bank_adviser.js"></script>
</body>
</html>
